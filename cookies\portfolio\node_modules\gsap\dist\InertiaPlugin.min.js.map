{"version": 3, "file": "InertiaPlugin.min.js", "sources": ["../src/utils/VelocityTracker.js", "../src/InertiaPlugin.js"], "sourcesContent": ["/*!\n * VelocityTracker: 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _toArray, _getUnit, _first, _ticker, _time1, _time2, _getCache,\n\t_getGSAP = () => gsap || typeof(window) !== \"undefined\" && (gsap = window.gsap),\n\t_lookup = {},\n\t_round = value => Math.round(value * 10000) / 10000,\n\t_getID = target => _getCache(target).id,\n\t_getByTarget = target => _lookup[_getID(typeof(target) === \"string\" ? _toArray(target)[0] : target)],\n\t_onTick = (time) => {\n\t\tlet pt = _first,\n\t\t\tval;\n\t\t//if the frame rate is too high, we won't be able to track the velocity as well, so only update the values about 20 times per second\n\t\tif (time - _time1 >= 0.05) {\n\t\t\t_time2 = _time1;\n\t\t\t_time1 = time;\n\t\t\twhile (pt) {\n\t\t\t\tval = pt.g(pt.t, pt.p);\n\t\t\t\tif (val !== pt.v1 || time - pt.t1 > 0.2) { //use a threshold of 0.2 seconds for zeroing-out velocity. If we only use 0.05 and things update slightly slower, like some Android devices dispatch \"touchmove\" events sluggishly so 2 or 3 ticks of the gsap.ticker may elapse inbetween, thus it may appear like the object is not moving but it actually is but it's not updating as frequently. A threshold of 0.2 seconds seems to be a good balance. We want to update things frequently (0.05 seconds) when they're moving so that we can respond to fast motions accurately, but we want to be more resistant to go back to a zero velocity.\n\t\t\t\t\tpt.v2 = pt.v1;\n\t\t\t\t\tpt.v1 = val;\n\t\t\t\t\tpt.t2 = pt.t1;\n\t\t\t\t\tpt.t1 = time;\n\t\t\t\t}\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t}\n\t},\n\t_types = {deg: 360, rad: Math.PI * 2},\n\n\t_initCore = () => {\n\t\tgsap = _getGSAP();\n\t\tif (gsap) {\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_getUnit = gsap.utils.getUnit;\n\t\t\t_getCache = gsap.core.getCache;\n\t\t\t_ticker = gsap.ticker;\n\t\t\t_coreInitted = 1;\n\t\t}\n\t};\n\nclass PropTracker {\n\n\tconstructor(target, property, type, next) {\n\t\tthis.t = target;\n\t\tthis.p = property;\n\t\tthis.g = target._gsap.get;\n\t\tthis.rCap = _types[type || _getUnit(this.g(target, property))]; //rotational cap (for degrees, \"deg\", it's 360 and for radians, \"rad\", it's Math.PI * 2)\n\t\tthis.v1 = this.v2 = 0;\n\t\tthis.t1 = this.t2 = _ticker.time;\n\t\tif (next) {\n\t\t\tthis._next = next;\n\t\t\tnext._prev = this;\n\t\t}\n\t}\n\n}\n\nexport class VelocityTracker {\n\n\tconstructor(target, property) {\n\t\tif (!_coreInitted) {\n\t\t\t_initCore();\n\t\t}\n\t\tthis.target = _toArray(target)[0];\n\t\t_lookup[_getID(this.target)] = this;\n\t\tthis._props = {};\n\t\tproperty && this.add(property);\n\t}\n\n\tstatic register(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t}\n\n\tget(property, skipRecentTick) {\n\t\tlet pt = this._props[property] || console.warn(\"Not tracking \" + property + \" velocity.\"),\n\t\t\tval, dif, rotationCap;\n\t\tval = parseFloat(skipRecentTick ? pt.v1 : pt.g(pt.t, pt.p));\n\t\tdif = (val - parseFloat(pt.v2));\n\t\trotationCap = pt.rCap;\n\t\tif (rotationCap) { //rotational values need special interpretation so that if, for example, they go from 179 to -178 degrees it is interpreted as a change of 3 instead of -357.\n\t\t\tdif = dif % rotationCap;\n\t\t\tif (dif !== dif % (rotationCap / 2)) {\n\t\t\t\tdif = (dif < 0) ? dif + rotationCap : dif - rotationCap;\n\t\t\t}\n\t\t}\n\t\treturn _round(dif / ((skipRecentTick ? pt.t1 : _ticker.time) - pt.t2));\n\t}\n\n\tgetAll() {\n\t\tlet result = {},\n\t\t\tprops = this._props,\n\t\t\tp;\n\t\tfor (p in props) {\n\t\t\tresult[p] = this.get(p);\n\t\t}\n\t\treturn result;\n\t}\n\n\tisTracking(property) {\n\t\treturn (property in this._props);\n\t}\n\n\tadd(property, type) {\n\t\tif (!(property in this._props)) {\n\t\t\tif (!_first) {\n\t\t\t\t_ticker.add(_onTick);\n\t\t\t\t_time1 = _time2 = _ticker.time;\n\t\t\t}\n\t\t\t_first = this._props[property] = new PropTracker(this.target, property, type, _first);\n\t\t}\n\t}\n\n\tremove(property) {\n\t\tlet pt = this._props[property],\n\t\t\tprev, next;\n\t\tif (pt) {\n\t\t\tprev = pt._prev;\n\t\t\tnext = pt._next;\n\t\t\tif (prev) {\n\t\t\t\tprev._next = next;\n\t\t\t}\n\t\t\tif (next) {\n\t\t\t\tnext._prev = prev;\n\t\t\t} else if (_first === pt) {\n\t\t\t\t_ticker.remove(_onTick);\n\t\t\t\t_first = 0;\n\t\t\t}\n\t\t\tdelete this._props[property];\n\t\t}\n\t}\n\n\tkill(shallow) {\n\t\tfor (let p in this._props) {\n\t\t\tthis.remove(p);\n\t\t}\n\t\tif (!shallow) {\n\t\t\tdelete _lookup[_getID(this.target)];\n\t\t}\n\t}\n\n\tstatic track(targets, properties, types) {\n\t\tif (!_coreInitted) {\n\t\t\t_initCore();\n\t\t}\n\t\tlet result = [],\n\t\t\ttargs = _toArray(targets),\n\t\t\ta = properties.split(\",\"),\n\t\t\tt = (types || \"\").split(\",\"),\n\t\t\ti = targs.length,\n\t\t\ttracker, j;\n\t\twhile (i--) {\n\t\t\ttracker = _getByTarget(targs[i]) || new VelocityTracker(targs[i]);\n\t\t\tj = a.length;\n\t\t\twhile (j--) {\n\t\t\t\ttracker.add(a[j], t[j] || t[0]);\n\t\t\t}\n\t\t\tresult.push(tracker);\n\t\t}\n\t\treturn result;\n\t}\n\n\tstatic untrack(targets, properties) {\n\t\tlet props = (properties || \"\").split(\",\");\n\t\t_toArray(targets).forEach(target => {\n\t\t\tlet tracker = _getByTarget(target);\n\t\t\tif (tracker) {\n\t\t\t\tif (!props.length) {\n\t\t\t\t\ttracker.kill(1);\n\t\t\t\t} else {\n\t\t\t\t\tprops.forEach(p => tracker.remove(p));\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\tstatic isTracking(target, property) {\n\t\tlet tracker = _getByTarget(target);\n\t\treturn tracker && tracker.isTracking(property);\n\t}\n\n\tstatic getVelocity(target, property) {\n\t\tlet tracker = _getByTarget(target);\n\t\treturn (!tracker || !tracker.isTracking(property)) ? console.warn(\"Not tracking velocity of \" + property) : tracker.get(property);\n\t}\n}\n\nVelocityTracker.getByTarget = _getByTarget;\n\n\n_getGSAP() && gsap.registerPlugin(VelocityTracker);\n\nexport { VelocityTracker as default };", "/*!\n * InertiaPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { VelocityTracker } from \"./utils/VelocityTracker.js\";\n\nlet gsap, _coreInitted, _parseEase, _toArray, _power3, _config, _getUnit, PropTween, _getCache,\t_checkPointRatio, _clamp, _processingVars, _getStyleSaver, _reverting,\n\t_getTracker = VelocityTracker.getByTarget,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isString = value => typeof(value) === \"string\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isObject = value => typeof(value) === \"object\",\n\t_isFunction = value => typeof(value) === \"function\",\n\t_bonusValidated = 1, //<name>InertiaPlugin</name>\n\t_isArray = Array.isArray,\n\t_emptyFunc = p => p,\n\t_bigNum = 1e10,\n\t_tinyNum = 1 / _bigNum,\n\t_checkPoint = 0.05,\n\t_round = value => Math.round(value * 10000) / 10000,\n\t_extend = (obj, defaults, exclude) => {\n\t\tfor (let p in defaults) {\n\t\t\tif (!(p in obj) && p !== exclude) {\n\t\t\t\tobj[p] = defaults[p];\n\t\t\t}\n\t\t}\n\t\treturn obj;\n\t},\n\t_deepClone = obj => {\n\t\tlet copy = {},\n\t\t\tp, v;\n\t\tfor (p in obj) {\n\t\t\tcopy[p] = _isObject(v = obj[p]) && !_isArray(v) ? _deepClone(v) : v;\n\t\t}\n\t\treturn copy;\n\t},\n\t_getClosest = (n, values, max, min, radius) => {\n\t\tlet i = values.length,\n\t\t\tclosest = 0,\n\t\t\tabsDif = _bigNum,\n\t\t\tval, dif, p, dist;\n\t\tif (_isObject(n)) {\n\t\t\twhile (i--) {\n\t\t\t\tval = values[i];\n\t\t\t\tdif = 0;\n\t\t\t\tfor (p in n) {\n\t\t\t\t\tdist = val[p] - n[p];\n\t\t\t\t\tdif += dist * dist;\n\t\t\t\t}\n\t\t\t\tif (dif < absDif) {\n\t\t\t\t\tclosest = i;\n\t\t\t\t\tabsDif = dif;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ((radius || _bigNum) < _bigNum && radius < Math.sqrt(absDif)) {\n\t\t\t\treturn n;\n\t\t\t}\n\t\t} else {\n\t\t\twhile (i--) {\n\t\t\t\tval = values[i];\n\t\t\t\tdif = val - n;\n\t\t\t\tif (dif < 0) {\n\t\t\t\t\tdif = -dif;\n\t\t\t\t}\n\t\t\t\tif (dif < absDif && val >= min && val <= max) {\n\t\t\t\t\tclosest = i;\n\t\t\t\t\tabsDif = dif;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn values[closest];\n\t},\n\t_parseEnd = (curProp, end, max, min, name, radius, velocity) => {\n\t\tif (curProp.end === \"auto\") {\n\t\t\treturn curProp;\n\t\t}\n\t\tlet endVar = curProp.end,\n\t\t\tadjustedEnd, p;\n\t\tmax = isNaN(max) ? _bigNum : max;\n\t\tmin = isNaN(min) ? -_bigNum : min;\n\t\tif (_isObject(end)) { //for objects, like {x, y} where they're linked and we must pass an object to the function or find the closest value in an array.\n\t\t\tadjustedEnd = end.calculated ? end : (_isFunction(endVar) ? endVar(end, velocity) : _getClosest(end, endVar, max, min, radius)) || end;\n\t\t\tif (!end.calculated) {\n\t\t\t\tfor (p in adjustedEnd) {\n\t\t\t\t\tend[p] = adjustedEnd[p];\n\t\t\t\t}\n\t\t\t\tend.calculated = true;\n\t\t\t}\n\t\t\tadjustedEnd = adjustedEnd[name];\n\t\t} else {\n\t\t\tadjustedEnd = _isFunction(endVar) ? endVar(end, velocity) : _isArray(endVar) ? _getClosest(end, endVar, max, min, radius) : parseFloat(endVar);\n\t\t}\n\t\tif (adjustedEnd > max) {\n\t\t\tadjustedEnd = max;\n\t\t} else if (adjustedEnd < min) {\n\t\t\tadjustedEnd = min;\n\t\t}\n\t\treturn {max: adjustedEnd, min: adjustedEnd, unitFactor: curProp.unitFactor};\n\t},\n\t_getNumOrDefault = (vars, property, defaultValue) => isNaN(vars[property]) ? defaultValue : +vars[property],\n\t_calculateChange = (velocity, duration) => (duration * _checkPoint * velocity) / _checkPointRatio,\n\t_calculateDuration = (start, end, velocity) => Math.abs( (end - start) * _checkPointRatio / velocity / _checkPoint ),\n\t_reservedProps = {resistance:1, checkpoint:1, preventOvershoot:1, linkedProps:1, radius:1, duration:1},\n\t_processLinkedProps = (target, vars, getVal, resistance) => {\n\t\tif (vars.linkedProps) { //when there are linkedProps (typically \"x,y\" where snapping has to factor in multiple properties, we must first populate an object with all of those end values, then feed it to the function that make any necessary alterations. So the point of this first loop is to simply build an object (like {x:100, y:204.5}) for feeding into that function which we'll do later in the \"real\" loop.\n\t\t\tlet linkedPropNames = vars.linkedProps.split(\",\"),\n\t\t\t\tlinkedProps = {},\n\t\t\t\ti, p, curProp, curVelocity, tracker, curDuration;\n\t\t\tfor (i = 0; i < linkedPropNames.length; i++) {\n\t\t\t\tp = linkedPropNames[i];\n\t\t\t\tcurProp = vars[p];\n\t\t\t\tif (curProp) {\n\t\t\t\t\tif (_isNumber(curProp.velocity)) {\n\t\t\t\t\t\tcurVelocity = curProp.velocity;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttracker = tracker || _getTracker(target);\n\t\t\t\t\t\tcurVelocity =  (tracker && tracker.isTracking(p)) ? tracker.get(p) : 0;\n\t\t\t\t\t}\n\t\t\t\t\tcurDuration = Math.abs(curVelocity / _getNumOrDefault(curProp, \"resistance\", resistance));\n\t\t\t\t\tlinkedProps[p] = parseFloat(getVal(target, p)) + _calculateChange(curVelocity, curDuration);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn linkedProps;\n\t\t}\n\t},\n\t_calculateTweenDuration = (target, vars, maxDuration = 10, minDuration = 0.2, overshootTolerance = 1, recordEnd = 0) => {\n\t\t_isString(target) && (target = _toArray(target)[0]);\n\t\tif (!target) {\n\t\t\treturn 0;\n\t\t}\n\t\tlet duration = 0,\n\t\t\tclippedDuration = _bigNum,\n\t\t\tinertiaVars = vars.inertia || vars,\n\t\t\tgetVal = _getCache(target).get,\n\t\t\tresistance = _getNumOrDefault(inertiaVars, \"resistance\", _config.resistance),\n\t\t\tp, curProp, curDuration, curVelocity, curVal, end, curClippedDuration, tracker, unitFactor, linkedProps;\n\n\t\t//when there are linkedProps (typically \"x,y\" where snapping has to factor in multiple properties, we must first populate an object with all of those end values, then feed it to the function that make any necessary alterations. So the point of this first loop is to simply build an object (like {x:100, y:204.5}) for feeding into that function which we'll do later in the \"real\" loop.\n\t\tlinkedProps = _processLinkedProps(target, inertiaVars, getVal, resistance);\n\n\t\tfor (p in inertiaVars) {\n\n\t\t\tif (!_reservedProps[p]) {\n\t\t\t\tcurProp = inertiaVars[p];\n\t\t\t\tif (!_isObject(curProp)) {\n\t\t\t\t\ttracker = tracker || _getTracker(target);\n\t\t\t\t\tif (tracker && tracker.isTracking(p)) {\n\t\t\t\t\t\tcurProp = _isNumber(curProp) ? {velocity:curProp} : {velocity:tracker.get(p)}; //if we're tracking this property, we should use the tracking velocity and then use the numeric value that was passed in as the min and max so that it tweens exactly there.\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcurVelocity = +curProp || 0;\n\t\t\t\t\t\tcurDuration = Math.abs(curVelocity / resistance);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (_isObject(curProp)) {\n\n\t\t\t\t\tif (_isNumber(curProp.velocity)) {\n\t\t\t\t\t\tcurVelocity = curProp.velocity;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttracker = tracker || _getTracker(target);\n\t\t\t\t\t\tcurVelocity = (tracker && tracker.isTracking(p)) ? tracker.get(p) : 0;\n\t\t\t\t\t}\n\n\t\t\t\t\tcurDuration = _clamp(minDuration, maxDuration, Math.abs(curVelocity / _getNumOrDefault(curProp, \"resistance\", resistance)));\n\t\t\t\t\tcurVal = parseFloat(getVal(target, p)) || 0;\n\t\t\t\t\tend = curVal + _calculateChange(curVelocity, curDuration);\n\t\t\t\t\tif (\"end\" in curProp) {\n\t\t\t\t\t\tcurProp = _parseEnd(curProp, (linkedProps && p in linkedProps) ? linkedProps : end, curProp.max, curProp.min, p, inertiaVars.radius, curVelocity);\n\t\t\t\t\t\tif (recordEnd) {\n\t\t\t\t\t\t\t(_processingVars === vars) && (_processingVars = inertiaVars = _deepClone(vars));\n\t\t\t\t\t\t\tinertiaVars[p] = _extend(curProp, inertiaVars[p], \"end\");\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif ((\"max\" in curProp) && end > +curProp.max + _tinyNum) {\n\t\t\t\t\t\tunitFactor = curProp.unitFactor || _config.unitFactors[p] || 1; //some values are measured in special units like radians in which case our thresholds need to be adjusted accordingly.\n\t\t\t\t\t\t//if the value is already exceeding the max or the velocity is too low, the duration can end up being uncomfortably long but in most situations, users want the snapping to occur relatively quickly (0.75 seconds), so we implement a cap here to make things more intuitive. If the max and min match, it means we're animating to a particular value and we don't want to shorten the time unless the velocity is really slow. Example: a rotation where the start and natural end value are less than the snapping spot, but the natural end is pretty close to the snap.\n\t\t\t\t\t\tcurClippedDuration = ((curVal > curProp.max && curProp.min !== curProp.max) || (curVelocity * unitFactor > -15 && curVelocity * unitFactor < 45)) ? (minDuration + (maxDuration - minDuration) * 0.1) : _calculateDuration(curVal, curProp.max, curVelocity);\n\t\t\t\t\t\tif (curClippedDuration + overshootTolerance < clippedDuration) {\n\t\t\t\t\t\t\tclippedDuration = curClippedDuration + overshootTolerance;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t} else if ((\"min\" in curProp) && end < +curProp.min - _tinyNum) {\n\t\t\t\t\t\tunitFactor = curProp.unitFactor || _config.unitFactors[p] || 1; //some values are measured in special units like radians in which case our thresholds need to be adjusted accordingly.\n\t\t\t\t\t\t//if the value is already exceeding the min or if the velocity is too low, the duration can end up being uncomfortably long but in most situations, users want the snapping to occur relatively quickly (0.75 seconds), so we implement a cap here to make things more intuitive.\n\t\t\t\t\t\tcurClippedDuration = ((curVal < curProp.min && curProp.min !== curProp.max) || (curVelocity * unitFactor > -45 && curVelocity * unitFactor < 15)) ? (minDuration + (maxDuration - minDuration) * 0.1) : _calculateDuration(curVal, curProp.min, curVelocity);\n\t\t\t\t\t\tif (curClippedDuration + overshootTolerance < clippedDuration) {\n\t\t\t\t\t\t\tclippedDuration = curClippedDuration + overshootTolerance;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t(curClippedDuration > duration) && (duration = curClippedDuration);\n\t\t\t\t}\n\n\t\t\t\t(curDuration > duration) && (duration = curDuration);\n\t\t\t}\n\t\t}\n\t\t(duration > clippedDuration) &&\t(duration = clippedDuration);\n\t\treturn (duration > maxDuration) ? maxDuration : (duration < minDuration) ? minDuration : duration;\n\t},\n\n\n\n\t_initCore = () => {\n\t\tgsap = _getGSAP();\n\t\tif (gsap) {\n\t\t\t_parseEase = gsap.parseEase;\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_getUnit = gsap.utils.getUnit;\n\t\t\t_getCache = gsap.core.getCache;\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_getStyleSaver = gsap.core.getStyleSaver;\n\t\t\t_reverting = gsap.core.reverting || function() {};\n\t\t\t_power3 = _parseEase(\"power3\");\n\t\t\t_checkPointRatio = _power3(0.05);\n\t\t\tPropTween = gsap.core.PropTween;\n\t\t\tgsap.config({resistance:100, unitFactors:{time: 1000, totalTime: 1000, progress: 1000, totalProgress: 1000}});\n\t\t\t_config = gsap.config();\n\t\t\tgsap.registerPlugin(VelocityTracker);\n\t\t\t_coreInitted = 1;\n\t\t}\n\t};\n\n\n\nexport const InertiaPlugin = {\n\tversion: \"3.13.0\",\n\tname: \"inertia\",\n\tregister(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t},\n\tinit(target, vars, tween, index, targets) {\n\t\t_coreInitted || _initCore();\n\t\tlet tracker = _getTracker(target);\n\t\tif (vars === \"auto\") {\n\t\t\tif (!tracker) {\n\t\t\t\tconsole.warn(\"No inertia tracking on \" + target + \". InertiaPlugin.track(target) first.\");\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvars = tracker.getAll();\n\t\t}\n\t\tthis.styles = _getStyleSaver && typeof(target.style) === \"object\" && _getStyleSaver(target);\n\t\tthis.target = target;\n\t\tthis.tween = tween;\n\t\t_processingVars = vars; // gets swapped inside _calculateTweenDuration() if there's a function-based value encountered (to avoid double-calling it)\n\t\tlet cache = target._gsap,\n\t\t\tgetVal = cache.get,\n\t\t\tdur = vars.duration,\n\t\t\tdurIsObj = _isObject(dur),\n\t\t\tpreventOvershoot = vars.preventOvershoot || (durIsObj && dur.overshoot === 0),\n\t\t\tresistance = _getNumOrDefault(vars, \"resistance\", _config.resistance),\n\t\t\tduration = _isNumber(dur) ? dur : _calculateTweenDuration(target, vars, (durIsObj && dur.max) || 10, (durIsObj && dur.min) || 0.2, (durIsObj && \"overshoot\" in dur) ? +dur.overshoot : preventOvershoot ? 0 : 1, true),\n\t\t\tp, curProp, curVal, unit, velocity, change1, end, change2, linkedProps;\n\t\tvars = _processingVars;\n\t\t_processingVars = 0;\n\t\t//when there are linkedProps (typically \"x,y\" where snapping has to factor in multiple properties, we must first populate an object with all of those end values, then feed it to the function that make any necessary alterations. So the point of this first loop is to simply build an object (like {x:100, y:204.5}) for feeding into that function which we'll do later in the \"real\" loop.\n\t\tlinkedProps = _processLinkedProps(target, vars, getVal, resistance);\n\n\t\tfor (p in vars) {\n\t\t\tif (!_reservedProps[p]) {\n\t\t\t\tcurProp = vars[p];\n\t\t\t\t_isFunction(curProp) && (curProp = curProp(index, target, targets));\n\t\t\t\tif (_isNumber(curProp)) {\n\t\t\t\t\tvelocity = curProp;\n\t\t\t\t} else if (_isObject(curProp) && !isNaN(curProp.velocity)) {\n\t\t\t\t\tvelocity = +curProp.velocity;\n\t\t\t\t} else {\n\t\t\t\t\tif (tracker && tracker.isTracking(p)) {\n\t\t\t\t\t\tvelocity = tracker.get(p);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn(\"ERROR: No velocity was defined for \" + target + \" property: \" + p);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tchange1 = _calculateChange(velocity, duration);\n\t\t\t\tchange2 = 0;\n\t\t\t\tcurVal = getVal(target, p);\n\t\t\t\tunit = _getUnit(curVal);\n\t\t\t\tcurVal = parseFloat(curVal);\n\t\t\t\tif (_isObject(curProp)) {\n\t\t\t\t\tend = curVal + change1;\n\t\t\t\t\tif (\"end\" in curProp) {\n\t\t\t\t\t\tcurProp = _parseEnd(curProp, (linkedProps && p in linkedProps) ? linkedProps : end, curProp.max, curProp.min, p, vars.radius, velocity);\n\t\t\t\t\t}\n\t\t\t\t\tif ((\"max\" in curProp) && +curProp.max < end) {\n\t\t\t\t\t\tif (preventOvershoot || curProp.preventOvershoot) {\n\t\t\t\t\t\t\tchange1 = curProp.max - curVal;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tchange2 = (curProp.max - curVal) - change1;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if ((\"min\" in curProp) && +curProp.min > end) {\n\t\t\t\t\t\tif (preventOvershoot || curProp.preventOvershoot) {\n\t\t\t\t\t\t\tchange1 = curProp.min - curVal;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tchange2 = (curProp.min - curVal) - change1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis._props.push(p);\n\t\t\t\tthis.styles && this.styles.save(p);\n\t\t\t\tthis._pt = new PropTween(this._pt, target, p, curVal, 0, _emptyFunc, 0, cache.set(target, p, this));\n\t\t\t\tthis._pt.u = unit || 0;\n\t\t\t\tthis._pt.c1 = change1;\n\t\t\t\tthis._pt.c2 = change2;\n\t\t\t}\n\t\t}\n\t\ttween.duration(duration);\n\t\treturn _bonusValidated;\n\t},\n\trender(ratio, data) {\n\t\tlet pt = data._pt;\n\t\tratio = _power3(data.tween._time / data.tween._dur);\n\t\tif (ratio || !_reverting()) {\n\t\t\twhile (pt) {\n\t\t\t\tpt.set(pt.t, pt.p, _round(pt.s + pt.c1 * ratio + pt.c2 * ratio * ratio) + pt.u, pt.d, ratio);\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t} else {\n\t\t\tdata.styles.revert();\n\t\t}\n\t}\n};\n\n\n\"track,untrack,isTracking,getVelocity,getByTarget\".split(\",\").forEach(name => InertiaPlugin[name] = VelocityTracker[name]);\n\n_getGSAP() && gsap.registerPlugin(InertiaPlugin);\n\nexport { InertiaPlugin as default, VelocityTracker };"], "names": ["_getGSAP", "gsap", "window", "_getID", "target", "_getCache", "id", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>", "_lookup", "_toArray", "_onTick", "time", "val", "pt", "_first", "_time1", "g", "t", "p", "v1", "t1", "v2", "t2", "_next", "_initCore", "utils", "toArray", "_getUnit", "getUnit", "core", "getCache", "_ticker", "ticker", "_coreInitted", "PropTracker", "property", "type", "next", "_gsap", "get", "rCap", "_types", "this", "_prev", "deg", "rad", "Math", "PI", "VelocityTracker", "_props", "add", "register", "skipRecentTick", "dif", "rotationCap", "console", "warn", "parseFloat", "_round", "value", "round", "getAll", "result", "props", "isTracking", "remove", "prev", "kill", "shallow", "track", "targets", "properties", "types", "tracker", "j", "targs", "a", "split", "i", "length", "push", "untrack", "for<PERSON>ach", "getVelocity", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerPlugin", "_isNumber", "_isObject", "_isFunction", "_emptyFunc", "_extend", "obj", "defaults", "exclude", "_deepClone", "v", "copy", "_isArray", "_getClosest", "n", "values", "max", "min", "radius", "dist", "closest", "absDif", "_bigNum", "sqrt", "_parseEnd", "curProp", "end", "name", "velocity", "adjustedEnd", "endVar", "isNaN", "calculated", "unitFactor", "_getNumOrDefault", "vars", "defaultValue", "_calculateChange", "duration", "_checkPointRatio", "_calculateDuration", "start", "abs", "_processLinkedProps", "getVal", "resistance", "linkedProps", "curVelocity", "curDuration", "linkedPropNames", "_getTracker", "_parseEase", "parseEase", "_clamp", "clamp", "_getStyleSaver", "getStyleSaver", "_reverting", "reverting", "_power3", "PropTween", "config", "unitFactors", "totalTime", "progress", "totalProgress", "_config", "_processingVars", "Array", "isArray", "_reservedProps", "checkpoint", "preventOvershoot", "InertiaPlugin", "version", "init", "tween", "index", "styles", "style", "curVal", "unit", "change1", "change2", "cache", "dur", "durIsObj", "overshoot", "_calculateTweenDuration", "maxDuration", "minDuration", "overshootTolerance", "recordEnd", "_isString", "curClippedDuration", "clippedDuration", "inertiaVars", "inertia", "save", "_pt", "set", "u", "c1", "c2", "render", "ratio", "data", "_time", "_dur", "s", "d", "revert"], "mappings": ";;;;;;;;;6MAWY,SAAXA,WAAiBC,GAA2B,oBAAZC,SAA4BD,EAAOC,OAAOD,MAGjE,SAATE,EAASC,UAAUC,EAAUD,GAAQE,GACtB,SAAfC,EAAeH,UAAUI,EAAQL,EAA0B,iBAAZC,EAAuBK,EAASL,GAAQ,GAAKA,IAClF,SAAVM,EAAWC,OAETC,EADGC,EAAKC,KAGY,KAAjBH,EAAOI,MAEVA,EAASJ,EACFE,KACND,EAAMC,EAAGG,EAAEH,EAAGI,EAAGJ,EAAGK,MACRL,EAAGM,IAAqB,GAAfR,EAAOE,EAAGO,MAC9BP,EAAGQ,GAAKR,EAAGM,GACXN,EAAGM,GAAKP,EACRC,EAAGS,GAAKT,EAAGO,GACXP,EAAGO,GAAKT,GAETE,EAAKA,EAAGU,MAMC,SAAZC,KACCvB,EAAOD,OAENS,EAAWR,EAAKwB,MAAMC,QACtBC,EAAW1B,EAAKwB,MAAMG,QACtBvB,EAAYJ,EAAK4B,KAAKC,SACtBC,EAAU9B,EAAK+B,OACfC,EAAe,GAMjB,SAFKC,EAEO9B,EAAQ+B,EAAUC,EAAMC,QAC9BpB,EAAIb,OACJc,EAAIiB,OACJnB,EAAIZ,EAAOkC,MAAMC,SACjBC,KAAOC,EAAOL,GAAQT,EAASe,KAAK1B,EAAEZ,EAAQ+B,UAC9ChB,GAAKuB,KAAKrB,GAAK,OACfD,GAAKsB,KAAKpB,GAAKS,EAAQpB,KACxB0B,UACEd,MAAQc,GACRM,MAAQD,MAjDhB,IAAIzC,EAAMgC,EAAcxB,EAAUkB,EAAUb,EAAQiB,EAAShB,EAAgBV,EAE5EG,EAAU,GAuBViC,EAAS,CAACG,IAAK,IAAKC,IAAe,EAAVC,KAAKC,IA8BlBC,sCAEA5C,EAAQ+B,GACdF,GACJT,SAEIpB,OAASK,EAASL,GAAQ,IAC/BI,EAAQL,EAAOuC,KAAKtC,SAAWsC,MAC1BO,OAAS,GACdd,GAAYO,KAAKQ,IAAIf,mBAGfgB,SAAP,kBAAgBtB,GACf5B,EAAO4B,EACPL,8CAGDe,IAAA,aAAIJ,EAAUiB,OAEPC,EAAKC,EADPzC,EAAK6B,KAAKO,OAAOd,IAAaoB,QAAQC,KAAK,gBAAkBrB,EAAW,qBAG5EkB,EADMI,WAAWL,EAAiBvC,EAAGM,GAAKN,EAAGG,EAAEH,EAAGI,EAAGJ,EAAGK,IAC3CuC,WAAW5C,EAAGQ,KAC3BiC,EAAczC,EAAG2B,QAEhBa,GAAYC,KACAD,GAAOC,EAAc,KAChCD,EAAOA,EAAM,EAAKA,EAAMC,EAAcD,EAAMC,GA9EtC,SAATI,OAASC,UAASb,KAAKc,MAAc,IAARD,GAAiB,IAiFtCD,CAAOL,IAAQD,EAAiBvC,EAAGO,GAAKW,EAAQpB,MAAQE,EAAGS,QAGnEuC,OAAA,sBAGE3C,EAFG4C,EAAS,GACZC,EAAQrB,KAAKO,WAET/B,KAAK6C,EACTD,EAAO5C,GAAKwB,KAAKH,IAAIrB,UAEf4C,KAGRE,WAAA,oBAAW7B,UACFA,KAAYO,KAAKO,UAG1BC,IAAA,aAAIf,EAAUC,GACPD,KAAYO,KAAKO,SACjBnC,IACJiB,EAAQmB,IAAIxC,GACZK,EAAkBgB,EAAQpB,MAE3BG,EAAS4B,KAAKO,OAAOd,GAAY,IAAID,EAAYQ,KAAKtC,OAAQ+B,EAAUC,EAAMtB,OAIhFmD,OAAA,gBAAO9B,OAEL+B,EAAM7B,EADHxB,EAAK6B,KAAKO,OAAOd,GAEjBtB,IACHqD,EAAOrD,EAAG8B,MACVN,EAAOxB,EAAGU,MACN2C,IACHA,EAAK3C,MAAQc,GAEVA,EACHA,EAAKM,MAAQuB,EACHpD,IAAWD,IACrBkB,EAAQkC,OAAOvD,GACfI,EAAS,UAEH4B,KAAKO,OAAOd,OAIrBgC,KAAA,cAAKC,OACC,IAAIlD,KAAKwB,KAAKO,YACbgB,OAAO/C,GAERkD,UACG5D,EAAQL,EAAOuC,KAAKtC,0BAItBiE,MAAP,eAAaC,EAASC,EAAYC,GAC5BvC,GACJT,YAOAiD,EAASC,EALNZ,EAAS,GACZa,EAAQlE,EAAS6D,GACjBM,EAAIL,EAAWM,MAAM,KACrB5D,GAAKuD,GAAS,IAAIK,MAAM,KACxBC,EAAIH,EAAMI,OAEJD,KAAK,KACXL,EAAUlE,EAAaoE,EAAMG,KAAO,IAAI9B,gBAAgB2B,EAAMG,IAC9DJ,EAAIE,EAAEG,OACCL,KACND,EAAQvB,IAAI0B,EAAEF,GAAIzD,EAAEyD,IAAMzD,EAAE,IAE7B6C,EAAOkB,KAAKP,UAENX,mBAGDmB,QAAP,iBAAeX,EAASC,OACnBR,GAASQ,GAAc,IAAIM,MAAM,KACrCpE,EAAS6D,GAASY,QAAQ,SAAA9E,OACrBqE,EAAUlE,EAAaH,GACvBqE,IACEV,EAAMgB,OAGVhB,EAAMmB,QAAQ,SAAAhE,UAAKuD,EAAQR,OAAO/C,KAFlCuD,EAAQN,KAAK,uBAQVH,WAAP,oBAAkB5D,EAAQ+B,OACrBsC,EAAUlE,EAAaH,UACpBqE,GAAWA,EAAQT,WAAW7B,oBAG/BgD,YAAP,qBAAmB/E,EAAQ+B,OACtBsC,EAAUlE,EAAaH,UAClBqE,GAAYA,EAAQT,WAAW7B,GAAoEsC,EAAQlC,IAAIJ,GAAnEoB,QAAQC,KAAK,4BAA8BrB,uBAIlGa,EAAgBoC,YAAc7E,EAG9BP,KAAcC,EAAKoF,eAAerC,GCxLtB,SAAXhD,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKoF,gBAAkBpF,EAE/F,SAAZqF,EAAY3B,SAA2B,iBAAXA,EAChB,SAAZ4B,EAAY5B,SAA2B,iBAAXA,EACd,SAAd6B,EAAc7B,SAA2B,mBAAXA,EAGjB,SAAb8B,EAAavE,UAAKA,EAIT,SAATwC,EAASC,UAASb,KAAKc,MAAc,IAARD,GAAiB,IACpC,SAAV+B,EAAWC,EAAKC,EAAUC,OACpB,IAAI3E,KAAK0E,EACP1E,KAAKyE,GAAQzE,IAAM2E,IACxBF,EAAIzE,GAAK0E,EAAS1E,WAGbyE,EAEK,SAAbG,EAAaH,OAEXzE,EAAG6E,EADAC,EAAO,OAEN9E,KAAKyE,EACTK,EAAK9E,GAAKqE,EAAUQ,EAAIJ,EAAIzE,MAAQ+E,EAASF,GAAKD,EAAWC,GAAKA,SAE5DC,EAEM,SAAdE,EAAeC,EAAGC,EAAQC,EAAKC,EAAKC,OAIlC3F,EAAKyC,EAAKnC,EAAGsF,EAHV1B,EAAIsB,EAAOrB,OACd0B,EAAU,EACVC,EAASC,KAENpB,EAAUY,GAAI,MACVrB,KAAK,KAGN5D,KAFLN,EAAMwF,EAAOtB,GACbzB,EAAM,EACI8C,EAET9C,IADAmD,EAAO5F,EAAIM,GAAKiF,EAAEjF,IACJsF,EAEXnD,EAAMqD,IACTD,EAAU3B,EACV4B,EAASrD,OAGNkD,GAAUI,GAAWA,GAAWJ,EAASzD,KAAK8D,KAAKF,UAChDP,YAGDrB,MAENzB,GADAzC,EAAMwF,EAAOtB,IACDqB,GACF,IACT9C,GAAOA,GAEJA,EAAMqD,GAAiBJ,GAAP1F,GAAcA,GAAOyF,IACxCI,EAAU3B,EACV4B,EAASrD,UAIL+C,EAAOK,GAEH,SAAZI,EAAaC,EAASC,EAAKV,EAAKC,EAAKU,EAAMT,EAAQU,MAC9B,SAAhBH,EAAQC,WACJD,MAGPI,EAAahG,EADViG,EAASL,EAAQC,OAErBV,EAAMe,MAAMf,GAAOM,EAAUN,EAC7BC,EAAMc,MAAMd,IAAQK,EAAUL,EAC1Bf,EAAUwB,GAAM,IACnBG,EAAcH,EAAIM,WAAaN,GAAOvB,EAAY2B,GAAUA,EAAOJ,EAAKE,GAAYf,EAAYa,EAAKI,EAAQd,EAAKC,EAAKC,KAAYQ,GAC9HA,EAAIM,WAAY,KACfnG,KAAKgG,EACTH,EAAI7F,GAAKgG,EAAYhG,GAEtB6F,EAAIM,YAAa,EAElBH,EAAcA,EAAYF,QAE1BE,EAAc1B,EAAY2B,GAAUA,EAAOJ,EAAKE,GAAYhB,EAASkB,GAAUjB,EAAYa,EAAKI,EAAQd,EAAKC,EAAKC,GAAU9C,WAAW0D,UAEtHd,EAAda,EACHA,EAAcb,EACJa,EAAcZ,IACxBY,EAAcZ,GAER,CAACD,IAAKa,EAAaZ,IAAKY,EAAaI,WAAYR,EAAQQ,YAE9C,SAAnBC,EAAoBC,EAAMrF,EAAUsF,UAAiBL,MAAMI,EAAKrF,IAAasF,GAAgBD,EAAKrF,GAC/E,SAAnBuF,GAAoBT,EAAUU,SAlFhB,IAkF8BA,EAAyBV,EAAYW,EAC5D,SAArBC,GAAsBC,EAAOf,EAAKE,UAAanE,KAAKiF,KAAMhB,EAAMe,GAASF,EAAmBX,EAnF9E,KAqFQ,SAAtBe,GAAuB5H,EAAQoH,EAAMS,EAAQC,MACxCV,EAAKW,YAAa,KAGpBrD,EAAG5D,EAAG4F,EAASsB,EAAa3D,EAAS4D,EAFlCC,EAAkBd,EAAKW,YAAYtD,MAAM,KAC5CsD,EAAc,OAEVrD,EAAI,EAAGA,EAAIwD,EAAgBvD,OAAQD,KAEvCgC,EAAUU,EADVtG,EAAIoH,EAAgBxD,OAIlBsD,EADG9C,EAAUwB,EAAQG,UACPH,EAAQG,UAEtBxC,EAAUA,GAAW8D,EAAYnI,KACNqE,EAAQT,WAAW9C,GAAMuD,EAAQlC,IAAIrB,GAAK,EAEtEmH,EAAcvF,KAAKiF,IAAIK,EAAcb,EAAiBT,EAAS,aAAcoB,IAC7EC,EAAYjH,GAAKuC,WAAWwE,EAAO7H,EAAQc,IAAMwG,GAAiBU,EAAaC,WAG1EF,GA+EG,SAAZ3G,MACCvB,EAAOD,OAENwI,EAAavI,EAAKwI,UAClBhI,EAAWR,EAAKwB,MAAMC,QACtBC,EAAW1B,EAAKwB,MAAMG,QACtBvB,EAAYJ,EAAK4B,KAAKC,SACtB4G,EAASzI,EAAKwB,MAAMkH,MACpBC,EAAiB3I,EAAK4B,KAAKgH,cAC3BC,EAAa7I,EAAK4B,KAAKkH,WAAa,aACpCC,EAAUR,EAAW,UACrBZ,EAAmBoB,EAAQ,KAC3BC,EAAYhJ,EAAK4B,KAAKoH,UACtBhJ,EAAKiJ,OAAO,CAAChB,WAAW,IAAKiB,YAAY,CAACxI,KAAM,IAAMyI,UAAW,IAAMC,SAAU,IAAMC,cAAe,OACtGC,EAAUtJ,EAAKiJ,SACfjJ,EAAKoF,eAAerC,GACpBf,EAAe,GAnNlB,IAAIhC,EAAMgC,EAAcuG,EAAY/H,EAAUuI,EAASO,EAAS5H,EAAUsH,EAAW5I,EAAWuH,EAAkBc,EAAQc,EAAiBZ,EAAgBE,EAC1JP,EAAcvF,EAAgBoC,YAO9Ba,EAAWwD,MAAMC,QAEjB/C,EAAU,KAsFVgD,EAAiB,CAACzB,WAAW,EAAG0B,WAAW,EAAGC,iBAAiB,EAAG1B,YAAY,EAAG5B,OAAO,EAAGoB,SAAS,GAyHxFmC,EAAgB,CAC5BC,QAAS,SACT/C,KAAM,UACN7D,2BAAStB,GACR5B,EAAO4B,EACPL,MAEDwI,mBAAK5J,EAAQoH,EAAMyC,EAAOC,EAAO5F,GAChCrC,GAAgBT,SACZiD,EAAU8D,EAAYnI,MACb,SAAToH,EAAiB,KACf/C,cACJlB,QAAQC,KAAK,0BAA4BpD,EAAS,wCAGnDoH,EAAO/C,EAAQZ,cAEXsG,OAASvB,GAA2C,iBAAlBxI,EAAOgK,OAAuBxB,EAAexI,QAC/EA,OAASA,OACT6J,MAAQA,EACbT,EAAkBhC,MAQjBtG,EAAG4F,EAASuD,EAAQC,EAAMrD,EAAUsD,EAASxD,EAAKyD,EAASrC,EAPxDsC,EAAQrK,EAAOkC,MAClB2F,EAASwC,EAAMlI,IACfmI,EAAMlD,EAAKG,SACXgD,EAAWpF,EAAUmF,GACrBb,EAAmBrC,EAAKqC,kBAAqBc,GAA8B,IAAlBD,EAAIE,UAC7D1C,EAAaX,EAAiBC,EAAM,aAAc+B,EAAQrB,YAC1DP,EAAWrC,EAAUoF,GAAOA,EA7HJ,SAA1BG,wBAA2BzK,EAAQoH,EAAMsD,EAAkBC,EAAmBC,EAAwBC,eAA7DH,IAAAA,EAAc,aAAIC,IAAAA,EAAc,aAAKC,IAAAA,EAAqB,YAAGC,IAAAA,EAAY,GApHtG,SAAZC,UAAYvH,SAA2B,iBAAXA,EAqH3BuH,CAAU9K,KAAYA,EAASK,EAASL,GAAQ,KAC3CA,SACG,MAOPc,EAAG4F,EAASuB,EAAaD,EAAaiC,EAAQtD,EAAKoE,EAAoB1G,EAAS6C,EAAYa,EALzFR,EAAW,EACdyD,EAAkBzE,EAClB0E,EAAc7D,EAAK8D,SAAW9D,EAC9BS,EAAS5H,EAAUD,GAAQmC,IAC3B2F,EAAaX,EAAiB8D,EAAa,aAAc9B,EAAQrB,gBAM7DhH,KAFLiH,EAAcH,GAAoB5H,EAAQiL,EAAapD,EAAQC,GAErDmD,EAEJ1B,EAAezI,KACnB4F,EAAUuE,EAAYnK,GACjBqE,EAAUuB,MACdrC,EAAUA,GAAW8D,EAAYnI,KAClBqE,EAAQT,WAAW9C,GACjC4F,EAAUxB,EAAUwB,GAAW,CAACG,SAASH,GAAW,CAACG,SAASxC,EAAQlC,IAAIrB,KAE1EkH,GAAetB,GAAW,EAC1BuB,EAAcvF,KAAKiF,IAAIK,EAAcF,KAGnC3C,EAAUuB,KAGZsB,EADG9C,EAAUwB,EAAQG,UACPH,EAAQG,UAEtBxC,EAAUA,GAAW8D,EAAYnI,KACPqE,EAAQT,WAAW9C,GAAMuD,EAAQlC,IAAIrB,GAAK,EAGrEmH,EAAcK,EAAOqC,EAAaD,EAAahI,KAAKiF,IAAIK,EAAcb,EAAiBT,EAAS,aAAcoB,KAE9GnB,GADAsD,EAAS5G,WAAWwE,EAAO7H,EAAQc,KAAO,GAC3BwG,GAAiBU,EAAaC,GACzC,QAASvB,IACZA,EAAUD,EAAUC,EAAUqB,GAAejH,KAAKiH,EAAeA,EAAcpB,EAAKD,EAAQT,IAAKS,EAAQR,IAAKpF,EAAGmK,EAAY9E,OAAQ6B,GACjI6C,IACFzB,IAAoBhC,IAAUgC,EAAkB6B,EAAcvF,EAAW0B,IAC1E6D,EAAYnK,GAAKwE,EAAQoB,EAASuE,EAAYnK,GAAI,SAG/C,QAAS4F,GAAYC,GAAOD,EAAQT,IA3JlC,OA4JNiB,EAAaR,EAAQQ,YAAciC,EAAQJ,YAAYjI,IAAM,GAE7DiK,EAAuBd,EAASvD,EAAQT,KAAOS,EAAQR,MAAQQ,EAAQT,MAAqC,GAA5B+B,EAAcd,GAAoBc,EAAcd,EAAa,GAAQyD,EAA4C,IAA7BD,EAAcC,GAAsBlD,GAAmBwC,EAAQvD,EAAQT,IAAK+B,IACvN4C,EAAqBI,IAC7CA,EAAkBD,EAAqBH,IAG7B,QAASlE,GAAYC,EAAOD,EAAQR,IAnKzC,QAoKNgB,EAAaR,EAAQQ,YAAciC,EAAQJ,YAAYjI,IAAM,GAE7DiK,EAAuBd,EAASvD,EAAQR,KAAOQ,EAAQR,MAAQQ,EAAQT,MAAqC,GAA5B+B,EAAcd,GAAoBc,EAAcd,EAAa,GAAQyD,EAA4C,IAA7BD,EAAcC,GAAsBlD,GAAmBwC,EAAQvD,EAAQR,IAAK8B,IACvN4C,EAAqBI,IAC7CA,EAAkBD,EAAqBH,IAInBrD,EAArBwD,IAAmCxD,EAAWwD,IAGjCxD,EAAdU,IAA4BV,EAAWU,WAG9B+C,EAAXzD,IAAgCA,EAAWyD,GACzBN,EAAXnD,EAA0BmD,EAAenD,EAAWoD,EAAeA,EAAcpD,EAsDtDkD,CAAwBzK,EAAQoH,EAAOmD,GAAYD,EAAIrE,KAAQ,GAAKsE,GAAYD,EAAIpE,KAAQ,GAAMqE,GAAY,cAAeD,GAAQA,EAAIE,UAAYf,EAAmB,EAAI,GAAG,OAO7M3I,KALLsG,EAAOgC,EACPA,EAAkB,EAElBrB,EAAcH,GAAoB5H,EAAQoH,EAAMS,EAAQC,GAE9CV,EACJmC,EAAezI,KACnB4F,EAAUU,EAAKtG,GACfsE,EAAYsB,KAAaA,EAAUA,EAAQoD,EAAO9J,EAAQkE,IACtDgB,EAAUwB,GACbG,EAAWH,EACDvB,EAAUuB,KAAaM,MAAMN,EAAQG,UAC/CA,GAAYH,EAAQG,SAEhBxC,GAAWA,EAAQT,WAAW9C,GACjC+F,EAAWxC,EAAQlC,IAAIrB,GAEvBqC,QAAQC,KAAK,sCAAwCpD,EAAS,cAAgBc,GAGhFqJ,EAAU7C,GAAiBT,EAAUU,GACrC6C,EAAU,EACVH,EAASpC,EAAO7H,EAAQc,GACxBoJ,EAAO3I,EAAS0I,GAChBA,EAAS5G,WAAW4G,GAChB9E,EAAUuB,KACbC,EAAMsD,EAASE,EACX,QAASzD,IACZA,EAAUD,EAAUC,EAAUqB,GAAejH,KAAKiH,EAAeA,EAAcpB,EAAKD,EAAQT,IAAKS,EAAQR,IAAKpF,EAAGsG,EAAKjB,OAAQU,IAE1H,QAASH,IAAaA,EAAQT,IAAMU,EACpC8C,GAAoB/C,EAAQ+C,iBAC/BU,EAAUzD,EAAQT,IAAMgE,EAExBG,EAAW1D,EAAQT,IAAMgE,EAAUE,EAEzB,QAASzD,IAAaA,EAAQR,IAAMS,IAC3C8C,GAAoB/C,EAAQ+C,iBAC/BU,EAAUzD,EAAQR,IAAM+D,EAExBG,EAAW1D,EAAQR,IAAM+D,EAAUE,SAIjCtH,OAAO+B,KAAK9D,QACZiJ,QAAUzH,KAAKyH,OAAOoB,KAAKrK,QAC3BsK,IAAM,IAAIvC,EAAUvG,KAAK8I,IAAKpL,EAAQc,EAAGmJ,EAAQ,EAAG5E,EAAY,EAAGgF,EAAMgB,IAAIrL,EAAQc,EAAGwB,YACxF8I,IAAIE,EAAIpB,GAAQ,OAChBkB,IAAIG,GAAKpB,OACTiB,IAAII,GAAKpB,UAGhBP,EAAMtC,SAASA,GAnSE,GAsSlBkE,uBAAOC,EAAOC,OACTlL,EAAKkL,EAAKP,QACdM,EAAQ9C,EAAQ+C,EAAK9B,MAAM+B,MAAQD,EAAK9B,MAAMgC,SAChCnD,SACNjI,GACNA,EAAG4K,IAAI5K,EAAGI,EAAGJ,EAAGK,EAAGwC,EAAO7C,EAAGqL,EAAIrL,EAAG8K,GAAKG,EAAQjL,EAAG+K,GAAKE,EAAQA,GAASjL,EAAG6K,EAAG7K,EAAGsL,EAAGL,GACtFjL,EAAKA,EAAGU,WAGTwK,EAAK5B,OAAOiC,WAMf,mDAAmDvH,MAAM,KAAKK,QAAQ,SAAA8B,UAAQ8C,EAAc9C,GAAQhE,EAAgBgE,UAEtG/G,EAAKoF,eAAeyE"}