{"version": 3, "file": "Observer.min.js", "sources": ["../src/Observer.js"], "sourcesContent": ["/*!\n * Observer 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _clamp, _win, _doc, _docEl, _body, _isTouch, _pointerType, ScrollTrigger, _root, _normalizer, _eventTypes, _context,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_startup = 1,\n\t_observers = [],\n\t_scrollers = [],\n\t_proxies = [],\n\t_getTime = Date.now,\n\t_bridge = (name, value) => value,\n\t_integrate = () => {\n\t\tlet core = ScrollTrigger.core,\n\t\t\tdata = core.bridge || {},\n\t\t\tscrollers = core._scrollers,\n\t\t\tproxies = core._proxies;\n\t\tscrollers.push(..._scrollers);\n\t\tproxies.push(..._proxies);\n\t\t_scrollers = scrollers;\n\t\t_proxies = proxies;\n\t\t_bridge = (name, value) => data[name](value);\n\t},\n\t_getProxyProp = (element, property) => ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property],\n\t_isViewport = el => !!~_root.indexOf(el),\n\t_addListener = (element, type, func, passive, capture) => element.addEventListener(type, func, {passive: passive !== false, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_scrollLeft = \"scrollLeft\",\n\t_scrollTop = \"scrollTop\",\n\t_onScroll = () => (_normalizer && _normalizer.isPressed) || _scrollers.cache++,\n\t_scrollCacheFunc = (f, doNotCache) => {\n\t\tlet cachingFunc = value => { // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n\t\t\tif (value || value === 0) {\n\t\t\t\t_startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\t\t\t\tlet isNormalizing = _normalizer && _normalizer.isPressed;\n\t\t\t\tvalue = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\t\t\t\tf(value);\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tisNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n\t\t\t} else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tcachingFunc.v = f();\n\t\t\t}\n\t\t\treturn cachingFunc.v + cachingFunc.offset;\n\t\t};\n\t\tcachingFunc.offset = 0;\n\t\treturn f && cachingFunc;\n\t},\n\t_horizontal = {s: _scrollLeft, p: \"left\", p2: \"Left\", os: \"right\", os2: \"Right\", d: \"width\", d2: \"Width\", a: \"x\", sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0})},\n\t_vertical = {s: _scrollTop, p: \"top\", p2: \"Top\", os: \"bottom\", os2: \"Bottom\", d: \"height\", d2: \"Height\", a: \"y\", op: _horizontal, sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0})},\n\t_getTarget = (t, self) => ((self && self._ctx && self._ctx.selector) || gsap.utils.toArray)(t)[0] || (typeof(t) === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null),\n\n\t_isWithin = (element, list) => { // check if the element is in the list or is a descendant of an element in the list.\n\t\tlet i = list.length;\n\t\twhile (i--) {\n\t\t\tif (list[i] === element || list[i].contains(element)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t},\n\t_getScrollFunc = (element, {s, sc}) => { // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n\t\t_isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\t\tlet i = _scrollers.indexOf(element),\n\t\t\toffset = sc === _vertical.sc ? 1 : 2;\n\t\t!~i && (i = _scrollers.push(element) - 1);\n\t\t_scrollers[i + offset] || _addListener(element, \"scroll\", _onScroll); // clear the cache when a scroll occurs\n\t\tlet prev = _scrollers[i + offset],\n\t\t\tfunc = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function(value) { return arguments.length ? (element[s] = value) : element[s]; })));\n\t\tfunc.target = element;\n\t\tprev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\t\treturn func;\n\t},\n\t_getVelocityProp = (value, minTimeRefresh, useDelta) => {\n\t\tlet v1 = value,\n\t\t\tv2 = value,\n\t\t\tt1 = _getTime(),\n\t\t\tt2 = t1,\n\t\t\tmin = minTimeRefresh || 50,\n\t\t\tdropToZeroTime = Math.max(500, min * 3),\n\t\t\tupdate = (value, force) => {\n\t\t\t\tlet t = _getTime();\n\t\t\t\tif (force || t - t1 > min) {\n\t\t\t\t\tv2 = v1;\n\t\t\t\t\tv1 = value;\n\t\t\t\t\tt2 = t1;\n\t\t\t\t\tt1 = t;\n\t\t\t\t} else if (useDelta) {\n\t\t\t\t\tv1 += value;\n\t\t\t\t} else { // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n\t\t\t\t\tv1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n\t\t\t\t}\n\t\t\t},\n\t\t\treset = () => { v2 = v1 = useDelta ? 0 : v1; t2 = t1 = 0; },\n\t\t\tgetVelocity = latestValue => {\n\t\t\t\tlet tOld = t2,\n\t\t\t\t\tvOld = v2,\n\t\t\t\t\tt = _getTime();\n\t\t\t\t(latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n\t\t\t\treturn (t1 === t2 || t - t2 > dropToZeroTime) ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n\t\t\t};\n\t\treturn {update, reset, getVelocity};\n\t},\n\t_getEvent = (e, preventDefault) => {\n\t\tpreventDefault && !e._gsapAllow && e.preventDefault();\n\t\treturn e.changedTouches ? e.changedTouches[0] : e;\n\t},\n\t_getAbsoluteMax = a => {\n\t\tlet max = Math.max(...a),\n\t\t\tmin = Math.min(...a);\n\t\treturn Math.abs(max) >= Math.abs(min) ? max : min;\n\t},\n\t_setScrollTrigger = () => {\n\t\tScrollTrigger = gsap.core.globals().ScrollTrigger;\n\t\tScrollTrigger && ScrollTrigger.core && _integrate();\n\t},\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (!_coreInitted && gsap && typeof(document) !== \"undefined\" && document.body) {\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t\t_docEl = _doc.documentElement;\n\t\t\t_body = _doc.body;\n\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_context = gsap.core.context || function() {};\n\t\t\t_pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\";\n\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t_isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : (\"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0) ? 2 : 0;\n\t\t\t_eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n\t\t\tsetTimeout(() => _startup = 0, 500);\n\t\t\t_setScrollTrigger();\n\t\t\t_coreInitted = 1;\n\t\t}\n\t\treturn _coreInitted;\n\t};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\n\nexport class Observer {\n\tconstructor(vars) {\n\t\tthis.init(vars);\n\t}\n\n\tinit(vars) {\n\t\t_coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n\t\tScrollTrigger || _setScrollTrigger();\n\t\tlet {tolerance, dragMinimum, type, target, lineHeight, debounce, preventDefault, onStop, onStopDelay, ignore, wheelSpeed, event, onDragStart, onDragEnd, onDrag, onPress, onRelease, onRight, onLeft, onUp, onDown, onChangeX, onChangeY, onChange, onToggleX, onToggleY, onHover, onHoverEnd, onMove, ignoreCheck, isNormalizer, onGestureStart, onGestureEnd, onWheel, onEnable, onDisable, onClick, scrollSpeed, capture, allowClicks, lockAxis, onLockAxis} = vars;\n\t\tthis.target = target = _getTarget(target) || _docEl;\n\t\tthis.vars = vars;\n\t\tignore && (ignore = gsap.utils.toArray(ignore));\n\t\ttolerance = tolerance || 1e-9;\n\t\tdragMinimum = dragMinimum || 0;\n\t\twheelSpeed = wheelSpeed || 1;\n\t\tscrollSpeed = scrollSpeed || 1;\n\t\ttype = type || \"wheel,touch,pointer\";\n\t\tdebounce = debounce !== false;\n\t\tlineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\t\tlet id, onStopDelayedCall, dragged, moved, wheeled, locked, axis,\n\t\t\tself = this,\n\t\t\tprevDeltaX = 0,\n\t\t\tprevDeltaY = 0,\n\t\t\tpassive = vars.passive || (!preventDefault && vars.passive !== false),\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollX = scrollFuncX(),\n\t\t\tscrollY = scrollFuncY(),\n\t\t\tlimitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\", // for devices that accommodate mouse events and touch events, we need to distinguish.\n\t\t\tisViewport = _isViewport(target),\n\t\t\townerDoc = target.ownerDocument || _doc,\n\t\t\tdeltaX = [0, 0, 0], // wheel, scroll, pointer/touch\n\t\t\tdeltaY = [0, 0, 0],\n\t\t\tonClickTime = 0,\n\t\t\tclickCapture = () => onClickTime = _getTime(),\n\t\t\t_ignoreCheck = (e, isPointerOrTouch) => (self.event = e) && (ignore && _isWithin(e.target, ignore)) || (isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\") || (ignoreCheck && ignoreCheck(e, isPointerOrTouch)),\n\t\t\tonStopFunc = () => {\n\t\t\t\tself._vx.reset();\n\t\t\t\tself._vy.reset();\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tonStop && onStop(self);\n\t\t\t},\n\t\t\tupdate = () => {\n\t\t\t\tlet dx = self.deltaX = _getAbsoluteMax(deltaX),\n\t\t\t\t\tdy = self.deltaY = _getAbsoluteMax(deltaY),\n\t\t\t\t\tchangedX = Math.abs(dx) >= tolerance,\n\t\t\t\t\tchangedY = Math.abs(dy) >= tolerance;\n\t\t\t\tonChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\t\t\t\tif (changedX) {\n\t\t\t\t\tonRight && self.deltaX > 0 && onRight(self);\n\t\t\t\t\tonLeft && self.deltaX < 0 && onLeft(self);\n\t\t\t\t\tonChangeX && onChangeX(self);\n\t\t\t\t\tonToggleX && ((self.deltaX < 0) !== (prevDeltaX < 0)) && onToggleX(self);\n\t\t\t\t\tprevDeltaX = self.deltaX;\n\t\t\t\t\tdeltaX[0] = deltaX[1] = deltaX[2] = 0\n\t\t\t\t}\n\t\t\t\tif (changedY) {\n\t\t\t\t\tonDown && self.deltaY > 0 && onDown(self);\n\t\t\t\t\tonUp && self.deltaY < 0 && onUp(self);\n\t\t\t\t\tonChangeY && onChangeY(self);\n\t\t\t\t\tonToggleY && ((self.deltaY < 0) !== (prevDeltaY < 0)) && onToggleY(self);\n\t\t\t\t\tprevDeltaY = self.deltaY;\n\t\t\t\t\tdeltaY[0] = deltaY[1] = deltaY[2] = 0\n\t\t\t\t}\n\t\t\t\tif (moved || dragged) {\n\t\t\t\t\tonMove && onMove(self);\n\t\t\t\t\tif (dragged) {\n\t\t\t\t\t\tonDragStart && dragged === 1 && onDragStart(self);\n\t\t\t\t\t\tonDrag && onDrag(self);\n\t\t\t\t\t\tdragged = 0;\n\t\t\t\t\t}\n\t\t\t\t\tmoved = false;\n\t\t\t\t}\n\t\t\t\tlocked && !(locked = false) && onLockAxis && onLockAxis(self);\n\t\t\t\tif (wheeled) {\n\t\t\t\t\tonWheel(self);\n\t\t\t\t\twheeled = false;\n\t\t\t\t}\n\t\t\t\tid = 0;\n\t\t\t},\n\t\t\tonDelta = (x, y, index) => {\n\t\t\t\tdeltaX[index] += x;\n\t\t\t\tdeltaY[index] += y;\n\t\t\t\tself._vx.update(x);\n\t\t\t\tself._vy.update(y);\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\tonTouchOrPointerDelta = (x, y) => {\n\t\t\t\tif (lockAxis && !axis) {\n\t\t\t\t\tself.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n\t\t\t\t\tlocked = true;\n\t\t\t\t}\n\t\t\t\tif (axis !== \"y\") {\n\t\t\t\t\tdeltaX[2] += x;\n\t\t\t\t\tself._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\t\t\t\t}\n\t\t\t\tif (axis !== \"x\") {\n\t\t\t\t\tdeltaY[2] += y;\n\t\t\t\t\tself._vy.update(y, true);\n\t\t\t\t}\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\t_onDrag = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y,\n\t\t\t\t\tisDragging = self.isDragging;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tif (isDragging || ((dx || dy) && (Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum))) {\n\t\t\t\t\tdragged = isDragging ? 2 : 1; // dragged: 0 = not dragging, 1 = first drag, 2 = normal drag\n\t\t\t\t\tisDragging || (self.isDragging = true);\n\t\t\t\t\tonTouchOrPointerDelta(dx, dy);\n\t\t\t\t}\n\t\t\t},\n\t\t\t_onPress = self.onPress = e => {\n\t\t\t\tif (_ignoreCheck(e, 1) || (e && e.button)) {return;}\n\t\t\t\tself.axis = axis = null;\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tself.isPressed = true;\n\t\t\t\te = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\t\t\t\tprevDeltaX = prevDeltaY = 0;\n\t\t\t\tself.startX = self.x = e.clientX;\n\t\t\t\tself.startY = self.y = e.clientY;\n\t\t\t\tself._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\t\t\t\tself._vy.reset();\n\t\t\t\t_addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, passive, true);\n\t\t\t\tself.deltaX = self.deltaY = 0;\n\t\t\t\tonPress && onPress(self);\n\t\t\t},\n\t\t\t_onRelease = self.onRelease = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\tlet isTrackingDrag = !isNaN(self.y - self.startY),\n\t\t\t\t\twasDragging = self.isDragging,\n\t\t\t\t\tisDragNotClick = wasDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3), // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n\t\t\t\t\teventData = _getEvent(e);\n\t\t\t\tif (!isDragNotClick && isTrackingDrag) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t//if (preventDefault && allowClicks && self.isPressed) { // check isPressed because in a rare edge case, the inputObserver in ScrollTrigger may stopPropagation() on the press/drag, so the onRelease may get fired without the onPress/onDrag ever getting called, thus it could trigger a click to occur on a link after scroll-dragging it.\n\t\t\t\t\tif (preventDefault && allowClicks) {\n\t\t\t\t\t\tgsap.delayedCall(0.08, () => { // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n\t\t\t\t\t\t\tif (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n\t\t\t\t\t\t\t\tif (e.target.click) { //some browsers (like mobile Safari) don't properly trigger the click event\n\t\t\t\t\t\t\t\t\te.target.click();\n\t\t\t\t\t\t\t\t} else if (ownerDoc.createEvent) {\n\t\t\t\t\t\t\t\t\tlet syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n\t\t\t\t\t\t\t\t\tsyntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n\t\t\t\t\t\t\t\t\te.target.dispatchEvent(syntheticEvent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t\t\tonStop && wasDragging && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t\tdragged && update(); // in case debouncing, we don't want onDrag to fire AFTER onDragEnd().\n\t\t\t\tonDragEnd && wasDragging && onDragEnd(self);\n\t\t\t\tonRelease && onRelease(self, isDragNotClick);\n\t\t\t},\n\t\t\t_onGestureStart = e => e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging),\n\t\t\t_onGestureEnd = () => (self.isGesturing = false) || onGestureEnd(self),\n\t\t\tonScroll = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = scrollFuncX(),\n\t\t\t\t\ty = scrollFuncY();\n\t\t\t\tonDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n\t\t\t\tscrollX = x;\n\t\t\t\tscrollY = y;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onWheel = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tonWheel && (wheeled = true);\n\t\t\t\tlet multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n\t\t\t\tonDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onMove = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tmoved = true;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t\t(dx || dy) && onTouchOrPointerDelta(dx, dy);\n\t\t\t},\n\t\t\t_onHover = e => {self.event = e; onHover(self);},\n\t\t\t_onHoverEnd = e => {self.event = e; onHoverEnd(self);},\n\t\t\t_onClick = e => _ignoreCheck(e) || (_getEvent(e, preventDefault) && onClick(self));\n\n\t\tonStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n\n\t\tself.deltaX = self.deltaY = 0;\n\t\tself._vx = _getVelocityProp(0, 50, true);\n\t\tself._vy = _getVelocityProp(0, 50, true);\n\t\tself.scrollX = scrollFuncX;\n\t\tself.scrollY = scrollFuncY;\n\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t_context(this);\n\t\tself.enable = e => {\n\t\t\tif (!self.isEnabled) {\n\t\t\t\t_addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\ttype.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, passive, capture);\n\t\t\t\ttype.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, passive, capture);\n\t\t\t\tif ((type.indexOf(\"touch\") >= 0 && _isTouch) || type.indexOf(\"pointer\") >= 0) {\n\t\t\t\t\t_addListener(target, _eventTypes[0], _onPress, passive, capture);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t\tallowClicks && _addListener(target, \"click\", clickCapture, true, true);\n\t\t\t\t\tonClick && _addListener(target, \"click\", _onClick);\n\t\t\t\t\tonGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t\tonGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t\tonHover && _addListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t\tonHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t\tonMove && _addListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\t}\n\t\t\t\tself.isEnabled = true;\n\t\t\t\tself.isDragging = self.isGesturing = self.isPressed = moved = dragged = false;\n\t\t\t\tself._vx.reset();\n\t\t\t\tself._vy.reset();\n\t\t\t\tscrollX = scrollFuncX();\n\t\t\t\tscrollY = scrollFuncY();\n\t\t\t\te && e.type && _onPress(e);\n\t\t\t\tonEnable && onEnable(self);\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\t\tself.disable = () => {\n\t\t\tif (self.isEnabled) {\n\t\t\t\t// only remove the _onScroll listener if there aren't any others that rely on the functionality.\n\t\t\t\t_observers.filter(o => o !== self && _isViewport(o.target)).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\tif (self.isPressed) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\t}\n\t\t\t\t_removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\t\t\t\t_removeListener(target, \"wheel\", _onWheel, capture);\n\t\t\t\t_removeListener(target, _eventTypes[0], _onPress, capture);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t_removeListener(target, \"click\", clickCapture, true);\n\t\t\t\t_removeListener(target, \"click\", _onClick);\n\t\t\t\t_removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t_removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t_removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\tself.isEnabled = self.isPressed = self.isDragging = false;\n\t\t\t\tonDisable && onDisable(self);\n\t\t\t}\n\t\t};\n\n\t\tself.kill = self.revert = () => {\n\t\t\tself.disable();\n\t\t\tlet i = _observers.indexOf(self);\n\t\t\ti >= 0 && _observers.splice(i, 1);\n\t\t\t_normalizer === self && (_normalizer = 0);\n\t\t}\n\n\t\t_observers.push(self);\n\t\tisNormalizer && _isViewport(target) && (_normalizer = self);\n\n\t\tself.enable(event);\n\t}\n\n\tget velocityX() {\n\t\treturn this._vx.getVelocity();\n\t}\n\tget velocityY() {\n\t\treturn this._vy.getVelocity();\n\t}\n\n}\n\nObserver.version = \"3.13.0\";\nObserver.create = vars => new Observer(vars);\nObserver.register = _initCore;\nObserver.getAll = () => _observers.slice();\nObserver.getById = id => _observers.filter(o => o.vars.id === id)[0];\n\n_getGSAP() && gsap.registerPlugin(Observer);\n\nexport { Observer as default, _isViewport, _scrollers, _getScrollFunc, _getProxyProp, _proxies, _getVelocityProp, _vertical, _horizontal, _getTarget };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_coreInitted", "_win", "_doc", "_docEl", "_body", "_isTouch", "_pointerType", "ScrollTrigger", "_root", "_normalizer", "_eventTypes", "_context", "_startup", "_observers", "_scrollers", "_proxies", "_getProxyProp", "element", "property", "indexOf", "_isViewport", "el", "_addListener", "type", "func", "passive", "capture", "addEventListener", "_removeListener", "removeEventListener", "_onScroll", "isPressed", "cache", "_scrollCacheFunc", "f", "doNotCache", "cachingFunc", "value", "history", "scrollRestoration", "isNormalizing", "v", "Math", "round", "iOS", "cacheID", "_bridge", "offset", "_getTarget", "t", "self", "_ctx", "selector", "utils", "toArray", "config", "null<PERSON><PERSON><PERSON><PERSON><PERSON>n", "console", "warn", "_getScrollFunc", "s", "sc", "scrollingElement", "i", "_vertical", "push", "prev", "arguments", "length", "target", "smooth", "getProperty", "_getVelocityProp", "minTimeRefresh", "useDel<PERSON>", "update", "force", "_getTime", "min", "t1", "v2", "v1", "t2", "dropToZeroTime", "max", "reset", "getVelocity", "latestValue", "tOld", "vOld", "_getEvent", "e", "preventDefault", "_gsapAllow", "changedTouches", "_getAbsoluteMax", "a", "abs", "_setScrollTrigger", "core", "globals", "_integrate", "data", "bridge", "scrollers", "proxies", "name", "_initCore", "document", "body", "documentElement", "clamp", "context", "Observer", "is<PERSON><PERSON>ch", "matchMedia", "matches", "navigator", "maxTouchPoints", "msMaxTouchPoints", "eventTypes", "split", "setTimeout", "Date", "now", "_scrollLeft", "_scrollTop", "_horizontal", "p", "p2", "os", "os2", "d", "d2", "scrollTo", "pageXOffset", "op", "pageYOffset", "init", "vars", "tolerance", "dragMinimum", "lineHeight", "debounce", "onStop", "onStopDelay", "ignore", "wheelSpeed", "event", "onDragStart", "onDragEnd", "onDrag", "onPress", "onRelease", "onRight", "onLeft", "onUp", "onDown", "onChangeX", "onChangeY", "onChange", "onToggleX", "onToggleY", "onHover", "onHoverEnd", "onMove", "<PERSON><PERSON><PERSON><PERSON>", "isNormalizer", "onGestureStart", "onGestureEnd", "onWheel", "onEnable", "onDisable", "onClick", "scrollSpeed", "allowClicks", "lockAxis", "onLockAxis", "clickCapture", "onClickTime", "_ignore<PERSON>heck", "isPointerOr<PERSON>ouch", "_isWithin", "list", "contains", "limitToTouch", "pointerType", "dx", "deltaX", "dy", "deltaY", "changedX", "changedY", "prevDeltaX", "prevDeltaY", "moved", "dragged", "locked", "wheeled", "id", "onDelta", "x", "y", "index", "_vx", "_vy", "requestAnimationFrame", "onTouchOrPointerDelta", "axis", "_onDrag", "clientX", "clientY", "isDragging", "startX", "startY", "_onGestureStart", "touches", "isGesturing", "_onGestureEnd", "onScroll", "scrollFuncX", "scrollFuncY", "scrollX", "scrollY", "onStopDelayedCall", "restart", "_onWheel", "multiplier", "deltaMode", "innerHeight", "_onMove", "_onHover", "_onHoverEnd", "_onClick", "parseFloat", "getComputedStyle", "this", "isViewport", "ownerDoc", "ownerDocument", "_onPress", "button", "pause", "_onRelease", "isTrackingDrag", "isNaN", "wasDragging", "isDragNotClick", "eventData", "delayedCall", "defaultPrevented", "click", "createEvent", "syntheticEvent", "initMouseEvent", "screenX", "screenY", "dispatchEvent", "_dc", "onStopFunc", "enable", "isEnabled", "disable", "filter", "o", "kill", "revert", "splice", "version", "create", "register", "getAll", "slice", "getById"], "mappings": ";;;;;;;;;mYAWY,SAAXA,WAAiBC,IAA4B,oBAAZC,SAA4BD,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,OADxGA,GAAMG,GAAsBC,GAAMC,GAAMC,GAAQC,GAAOC,GAAUC,GAAcC,GAAeC,EAAOC,GAAaC,GAAaC,GAElIC,EAAW,EACXC,GAAa,GACbC,aAAa,GACbC,WAAW,GAcK,SAAhBC,EAAiBC,EAASC,UAAcH,WAASI,QAAQF,IAAYF,WAASA,WAASI,QAAQF,GAAW,GAAGC,GAC/F,SAAdE,EAAcC,YAASb,EAAMW,QAAQE,GACtB,SAAfC,EAAgBL,EAASM,EAAMC,EAAMC,EAASC,UAAYT,EAAQU,iBAAiBJ,EAAMC,EAAM,CAACC,SAAqB,IAAZA,EAAmBC,UAAWA,IACrH,SAAlBE,EAAmBX,EAASM,EAAMC,EAAME,UAAYT,EAAQY,oBAAoBN,EAAMC,IAAQE,GAGlF,SAAZI,WAAmBrB,IAAeA,GAAYsB,WAAcjB,aAAWkB,QACpD,SAAnBC,EAAoBC,EAAGC,GACJ,SAAdC,GAAcC,MACbA,GAAmB,IAAVA,EAAa,CACzBzB,IAAaX,GAAKqC,QAAQC,kBAAoB,cAC1CC,EAAgB/B,IAAeA,GAAYsB,UAC/CM,EAAQD,GAAYK,EAAIC,KAAKC,MAAMN,KAAW5B,IAAeA,GAAYmC,IAAM,EAAI,GACnFV,EAAEG,GACFD,GAAYS,QAAU/B,aAAWkB,MACjCQ,GAAiBM,EAAQ,KAAMT,QACrBF,GAAcrB,aAAWkB,QAAUI,GAAYS,SAAWC,EAAQ,UAC5EV,GAAYS,QAAU/B,aAAWkB,MACjCI,GAAYK,EAAIP,YAEVE,GAAYK,EAAIL,GAAYW,cAEpCX,GAAYW,OAAS,EACdb,GAAKE,GAIA,SAAbY,EAAcC,EAAGC,UAAWA,GAAQA,EAAKC,MAAQD,EAAKC,KAAKC,UAAavD,GAAKwD,MAAMC,SAASL,GAAG,KAAqB,iBAAPA,IAAoD,IAAjCpD,GAAK0D,SAASC,eAA2BC,QAAQC,KAAK,qBAAsBT,GAAK,MAWhM,SAAjBU,EAAkB1C,SAAU2C,IAAAA,EAAGC,IAAAA,GAC9BzC,EAAYH,KAAaA,EAAUf,GAAK4D,kBAAoB3D,QACxD4D,EAAIjD,aAAWK,QAAQF,GAC1B8B,EAASc,IAAOG,GAAUH,GAAK,EAAI,GAClCE,IAAMA,EAAIjD,aAAWmD,KAAKhD,GAAW,GACvCH,aAAWiD,EAAIhB,IAAWzB,EAAaL,EAAS,SAAUa,OACtDoC,EAAOpD,aAAWiD,EAAIhB,GACzBvB,EAAO0C,IAASpD,aAAWiD,EAAIhB,GAAUd,EAAiBjB,EAAcC,EAAS2C,IAAI,KAAUxC,EAAYH,GAAW4C,EAAK5B,EAAiB,SAASI,UAAgB8B,UAAUC,OAAUnD,EAAQ2C,GAAKvB,EAASpB,EAAQ2C,cACxNpC,EAAK6C,OAASpD,EACdiD,IAAS1C,EAAK8C,OAAyD,WAAhDzE,GAAK0E,YAAYtD,EAAS,mBAC1CO,EAEW,SAAnBgD,EAAoBnC,EAAOoC,EAAgBC,GAOhC,SAATC,GAAUtC,EAAOuC,OACZ3B,EAAI4B,KACJD,GAAkBE,EAAT7B,EAAI8B,GAChBC,EAAKC,EACLA,EAAK5C,EACL6C,EAAKH,EACLA,EAAK9B,GACKyB,EACVO,GAAM5C,EAEN4C,EAAKD,GAAM3C,EAAQ2C,IAAO/B,EAAIiC,IAAOH,EAAKG,OAhBzCD,EAAK5C,EACR2C,EAAK3C,EACL0C,EAAKF,KACLK,EAAKH,EACLD,EAAML,GAAkB,GACxBU,EAAiBzC,KAAK0C,IAAI,IAAW,EAANN,SAsBzB,CAACH,OAAAA,GAAQU,MARP,SAARA,QAAgBL,EAAKC,EAAKP,EAAW,EAAIO,EAAIC,EAAKH,EAAK,GAQjCO,YAPR,SAAdA,YAAcC,OACTC,EAAON,EACVO,EAAOT,EACP/B,EAAI4B,YACJU,GAA+B,IAAhBA,GAAsBA,IAAgBN,GAAMN,GAAOY,GAC3DR,IAAOG,GAAeC,EAATlC,EAAIiC,EAAuB,GAAKD,GAAMP,EAAWe,GAAQA,MAAWf,EAAWzB,EAAI8B,GAAMS,GAAQ,MAI7G,SAAZE,EAAaC,EAAGC,UACfA,IAAmBD,EAAEE,YAAcF,EAAEC,iBAC9BD,EAAEG,eAAiBH,EAAEG,eAAe,GAAKH,EAE/B,SAAlBI,EAAkBC,OACbZ,EAAM1C,KAAK0C,UAAL1C,KAAYsD,GACrBlB,EAAMpC,KAAKoC,UAALpC,KAAYsD,UACZtD,KAAKuD,IAAIb,IAAQ1C,KAAKuD,IAAInB,GAAOM,EAAMN,EAE3B,SAApBoB,KACC3F,GAAgBV,GAAKsG,KAAKC,UAAU7F,gBACnBA,GAAc4F,MAtGnB,SAAbE,iBACKF,EAAO5F,GAAc4F,KACxBG,EAAOH,EAAKI,QAAU,GACtBC,EAAYL,EAAKrF,WACjB2F,EAAUN,EAAKpF,SAChByF,EAAUvC,WAAVuC,EAAkB1F,cAClB2F,EAAQxC,WAARwC,EAAgB1F,YAChBD,aAAa0F,EACbzF,WAAW0F,EACX3D,EAAU,iBAAC4D,EAAMrE,UAAUiE,EAAKI,GAAMrE,IA6FCgE,GAE5B,SAAZM,EAAYR,UACXtG,GAAOsG,GAAQvG,KACVI,IAAgBH,IAA6B,oBAAd+G,UAA6BA,SAASC,OACzE5G,GAAOH,OAEPK,IADAD,GAAO0G,UACOE,gBACd1G,GAAQF,GAAK2G,KACbrG,EAAQ,CAACP,GAAMC,GAAMC,GAAQC,IACpBP,GAAKwD,MAAM0D,MACpBpG,GAAWd,GAAKsG,KAAKa,SAAW,aAChC1G,GAAe,mBAAoBF,GAAQ,UAAY,QAEvDC,GAAW4G,EAASC,QAAUjH,GAAKkH,YAAclH,GAAKkH,WAAW,oCAAoCC,QAAU,EAAK,iBAAkBnH,IAAmC,EAA3BoH,UAAUC,gBAAmD,EAA7BD,UAAUE,iBAAwB,EAAI,EACpN7G,GAAcuG,EAASO,YAAc,iBAAkBrH,GAAS,4CAAgD,kBAAmBA,GAAkD,kDAAxC,uCAA2FsH,MAAM,KAC9OC,WAAW,kBAAM9G,EAAW,GAAG,KAC/BsF,IACAlG,GAAe,GAETA,OA5HR6E,GAAW8C,KAAKC,IAChB9E,EAAU,iBAAC4D,EAAMrE,UAAUA,GAgB3BwF,EAAc,aACdC,EAAa,YAoBbC,GAAc,CAACnE,EAAGiE,EAAaG,EAAG,OAAQC,GAAI,OAAQC,GAAI,QAASC,IAAK,QAASC,EAAG,QAASC,GAAI,QAASrC,EAAG,IAAKnC,GAAI5B,EAAiB,SAASI,UAAgB8B,UAAUC,OAASnE,GAAKqI,SAASjG,EAAO2B,GAAUH,MAAQ5D,GAAKsI,aAAerI,GAAK2H,IAAgB1H,GAAO0H,IAAgBzH,GAAMyH,IAAgB,KAChT7D,GAAY,CAACJ,EAAGkE,EAAYE,EAAG,MAAOC,GAAI,MAAOC,GAAI,SAAUC,IAAK,SAAUC,EAAG,SAAUC,GAAI,SAAUrC,EAAG,IAAKwC,GAAIT,GAAalE,GAAI5B,EAAiB,SAASI,UAAgB8B,UAAUC,OAASnE,GAAKqI,SAASP,GAAYlE,KAAMxB,GAASpC,GAAKwI,aAAevI,GAAK4H,IAAe3H,GAAO2H,IAAe1H,GAAM0H,IAAe,KAwFhUC,GAAYS,GAAKxE,gBACNhC,MAAQ,MAENiF,sBAKZyB,KAAA,cAAKC,GACJ3I,IAAgB2G,EAAU9G,KAAS4D,QAAQC,KAAK,wCAChDnD,IAAiB2F,QACZ0C,EAA6bD,EAA7bC,UAAWC,EAAkbF,EAAlbE,YAAatH,EAAqaoH,EAArapH,KAAM8C,EAA+ZsE,EAA/ZtE,OAAQyE,EAAuZH,EAAvZG,WAAYC,EAA2YJ,EAA3YI,SAAUnD,EAAiY+C,EAAjY/C,eAAgBoD,EAAiXL,EAAjXK,OAAQC,EAAyWN,EAAzWM,YAAaC,EAA4VP,EAA5VO,OAAQC,EAAoVR,EAApVQ,WAAYC,EAAwUT,EAAxUS,MAAOC,EAAiUV,EAAjUU,YAAaC,EAAoTX,EAApTW,UAAWC,EAAySZ,EAAzSY,OAAQC,EAAiSb,EAAjSa,QAASC,EAAwRd,EAAxRc,UAAWC,EAA6Qf,EAA7Qe,QAASC,EAAoQhB,EAApQgB,OAAQC,EAA4PjB,EAA5PiB,KAAMC,EAAsPlB,EAAtPkB,OAAQC,EAA8OnB,EAA9OmB,UAAWC,EAAmOpB,EAAnOoB,UAAWC,EAAwNrB,EAAxNqB,SAAUC,EAA8MtB,EAA9MsB,UAAWC,EAAmMvB,EAAnMuB,UAAWC,EAAwLxB,EAAxLwB,QAASC,EAA+KzB,EAA/KyB,WAAYC,EAAmK1B,EAAnK0B,OAAQC,EAA2J3B,EAA3J2B,YAAaC,EAA8I5B,EAA9I4B,aAAcC,EAAgI7B,EAAhI6B,eAAgBC,EAAgH9B,EAAhH8B,aAAcC,EAAkG/B,EAAlG+B,QAASC,EAAyFhC,EAAzFgC,SAAUC,EAA+EjC,EAA/EiC,UAAWC,EAAoElC,EAApEkC,QAASC,EAA2DnC,EAA3DmC,YAAapJ,EAA8CiH,EAA9CjH,QAASqJ,EAAqCpC,EAArCoC,YAAaC,EAAwBrC,EAAxBqC,SAAUC,EAActC,EAAdsC,WA0Bpa,SAAfC,YAAqBC,GAActG,KACpB,SAAfuG,GAAgBzF,EAAG0F,UAAsBnI,GAAKkG,MAAQzD,IAAOuD,GA3HnD,SAAZoC,UAAarK,EAASsK,WACjBxH,EAAIwH,EAAKnH,OACNL,QACFwH,EAAKxH,KAAO9C,GAAWsK,EAAKxH,GAAGyH,SAASvK,UACpC,SAGF,EAoHiEqK,CAAU3F,EAAEtB,OAAQ6E,IAAamC,GAAoBI,IAAkC,UAAlB9F,EAAE+F,aAA6BpB,GAAeA,EAAY3E,EAAG0F,GAOhM,SAAT1G,SACKgH,EAAKzI,GAAK0I,OAAS7F,EAAgB6F,IACtCC,EAAK3I,GAAK4I,OAAS/F,EAAgB+F,IACnCC,EAAWrJ,KAAKuD,IAAI0F,IAAO/C,EAC3BoD,EAAWtJ,KAAKuD,IAAI4F,IAAOjD,EAC5BoB,IAAa+B,GAAYC,IAAahC,EAAS9G,GAAMyI,EAAIE,EAAID,GAAQE,IACjEC,IACHrC,GAAyB,EAAdxG,GAAK0I,QAAclC,EAAQxG,IACtCyG,GAAUzG,GAAK0I,OAAS,GAAKjC,EAAOzG,IACpC4G,GAAaA,EAAU5G,IACvB+G,GAAe/G,GAAK0I,OAAS,GAAQK,GAAa,GAAOhC,EAAU/G,IACnE+I,GAAa/I,GAAK0I,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,GAEjCI,IACHnC,GAAwB,EAAd3G,GAAK4I,QAAcjC,EAAO3G,IACpC0G,GAAQ1G,GAAK4I,OAAS,GAAKlC,EAAK1G,IAChC6G,GAAaA,EAAU7G,IACvBgH,GAAehH,GAAK4I,OAAS,GAAQI,GAAa,GAAOhC,EAAUhH,IACnEgJ,GAAahJ,GAAK4I,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,IAEjCK,IAASC,MACZ/B,GAAUA,EAAOnH,IACbkJ,KACH/C,GAA2B,IAAZ+C,IAAiB/C,EAAYnG,IAC5CqG,GAAUA,EAAOrG,IACjBkJ,GAAU,GAEXD,IAAQ,GAETE,MAAYA,IAAS,IAAUpB,GAAcA,EAAW/H,IACpDoJ,KACH5B,EAAQxH,IACRoJ,IAAU,GAEXC,GAAK,EAEI,SAAVC,GAAWC,EAAGC,EAAGC,GAChBf,GAAOe,IAAUF,EACjBX,GAAOa,IAAUD,EACjBxJ,GAAK0J,IAAIjI,OAAO8H,GAChBvJ,GAAK2J,IAAIlI,OAAO+H,GAChB3D,EAAkBwD,GAAPA,IAAYO,sBAAsBnI,IAAWA,KAEjC,SAAxBoI,GAAyBN,EAAGC,GACvB1B,IAAagC,KAChB9J,GAAK8J,KAAOA,GAAOtK,KAAKuD,IAAIwG,GAAK/J,KAAKuD,IAAIyG,GAAK,IAAM,IACrDL,IAAS,GAEG,MAATW,KACHpB,GAAO,IAAMa,EACbvJ,GAAK0J,IAAIjI,OAAO8H,GAAG,IAEP,MAATO,KACHlB,GAAO,IAAMY,EACbxJ,GAAK2J,IAAIlI,OAAO+H,GAAG,IAEpB3D,EAAkBwD,GAAPA,IAAYO,sBAAsBnI,IAAWA,KAE/C,SAAVsI,GAAUtH,OACLyF,GAAazF,EAAG,QAEhB8G,GADJ9G,EAAID,EAAUC,EAAGC,IACPsH,QACTR,EAAI/G,EAAEwH,QACNxB,EAAKc,EAAIvJ,GAAKuJ,EACdZ,EAAKa,EAAIxJ,GAAKwJ,EACdU,EAAalK,GAAKkK,WACnBlK,GAAKuJ,EAAIA,EACTvJ,GAAKwJ,EAAIA,GACLU,IAAgBzB,GAAME,KAAQnJ,KAAKuD,IAAI/C,GAAKmK,OAASZ,IAAM5D,GAAenG,KAAKuD,IAAI/C,GAAKoK,OAASZ,IAAM7D,MAC1GuD,GAAUgB,EAAa,EAAI,EAC3BA,IAAelK,GAAKkK,YAAa,GACjCL,GAAsBpB,EAAIE,KAiDV,SAAlB0B,GAAkB5H,UAAKA,EAAE6H,SAA8B,EAAnB7H,EAAE6H,QAAQpJ,SAAelB,GAAKuK,aAAc,IAASjD,EAAe7E,EAAGzC,GAAKkK,YAChG,SAAhBM,YAAuBxK,GAAKuK,aAAc,IAAUhD,EAAavH,IACtD,SAAXyK,GAAWhI,OACNyF,GAAazF,QACb8G,EAAImB,KACPlB,EAAImB,KACLrB,IAASC,EAAIqB,IAAWhD,GAAc4B,EAAIqB,IAAWjD,EAAa,GAClEgD,GAAUrB,EACVsB,GAAUrB,EACV1D,GAAUgF,GAAkBC,SAAQ,IAE1B,SAAXC,GAAWvI,OACNyF,GAAazF,IACjBA,EAAID,EAAUC,EAAGC,GACjB8E,IAAY4B,IAAU,OAClB6B,GAA8B,IAAhBxI,EAAEyI,UAAkBtF,EAA6B,IAAhBnD,EAAEyI,UAAkBnO,GAAKoO,YAAc,GAAKlF,EAC/FqD,GAAQ7G,EAAEiG,OAASuC,EAAYxI,EAAEmG,OAASqC,EAAY,GACtDnF,IAAWuB,GAAgByD,GAAkBC,SAAQ,IAE5C,SAAVK,GAAU3I,OACLyF,GAAazF,QACb8G,EAAI9G,EAAEuH,QACTR,EAAI/G,EAAEwH,QACNxB,EAAKc,EAAIvJ,GAAKuJ,EACdZ,EAAKa,EAAIxJ,GAAKwJ,EACfxJ,GAAKuJ,EAAIA,EACTvJ,GAAKwJ,EAAIA,EACTP,IAAQ,EACRnD,GAAUgF,GAAkBC,SAAQ,IACnCtC,GAAME,IAAOkB,GAAsBpB,EAAIE,IAE9B,SAAX0C,GAAW5I,GAAMzC,GAAKkG,MAAQzD,EAAGwE,EAAQjH,IAC3B,SAAdsL,GAAc7I,GAAMzC,GAAKkG,MAAQzD,EAAGyE,EAAWlH,IACpC,SAAXuL,GAAW9I,UAAKyF,GAAazF,IAAOD,EAAUC,EAAGC,IAAmBiF,EAAQ3H,SA5LxEmB,OAASA,EAASrB,EAAWqB,IAAWlE,QACxCwI,KAAOA,EACDO,EAAXA,GAAoBrJ,GAAKwD,MAAMC,QAAQ4F,GACvCN,EAAYA,GAAa,KACzBC,EAAcA,GAAe,EAC7BM,EAAaA,GAAc,EAC3B2B,EAAcA,GAAe,EAC7BvJ,EAAOA,GAAQ,sBACfwH,GAAwB,IAAbA,EACID,EAAfA,GAA4B4F,WAAWzO,GAAK0O,iBAAiBvO,IAAO0I,aAAe,OAC/EyD,GAAIyB,GAAmB5B,GAASD,GAAOG,GAASD,GAAQW,GAC3D9J,GAAO0L,KACP3C,GAAa,EACbC,GAAa,EACbzK,GAAUkH,EAAKlH,UAAamE,IAAmC,IAAjB+C,EAAKlH,QACnDmM,GAAcjK,EAAeU,EAAQ0D,IACrC8F,GAAclK,EAAeU,EAAQL,IACrC8J,GAAUF,KACVG,GAAUF,KACVpC,IAAgBlK,EAAKJ,QAAQ,YAAcI,EAAKJ,QAAQ,YAAiC,gBAAnBT,GAAY,GAClFmO,GAAazN,EAAYiD,GACzByK,GAAWzK,EAAO0K,eAAiB7O,GACnC0L,GAAS,CAAC,EAAG,EAAG,GAChBE,GAAS,CAAC,EAAG,EAAG,GAChBX,GAAc,EAqFd6D,GAAW9L,GAAKsG,QAAU,SAAA7D,GACrByF,GAAazF,EAAG,IAAOA,GAAKA,EAAEsJ,SAClC/L,GAAK8J,KAAOA,GAAO,KACnBgB,GAAkBkB,QAClBhM,GAAKnB,WAAY,EACjB4D,EAAID,EAAUC,GACdsG,GAAaC,GAAa,EAC1BhJ,GAAKmK,OAASnK,GAAKuJ,EAAI9G,EAAEuH,QACzBhK,GAAKoK,OAASpK,GAAKwJ,EAAI/G,EAAEwH,QACzBjK,GAAK0J,IAAIvH,QACTnC,GAAK2J,IAAIxH,QACT/D,EAAaiJ,EAAelG,EAASyK,GAAUpO,GAAY,GAAIuM,GAASxL,IAAS,GACjFyB,GAAK0I,OAAS1I,GAAK4I,OAAS,EAC5BtC,GAAWA,EAAQtG,MAEpBiM,GAAajM,GAAKuG,UAAY,SAAA9D,OACzByF,GAAazF,EAAG,IACpB/D,EAAgB2I,EAAelG,EAASyK,GAAUpO,GAAY,GAAIuM,IAAS,OACvEmC,GAAkBC,MAAMnM,GAAKwJ,EAAIxJ,GAAKoK,QACzCgC,EAAcpM,GAAKkK,WACnBmC,EAAiBD,IAAiD,EAAjC5M,KAAKuD,IAAI/C,GAAKuJ,EAAIvJ,GAAKmK,SAAgD,EAAjC3K,KAAKuD,IAAI/C,GAAKwJ,EAAIxJ,GAAKoK,SAC9FkC,EAAY9J,EAAUC,IAClB4J,GAAkBH,IACtBlM,GAAK0J,IAAIvH,QACTnC,GAAK2J,IAAIxH,QAELO,GAAkBmF,GACrBlL,GAAK4P,YAAY,IAAM,cACS,IAA3B5K,KAAasG,KAAsBxF,EAAE+J,oBACpC/J,EAAEtB,OAAOsL,MACZhK,EAAEtB,OAAOsL,aACH,GAAIb,GAASc,YAAa,KAC5BC,EAAiBf,GAASc,YAAY,eAC1CC,EAAeC,eAAe,SAAS,GAAM,EAAM7P,GAAM,EAAGuP,EAAUO,QAASP,EAAUQ,QAASR,EAAUtC,QAASsC,EAAUrC,SAAS,GAAO,GAAO,GAAO,EAAO,EAAG,MACvKxH,EAAEtB,OAAO4L,cAAcJ,OAM5B3M,GAAKkK,WAAalK,GAAKuK,YAAcvK,GAAKnB,WAAY,EACtDiH,GAAUsG,IAAgB/E,GAAgByD,GAAkBC,SAAQ,GACpE7B,IAAWzH,KACX2E,GAAagG,GAAehG,EAAUpG,IACtCuG,GAAaA,EAAUvG,GAAMqM,KAqC/BvB,GAAoB9K,GAAKgN,IAAMrQ,GAAK4P,YAAYxG,GAAe,IAnKjD,SAAbkH,aACCjN,GAAK0J,IAAIvH,QACTnC,GAAK2J,IAAIxH,QACT2I,GAAkBkB,QAClBlG,GAAUA,EAAO9F,MA+J8DgM,QAEjFhM,GAAK0I,OAAS1I,GAAK4I,OAAS,EAC5B5I,GAAK0J,IAAMpI,EAAiB,EAAG,IAAI,GACnCtB,GAAK2J,IAAMrI,EAAiB,EAAG,IAAI,GACnCtB,GAAK4K,QAAUF,GACf1K,GAAK6K,QAAUF,GACf3K,GAAKkK,WAAalK,GAAKuK,YAAcvK,GAAKnB,WAAY,EACtDpB,GAASiO,MACT1L,GAAKkN,OAAS,SAAAzK,UACRzC,GAAKmN,YACT/O,EAAauN,GAAaC,GAAWzK,EAAQ,SAAUvC,GAC7B,GAA1BP,EAAKJ,QAAQ,WAAkBG,EAAauN,GAAaC,GAAWzK,EAAQ,SAAUsJ,GAAUlM,GAASC,GAChF,GAAzBH,EAAKJ,QAAQ,UAAiBG,EAAa+C,EAAQ,QAAS6J,GAAUzM,GAASC,IACjD,GAAzBH,EAAKJ,QAAQ,UAAiBd,IAAwC,GAA3BkB,EAAKJ,QAAQ,cAC5DG,EAAa+C,EAAQ3D,GAAY,GAAIsO,GAAUvN,GAASC,GACxDJ,EAAawN,GAAUpO,GAAY,GAAIyO,IACvC7N,EAAawN,GAAUpO,GAAY,GAAIyO,IACvCpE,GAAezJ,EAAa+C,EAAQ,QAAS6G,IAAc,GAAM,GACjEL,GAAWvJ,EAAa+C,EAAQ,QAASoK,IACzCjE,GAAkBlJ,EAAawN,GAAU,eAAgBvB,IACzD9C,GAAgBnJ,EAAawN,GAAU,aAAcpB,IACrDvD,GAAW7I,EAAa+C,EAAQ/D,GAAe,QAASiO,IACxDnE,GAAc9I,EAAa+C,EAAQ/D,GAAe,QAASkO,IAC3DnE,GAAU/I,EAAa+C,EAAQ/D,GAAe,OAAQgO,KAEvDpL,GAAKmN,WAAY,EACjBnN,GAAKkK,WAAalK,GAAKuK,YAAcvK,GAAKnB,UAAYoK,GAAQC,IAAU,EACxElJ,GAAK0J,IAAIvH,QACTnC,GAAK2J,IAAIxH,QACTyI,GAAUF,KACVG,GAAUF,KACVlI,GAAKA,EAAEpE,MAAQyN,GAASrJ,GACxBgF,GAAYA,EAASzH,KAEfA,IAERA,GAAKoN,QAAU,WACVpN,GAAKmN,YAERxP,GAAW0P,OAAO,SAAAC,UAAKA,IAAMtN,IAAQ9B,EAAYoP,EAAEnM,UAASD,QAAUxC,EAAgBiN,GAAaC,GAAWzK,EAAQ,SAAUvC,GAC5HoB,GAAKnB,YACRmB,GAAK0J,IAAIvH,QACTnC,GAAK2J,IAAIxH,QACTzD,EAAgB2I,EAAelG,EAASyK,GAAUpO,GAAY,GAAIuM,IAAS,IAE5ErL,EAAgBiN,GAAaC,GAAWzK,EAAQ,SAAUsJ,GAAUjM,GACpEE,EAAgByC,EAAQ,QAAS6J,GAAUxM,GAC3CE,EAAgByC,EAAQ3D,GAAY,GAAIsO,GAAUtN,GAClDE,EAAgBkN,GAAUpO,GAAY,GAAIyO,IAC1CvN,EAAgBkN,GAAUpO,GAAY,GAAIyO,IAC1CvN,EAAgByC,EAAQ,QAAS6G,IAAc,GAC/CtJ,EAAgByC,EAAQ,QAASoK,IACjC7M,EAAgBkN,GAAU,eAAgBvB,IAC1C3L,EAAgBkN,GAAU,aAAcpB,IACxC9L,EAAgByC,EAAQ/D,GAAe,QAASiO,IAChD3M,EAAgByC,EAAQ/D,GAAe,QAASkO,IAChD5M,EAAgByC,EAAQ/D,GAAe,OAAQgO,IAC/CpL,GAAKmN,UAAYnN,GAAKnB,UAAYmB,GAAKkK,YAAa,EACpDxC,GAAaA,EAAU1H,MAIzBA,GAAKuN,KAAOvN,GAAKwN,OAAS,WACzBxN,GAAKoN,cACDvM,EAAIlD,GAAWM,QAAQ+B,IACtB,GAALa,GAAUlD,GAAW8P,OAAO5M,EAAG,GAC/BtD,KAAgByC,KAASzC,GAAc,IAGxCI,GAAWoD,KAAKf,IAChBqH,GAAgBnJ,EAAYiD,KAAY5D,GAAcyC,IAEtDA,GAAKkN,OAAOhH,8JAILwF,KAAKhC,IAAItH,2DAGTsJ,KAAK/B,IAAIvH,8CAtRLqD,QACND,KAAKC,GA0RZ1B,EAAS2J,QAAU,SACnB3J,EAAS4J,OAAS,SAAAlI,UAAQ,IAAI1B,EAAS0B,IACvC1B,EAAS6J,SAAWnK,EACpBM,EAAS8J,OAAS,kBAAMlQ,GAAWmQ,SACnC/J,EAASgK,QAAU,SAAA1E,UAAM1L,GAAW0P,OAAO,SAAAC,UAAKA,EAAE7H,KAAK4D,KAAOA,IAAI,IAElE3M,KAAcC,GAAKE,eAAekH"}