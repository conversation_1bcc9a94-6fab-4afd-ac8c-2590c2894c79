{"version": 3, "file": "MotionPathPlugin.min.js", "sources": ["../src/utils/paths.js", "../src/utils/matrix.js", "../src/MotionPathPlugin.js"], "sourcesContent": ["/*!\n * paths 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_numbersExp = /(?:(-)?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n\t_selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\n\t_DEG2RAD = Math.PI / 180,\n\t_RAD2DEG = 180 / Math.PI,\n\t_sin = Math.sin,\n\t_cos = Math.cos,\n\t_abs = Math.abs,\n\t_sqrt = Math.sqrt,\n\t_atan2 = Math.atan2,\n\t_largeNum = 1e8,\n\t_isString = value => typeof(value) === \"string\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isUndefined = value => typeof(value) === \"undefined\",\n\t_temp = {},\n\t_temp2 = {},\n\t_roundingNum = 1e5,\n\t_wrapProgress = progress => (Math.round((progress + _largeNum) % 1 * _roundingNum) / _roundingNum) || ((progress < 0) ? 0 : 1), //if progress lands on 1, the % will make it 0 which is why we || 1, but not if it's negative because it makes more sense for motion to end at 0 in that case.\n\t_round = value => (Math.round(value * _roundingNum) / _roundingNum) || 0,\n\t_roundPrecise = value => (Math.round(value * 1e10) / 1e10) || 0,\n\t_splitSegment = (rawPath, segIndex, i, t) => {\n\t\tlet segment = rawPath[segIndex],\n\t\t\tshift = t === 1 ? 6 : subdivideSegment(segment, i, t);\n\t\tif ((shift || !t) && shift + i + 2 < segment.length) {\n\t\t\trawPath.splice(segIndex, 0, segment.slice(0, i + shift + 2));\n\t\t\tsegment.splice(0, i + shift);\n\t\t\treturn 1;\n\t\t}\n\t},\n\t_getSampleIndex = (samples, length, progress) => {\n\t\t// slightly slower way than doing this (when there's no lookup): segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0;\n\t\tlet l = samples.length,\n\t\t\ti = ~~(progress * l);\n\t\tif (samples[i] > length) {\n\t\t\twhile (--i && samples[i] > length) {}\n\t\t\ti < 0 && (i = 0);\n\t\t} else {\n\t\t\twhile (samples[++i] < length && i < l) {}\n\t\t}\n\t\treturn i < l ? i : l - 1;\n\t},\n\t_reverseRawPath = (rawPath, skipOuter) => {\n\t\tlet i = rawPath.length;\n\t\tskipOuter || rawPath.reverse();\n\t\twhile (i--) {\n\t\t\trawPath[i].reversed || reverseSegment(rawPath[i]);\n\t\t}\n\t},\n\t_copyMetaData = (source, copy) => {\n\t\tcopy.totalLength = source.totalLength;\n\t\tif (source.samples) { //segment\n\t\t\tcopy.samples = source.samples.slice(0);\n\t\t\tcopy.lookup = source.lookup.slice(0);\n\t\t\tcopy.minLength = source.minLength;\n\t\t\tcopy.resolution = source.resolution;\n\t\t} else if (source.totalPoints) { //rawPath\n\t\t\tcopy.totalPoints = source.totalPoints;\n\t\t}\n\t\treturn copy;\n\t},\n\t//pushes a new segment into a rawPath, but if its starting values match the ending values of the last segment, it'll merge it into that same segment (to reduce the number of segments)\n\t_appendOrMerge = (rawPath, segment) => {\n\t\tlet index = rawPath.length,\n\t\t\tprevSeg = rawPath[index - 1] || [],\n\t\t\tl = prevSeg.length;\n\t\tif (index && segment[0] === prevSeg[l-2] && segment[1] === prevSeg[l-1]) {\n\t\t\tsegment = prevSeg.concat(segment.slice(2));\n\t\t\tindex--;\n\t\t}\n\t\trawPath[index] = segment;\n\t},\n\t_bestDistance;\n\n/* TERMINOLOGY\n - RawPath - an array of arrays, one for each Segment. A single RawPath could have multiple \"M\" commands, defining Segments (paths aren't always connected).\n - Segment - an array containing a sequence of Cubic Bezier coordinates in alternating x, y, x, y format. Starting anchor, then control point 1, control point 2, and ending anchor, then the next control point 1, control point 2, anchor, etc. Uses less memory than an array with a bunch of {x, y} points.\n - Bezier - a single cubic Bezier with a starting anchor, two control points, and an ending anchor.\n - the variable \"t\" is typically the position along an individual Bezier path (time) and it's NOT linear, meaning it could accelerate/decelerate based on the control points whereas the \"p\" or \"progress\" value is linearly mapped to the whole path, so it shouldn't really accelerate/decelerate based on control points. So a progress of 0.2 would be almost exactly 20% along the path. \"t\" is ONLY in an individual Bezier piece.\n */\n\n//accepts basic selector text, a path instance, a RawPath instance, or a Segment and returns a RawPath (makes it easy to homogenize things). If an element or selector text is passed in, it'll also cache the value so that if it's queried again, it'll just take the path data from there instead of parsing it all over again (as long as the path data itself hasn't changed - it'll check).\nexport function getRawPath(value) {\n\tvalue = (_isString(value) && _selectorExp.test(value)) ? document.querySelector(value) || value : value;\n\tlet e = value.getAttribute ? value : 0,\n\t\trawPath;\n\tif (e && (value = value.getAttribute(\"d\"))) {\n\t\t//implements caching\n\t\tif (!e._gsPath) {\n\t\t\te._gsPath = {};\n\t\t}\n\t\trawPath = e._gsPath[value];\n\t\treturn (rawPath && !rawPath._dirty) ? rawPath : (e._gsPath[value] = stringToRawPath(value));\n\t}\n\treturn !value ? console.warn(\"Expecting a <path> element or an SVG path data string\") : _isString(value) ? stringToRawPath(value) : (_isNumber(value[0])) ? [value] : value;\n}\n\n//copies a RawPath WITHOUT the length meta data (for speed)\nexport function copyRawPath(rawPath) {\n\tlet a = [],\n\t\ti = 0;\n\tfor (; i < rawPath.length; i++) {\n\t\ta[i] = _copyMetaData(rawPath[i], rawPath[i].slice(0));\n\t}\n\treturn _copyMetaData(rawPath, a);\n}\n\nexport function reverseSegment(segment) {\n\tlet i = 0,\n\t\ty;\n\tsegment.reverse(); //this will invert the order y, x, y, x so we must flip it back.\n\tfor (; i < segment.length; i += 2) {\n\t\ty = segment[i];\n\t\tsegment[i] = segment[i+1];\n\t\tsegment[i+1] = y;\n\t}\n\tsegment.reversed = !segment.reversed;\n}\n\n\n\nlet _createPath = (e, ignore) => {\n\t\tlet path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\"),\n\t\t\tattr = [].slice.call(e.attributes),\n\t\t\ti = attr.length,\n\t\t\tname;\n\t\tignore = \",\" + ignore + \",\";\n\t\twhile (--i > -1) {\n\t\t\tname = attr[i].nodeName.toLowerCase(); //in Microsoft Edge, if you don't set the attribute with a lowercase name, it doesn't render correctly! Super weird.\n\t\t\tif (ignore.indexOf(\",\" + name + \",\") < 0) {\n\t\t\t\tpath.setAttributeNS(null, name, attr[i].nodeValue);\n\t\t\t}\n\t\t}\n\t\treturn path;\n\t},\n\t_typeAttrs = {\n\t\trect:\"rx,ry,x,y,width,height\",\n\t\tcircle:\"r,cx,cy\",\n\t\tellipse:\"rx,ry,cx,cy\",\n\t\tline:\"x1,x2,y1,y2\"\n\t},\n\t_attrToObj = (e, attrs) => {\n\t\tlet props = attrs ? attrs.split(\",\") : [],\n\t\t\tobj = {},\n\t\t\ti = props.length;\n\t\twhile (--i > -1) {\n\t\t\tobj[props[i]] = +e.getAttribute(props[i]) || 0;\n\t\t}\n\t\treturn obj;\n\t};\n\n//converts an SVG shape like <circle>, <rect>, <polygon>, <polyline>, <ellipse>, etc. to a <path>, swapping it in and copying the attributes to match.\nexport function convertToPath(element, swap) {\n\tlet type = element.tagName.toLowerCase(),\n\t\tcirc = 0.552284749831,\n\t\tdata, x, y, r, ry, path, rcirc, rycirc, points, w, h, x2, x3, x4, x5, x6, y2, y3, y4, y5, y6, attr;\n\tif (type === \"path\" || !element.getBBox) {\n\t\treturn element;\n\t}\n\tpath = _createPath(element, \"x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points\");\n\tattr = _attrToObj(element, _typeAttrs[type]);\n\tif (type === \"rect\") {\n\t\tr = attr.rx;\n\t\try = attr.ry || r;\n\t\tx = attr.x;\n\t\ty = attr.y;\n\t\tw = attr.width - r * 2;\n\t\th = attr.height - ry * 2;\n\t\tif (r || ry) { //if there are rounded corners, render cubic beziers\n\t\t\tx2 = x + r * (1 - circ);\n\t\t\tx3 = x + r;\n\t\t\tx4 = x3 + w;\n\t\t\tx5 = x4 + r * circ;\n\t\t\tx6 = x4 + r;\n\t\t\ty2 = y + ry * (1 - circ);\n\t\t\ty3 = y + ry;\n\t\t\ty4 = y3 + h;\n\t\t\ty5 = y4 + ry * circ;\n\t\t\ty6 = y4 + ry;\n\t\t\tdata = \"M\" + x6 + \",\" + y3 + \" V\" + y4 + \" C\" + [x6, y5, x5, y6, x4, y6, x4 - (x4 - x3) / 3, y6, x3 + (x4 - x3) / 3, y6, x3, y6, x2, y6, x, y5, x, y4, x, y4 - (y4 - y3) / 3, x, y3 + (y4 - y3) / 3, x, y3, x, y2, x2, y, x3, y, x3 + (x4 - x3) / 3, y, x4 - (x4 - x3) / 3, y, x4, y, x5, y, x6, y2, x6, y3].join(\",\") + \"z\";\n\t\t} else {\n\t\t\tdata = \"M\" + (x + w) + \",\" + y + \" v\" + h + \" h\" + (-w) + \" v\" + (-h) + \" h\" + w + \"z\";\n\t\t}\n\n\t} else if (type === \"circle\" || type === \"ellipse\") {\n\t\tif (type === \"circle\") {\n\t\t\tr = ry = attr.r;\n\t\t\trycirc = r * circ;\n\t\t} else {\n\t\t\tr = attr.rx;\n\t\t\try = attr.ry;\n\t\t\trycirc = ry * circ;\n\t\t}\n\t\tx = attr.cx;\n\t\ty = attr.cy;\n\t\trcirc = r * circ;\n\t\tdata = \"M\" + (x+r) + \",\" + y + \" C\" + [x+r, y + rycirc, x + rcirc, y + ry, x, y + ry, x - rcirc, y + ry, x - r, y + rycirc, x - r, y, x - r, y - rycirc, x - rcirc, y - ry, x, y - ry, x + rcirc, y - ry, x + r, y - rycirc, x + r, y].join(\",\") + \"z\";\n\t} else if (type === \"line\") {\n\t\tdata = \"M\" + attr.x1 + \",\" + attr.y1 + \" L\" + attr.x2 + \",\" + attr.y2; //previously, we just converted to \"Mx,y Lx,y\" but Safari has bugs that cause that not to render properly when using a stroke-dasharray that's not fully visible! Using a cubic bezier fixes that issue.\n\t} else if (type === \"polyline\" || type === \"polygon\") {\n\t\tpoints = (element.getAttribute(\"points\") + \"\").match(_numbersExp) || [];\n\t\tx = points.shift();\n\t\ty = points.shift();\n\t\tdata = \"M\" + x + \",\" + y + \" L\" + points.join(\",\");\n\t\tif (type === \"polygon\") {\n\t\t\tdata += \",\" + x + \",\" + y + \"z\";\n\t\t}\n\t}\n\tpath.setAttribute(\"d\", rawPathToString(path._gsRawPath = stringToRawPath(data)));\n\tif (swap && element.parentNode) {\n\t\telement.parentNode.insertBefore(path, element);\n\t\telement.parentNode.removeChild(element);\n\t}\n\treturn path;\n}\n\n\n\n//returns the rotation (in degrees) at a particular progress on a rawPath (the slope of the tangent)\nexport function getRotationAtProgress(rawPath, progress) {\n\tlet d = getProgressData(rawPath, progress >= 1 ? 1 - 1e-9 : progress ? progress : 1e-9);\n\treturn getRotationAtBezierT(d.segment, d.i, d.t);\n}\n\nfunction getRotationAtBezierT(segment, i, t) {\n\tlet a = segment[i],\n\t\tb = segment[i+2],\n\t\tc = segment[i+4],\n\t\tx;\n\ta += (b - a) * t;\n\tb += (c - b) * t;\n\ta += (b - a) * t;\n\tx = b + ((c + (segment[i+6] - c) * t) - b) * t - a;\n\ta = segment[i+1];\n\tb = segment[i+3];\n\tc = segment[i+5];\n\ta += (b - a) * t;\n\tb += (c - b) * t;\n\ta += (b - a) * t;\n\treturn _round(_atan2(b + ((c + (segment[i+7] - c) * t) - b) * t - a, x) * _RAD2DEG);\n}\n\nexport function sliceRawPath(rawPath, start, end) {\n\tend = _isUndefined(end) ? 1 : _roundPrecise(end) || 0; // we must round to avoid issues like 4.15 / 8 = 0.8300000000000001 instead of 0.83 or 2.8 / 5 = 0.5599999999999999 instead of 0.56 and if someone is doing a loop like start: 2.8 / 0.5, end: 2.8 / 0.5 + 1.\n\tstart = _roundPrecise(start) || 0;\n\tlet loops = Math.max(0, ~~(_abs(end - start) - 1e-8)),\n\t\tpath = copyRawPath(rawPath);\n\tif (start > end) {\n\t\tstart = 1 - start;\n\t\tend = 1 - end;\n\t\t_reverseRawPath(path);\n\t\tpath.totalLength = 0;\n\t}\n\tif (start < 0 || end < 0) {\n\t\tlet offset = Math.abs(~~Math.min(start, end)) + 1;\n\t\tstart += offset;\n\t\tend += offset;\n\t}\n\tpath.totalLength || cacheRawPathMeasurements(path);\n\tlet wrap = (end > 1),\n\t\ts = getProgressData(path, start, _temp, true),\n\t\te = getProgressData(path, end, _temp2),\n\t\teSeg = e.segment,\n\t\tsSeg = s.segment,\n\t\teSegIndex = e.segIndex,\n\t\tsSegIndex = s.segIndex,\n\t\tei = e.i,\n\t\tsi = s.i,\n\t\tsameSegment = (sSegIndex === eSegIndex),\n\t\tsameBezier = (ei === si && sameSegment),\n\t\twrapsBehind, sShift, eShift, i, copy, totalSegments, l, j;\n\tif (wrap || loops) {\n\t\twrapsBehind = eSegIndex < sSegIndex || (sameSegment && ei < si) || (sameBezier && e.t < s.t);\n\t\tif (_splitSegment(path, sSegIndex, si, s.t)) {\n\t\t\tsSegIndex++;\n\t\t\tif (!wrapsBehind) {\n\t\t\t\teSegIndex++;\n\t\t\t\tif (sameBezier) {\n\t\t\t\t\te.t = (e.t - s.t) / (1 - s.t);\n\t\t\t\t\tei = 0;\n\t\t\t\t} else if (sameSegment) {\n\t\t\t\t\tei -= si;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (Math.abs(1 - (end - start)) < 1e-5) {\n\t\t\teSegIndex = sSegIndex - 1;\n\t\t} else if (!e.t && eSegIndex) {\n\t\t\teSegIndex--;\n\t\t} else if (_splitSegment(path, eSegIndex, ei, e.t) && wrapsBehind) {\n\t\t\tsSegIndex++;\n\t\t}\n\t\tif (s.t === 1) {\n\t\t\tsSegIndex = (sSegIndex + 1) % path.length;\n\t\t}\n\t\tcopy = [];\n\t\ttotalSegments = path.length;\n\t\tl = 1 + totalSegments * loops;\n\t\tj = sSegIndex;\n\t\tl += ((totalSegments - sSegIndex) + eSegIndex) % totalSegments;\n\t\tfor (i = 0; i < l; i++) {\n\t\t\t_appendOrMerge(copy, path[j++ % totalSegments]);\n\t\t}\n\t\tpath = copy;\n\t} else {\n\t\teShift = e.t === 1 ? 6 : subdivideSegment(eSeg, ei, e.t);\n\t\tif (start !== end) {\n\t\t\tsShift = subdivideSegment(sSeg, si, sameBezier ? s.t / e.t : s.t);\n\t\t\tsameSegment && (eShift += sShift);\n\t\t\teSeg.splice(ei + eShift + 2);\n\t\t\t(sShift || si) && sSeg.splice(0, si + sShift);\n\t\t\ti = path.length;\n\t\t\twhile (i--) {\n\t\t\t\t//chop off any extra segments\n\t\t\t\t(i < sSegIndex || i > eSegIndex) &&\tpath.splice(i, 1);\n\t\t\t}\n\t\t} else {\n\t\t\teSeg.angle = getRotationAtBezierT(eSeg, ei + eShift, 0); //record the value before we chop because it'll be impossible to determine the angle after its length is 0!\n\t\t\tei += eShift;\n\t\t\ts = eSeg[ei];\n\t\t\te = eSeg[ei+1];\n\t\t\teSeg.length = eSeg.totalLength = 0;\n\t\t\teSeg.totalPoints = path.totalPoints = 8;\n\t\t\teSeg.push(s, e, s, e, s, e, s, e);\n\t\t}\n\t}\n\tpath.totalLength = 0;\n\treturn path;\n}\n\n//measures a Segment according to its resolution (so if segment.resolution is 6, for example, it'll take 6 samples equally across each Bezier) and create/populate a \"samples\" Array that has the length up to each of those sample points (always increasing from the start) as well as a \"lookup\" array that's broken up according to the smallest distance between 2 samples. This gives us a very fast way of looking up a progress position rather than looping through all the points/Beziers. You can optionally have it only measure a subset, starting at startIndex and going for a specific number of beziers (remember, there are 3 x/y pairs each, for a total of 6 elements for each Bezier). It will also populate a \"totalLength\" property, but that's not generally super accurate because by default it'll only take 6 samples per Bezier. But for performance reasons, it's perfectly adequate for measuring progress values along the path. If you need a more accurate totalLength, either increase the resolution or use the more advanced bezierToPoints() method which keeps adding points until they don't deviate by more than a certain precision value.\nfunction measureSegment(segment, startIndex, bezierQty) {\n\tstartIndex = startIndex || 0;\n\tif (!segment.samples) {\n\t\tsegment.samples = [];\n\t\tsegment.lookup = [];\n\t}\n\tlet resolution = ~~segment.resolution || 12,\n\t\tinc = 1 / resolution,\n\t\tendIndex = bezierQty ? startIndex + bezierQty * 6 + 1 : segment.length,\n\t\tx1 = segment[startIndex],\n\t\ty1 = segment[startIndex + 1],\n\t\tsamplesIndex = startIndex ? (startIndex / 6) * resolution : 0,\n\t\tsamples = segment.samples,\n\t\tlookup = segment.lookup,\n\t\tmin = (startIndex ? segment.minLength : _largeNum) || _largeNum,\n\t\tprevLength = samples[samplesIndex + bezierQty * resolution - 1],\n\t\tlength = startIndex ? samples[samplesIndex-1] : 0,\n\t\ti, j, x4, x3, x2, xd, xd1, y4, y3, y2, yd, yd1, inv, t, lengthIndex, l, segLength;\n\tsamples.length = lookup.length = 0;\n\tfor (j = startIndex + 2; j < endIndex; j += 6) {\n\t\tx4 = segment[j + 4] - x1;\n\t\tx3 = segment[j + 2] - x1;\n\t\tx2 = segment[j] - x1;\n\t\ty4 = segment[j + 5] - y1;\n\t\ty3 = segment[j + 3] - y1;\n\t\ty2 = segment[j + 1] - y1;\n\t\txd = xd1 = yd = yd1 = 0;\n\t\tif (_abs(x4) < .01 && _abs(y4) < .01 && _abs(x2) + _abs(y2) < .01) { //dump points that are sufficiently close (basically right on top of each other, making a bezier super tiny or 0 length)\n\t\t\tif (segment.length > 8) {\n\t\t\t\tsegment.splice(j, 6);\n\t\t\t\tj -= 6;\n\t\t\t\tendIndex -= 6;\n\t\t\t}\n\t\t} else {\n\t\t\tfor (i = 1; i <= resolution; i++) {\n\t\t\t\tt = inc * i;\n\t\t\t\tinv = 1 - t;\n\t\t\t\txd = xd1 - (xd1 = (t * t * x4 + 3 * inv * (t * x3 + inv * x2)) * t);\n\t\t\t\tyd = yd1 - (yd1 = (t * t * y4 + 3 * inv * (t * y3 + inv * y2)) * t);\n\t\t\t\tl = _sqrt(yd * yd + xd * xd);\n\t\t\t\tif (l < min) {\n\t\t\t\t\tmin = l;\n\t\t\t\t}\n\t\t\t\tlength += l;\n\t\t\t\tsamples[samplesIndex++] = length;\n\t\t\t}\n\t\t}\n\t\tx1 += x4;\n\t\ty1 += y4;\n\t}\n\tif (prevLength) {\n\t\tprevLength -= length;\n\t\tfor (; samplesIndex < samples.length; samplesIndex++) {\n\t\t\tsamples[samplesIndex] += prevLength;\n\t\t}\n\t}\n\tif (samples.length && min) {\n\t\tsegment.totalLength = segLength = samples[samples.length-1] || 0;\n\t\tsegment.minLength = min;\n\t\tif (segLength / min < 9999) { // if the lookup would require too many values (memory problem), we skip this and instead we use a loop to lookup values directly in the samples Array\n\t\t\tl = lengthIndex = 0;\n\t\t\tfor (i = 0; i < segLength; i += min) {\n\t\t\t\tlookup[l++] = (samples[lengthIndex] < i) ? ++lengthIndex : lengthIndex;\n\t\t\t}\n\t\t}\n\t} else {\n\t\tsegment.totalLength = samples[0] = 0;\n\t}\n\treturn startIndex ? length - samples[startIndex / 2 - 1] : length;\n}\n\nexport function cacheRawPathMeasurements(rawPath, resolution) {\n\tlet pathLength, points, i;\n\tfor (i = pathLength = points = 0; i < rawPath.length; i++) {\n\t\trawPath[i].resolution = ~~resolution || 12; //steps per Bezier curve (anchor, 2 control points, to anchor)\n\t\tpoints += rawPath[i].length;\n\t\tpathLength += measureSegment(rawPath[i]);\n\t}\n\trawPath.totalPoints = points;\n\trawPath.totalLength = pathLength;\n\treturn rawPath;\n}\n\n//divide segment[i] at position t (value between 0 and 1, progress along that particular cubic bezier segment that starts at segment[i]). Returns how many elements were spliced into the segment array (either 0 or 6)\nexport function subdivideSegment(segment, i, t) {\n\tif (t <= 0 || t >= 1) {\n\t\treturn 0;\n\t}\n\tlet ax = segment[i],\n\t\tay = segment[i+1],\n\t\tcp1x = segment[i+2],\n\t\tcp1y = segment[i+3],\n\t\tcp2x = segment[i+4],\n\t\tcp2y = segment[i+5],\n\t\tbx = segment[i+6],\n\t\tby = segment[i+7],\n\t\tx1a = ax + (cp1x - ax) * t,\n\t\tx2 = cp1x + (cp2x - cp1x) * t,\n\t\ty1a = ay + (cp1y - ay) * t,\n\t\ty2 = cp1y + (cp2y - cp1y) * t,\n\t\tx1 = x1a + (x2 - x1a) * t,\n\t\ty1 = y1a + (y2 - y1a) * t,\n\t\tx2a = cp2x + (bx - cp2x) * t,\n\t\ty2a = cp2y + (by - cp2y) * t;\n\tx2 += (x2a - x2) * t;\n\ty2 += (y2a - y2) * t;\n\tsegment.splice(i + 2, 4,\n\t\t_round(x1a),                  //first control point\n\t\t_round(y1a),\n\t\t_round(x1),                   //second control point\n\t\t_round(y1),\n\t\t_round(x1 + (x2 - x1) * t),   //new fabricated anchor on line\n\t\t_round(y1 + (y2 - y1) * t),\n\t\t_round(x2),                   //third control point\n\t\t_round(y2),\n\t\t_round(x2a),                  //fourth control point\n\t\t_round(y2a)\n\t);\n\tsegment.samples && segment.samples.splice(((i / 6) * segment.resolution) | 0, 0, 0, 0, 0, 0, 0, 0);\n\treturn 6;\n}\n\n// returns an object {path, segment, segIndex, i, t}\nfunction getProgressData(rawPath, progress, decoratee, pushToNextIfAtEnd) {\n\tdecoratee = decoratee || {};\n\trawPath.totalLength || cacheRawPathMeasurements(rawPath);\n\tif (progress < 0 || progress > 1) {\n\t\tprogress = _wrapProgress(progress);\n\t}\n\tlet segIndex = 0,\n\t\tsegment = rawPath[0],\n\t\tsamples, resolution, length, min, max, i, t;\n\tif (!progress) {\n\t\tt = i = segIndex = 0;\n\t\tsegment = rawPath[0];\n\t} else if (progress === 1) {\n\t\tt = 1;\n\t\tsegIndex = rawPath.length - 1;\n\t\tsegment = rawPath[segIndex];\n\t\ti = segment.length - 8;\n\t} else {\n\t\tif (rawPath.length > 1) { //speed optimization: most of the time, there's only one segment so skip the recursion.\n\t\t\tlength = rawPath.totalLength * progress;\n\t\t\tmax = i = 0;\n\t\t\twhile ((max += rawPath[i++].totalLength) < length) {\n\t\t\t\tsegIndex = i;\n\t\t\t}\n\t\t\tsegment = rawPath[segIndex];\n\t\t\tmin = max - segment.totalLength;\n\t\t\tprogress = ((length - min) / (max - min)) || 0;\n\t\t}\n\t\tsamples = segment.samples;\n\t\tresolution = segment.resolution; //how many samples per cubic bezier chunk\n\t\tlength = segment.totalLength * progress;\n\t\ti = segment.lookup.length ? segment.lookup[~~(length / segment.minLength)] || 0 : _getSampleIndex(samples, length, progress);\n\t\tmin = i ? samples[i-1] : 0;\n\t\tmax = samples[i];\n\t\tif (max < length) {\n\t\t\tmin = max;\n\t\t\tmax = samples[++i];\n\t\t}\n\t\tt = (1 / resolution) * (((length - min) / (max - min)) + ((i % resolution)));\n\t\ti = ~~(i / resolution) * 6;\n\t\tif (pushToNextIfAtEnd && t === 1) {\n\t\t\tif (i + 6 < segment.length) {\n\t\t\t\ti += 6;\n\t\t\t\tt = 0;\n\t\t\t} else if (segIndex + 1 < rawPath.length) {\n\t\t\t\ti = t = 0;\n\t\t\t\tsegment = rawPath[++segIndex];\n\t\t\t}\n\t\t}\n\t}\n\tdecoratee.t = t;\n\tdecoratee.i = i;\n\tdecoratee.path = rawPath;\n\tdecoratee.segment = segment;\n\tdecoratee.segIndex = segIndex;\n\treturn decoratee;\n}\n\nexport function getPositionOnPath(rawPath, progress, includeAngle, point) {\n\tlet segment = rawPath[0],\n\t\tresult = point || {},\n\t\tsamples, resolution, length, min, max, i, t, a, inv;\n\tif (progress < 0 || progress > 1) {\n\t\tprogress = _wrapProgress(progress);\n\t}\n\tsegment.lookup || cacheRawPathMeasurements(rawPath);\n\tif (rawPath.length > 1) { //speed optimization: most of the time, there's only one segment so skip the recursion.\n\t\tlength = rawPath.totalLength * progress;\n\t\tmax = i = 0;\n\t\twhile ((max += rawPath[i++].totalLength) < length) {\n\t\t\tsegment = rawPath[i];\n\t\t}\n\t\tmin = max - segment.totalLength;\n\t\tprogress = ((length - min) / (max - min)) || 0;\n\t}\n\tsamples = segment.samples;\n\tresolution = segment.resolution;\n\tlength = segment.totalLength * progress;\n\ti = segment.lookup.length ? segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0 : _getSampleIndex(samples, length, progress);\n\tmin = i ? samples[i-1] : 0;\n\tmax = samples[i];\n\tif (max < length) {\n\t\tmin = max;\n\t\tmax = samples[++i];\n\t}\n\tt = ((1 / resolution) * (((length - min) / (max - min)) + ((i % resolution)))) || 0;\n\tinv = 1 - t;\n\ti = ~~(i / resolution) * 6;\n\ta = segment[i];\n\tresult.x = _round((t * t * (segment[i + 6] - a) + 3 * inv * (t * (segment[i + 4] - a) + inv * (segment[i + 2] - a))) * t + a);\n\tresult.y = _round((t * t * (segment[i + 7] - (a = segment[i+1])) + 3 * inv * (t * (segment[i + 5] - a) + inv * (segment[i + 3] - a))) * t + a);\n\tif (includeAngle) {\n\t\tresult.angle = segment.totalLength ? getRotationAtBezierT(segment, i, t >= 1 ? 1 - 1e-9 : t ? t : 1e-9) : segment.angle || 0;\n\t}\n\treturn result;\n}\n\n\n\n//applies a matrix transform to RawPath (or a segment in a RawPath) and returns whatever was passed in (it transforms the values in the array(s), not a copy).\nexport function transformRawPath(rawPath, a, b, c, d, tx, ty) {\n\tlet j = rawPath.length,\n\t\tsegment, l, i, x, y;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tl = segment.length;\n\t\tfor (i = 0; i < l; i += 2) {\n\t\t\tx = segment[i];\n\t\t\ty = segment[i+1];\n\t\t\tsegment[i] = x * a + y * c + tx;\n\t\t\tsegment[i+1] = x * b + y * d + ty;\n\t\t}\n\t}\n\trawPath._dirty = 1;\n\treturn rawPath;\n}\n\n\n\n// translates SVG arc data into a segment (cubic beziers). Angle is in degrees.\nfunction arcToSegment(lastX, lastY, rx, ry, angle, largeArcFlag, sweepFlag, x, y) {\n\tif (lastX === x && lastY === y) {\n\t\treturn;\n\t}\n\trx = _abs(rx);\n\try = _abs(ry);\n\tlet angleRad = (angle % 360) * _DEG2RAD,\n\t\tcosAngle = _cos(angleRad),\n\t\tsinAngle = _sin(angleRad),\n\t\tPI = Math.PI,\n\t\tTWOPI = PI * 2,\n\t\tdx2 = (lastX - x) / 2,\n\t\tdy2 = (lastY - y) / 2,\n\t\tx1 = (cosAngle * dx2 + sinAngle * dy2),\n\t\ty1 = (-sinAngle * dx2 + cosAngle * dy2),\n\t\tx1_sq = x1 * x1,\n\t\ty1_sq = y1 * y1,\n\t\tradiiCheck = x1_sq / (rx * rx) + y1_sq / (ry * ry);\n\tif (radiiCheck > 1) {\n\t\trx = _sqrt(radiiCheck) * rx;\n\t\try = _sqrt(radiiCheck) * ry;\n\t}\n\tlet rx_sq = rx * rx,\n\t\try_sq = ry * ry,\n\t\tsq = ((rx_sq * ry_sq) - (rx_sq * y1_sq) - (ry_sq * x1_sq)) / ((rx_sq * y1_sq) + (ry_sq * x1_sq));\n\tif (sq < 0) {\n\t\tsq = 0;\n\t}\n\tlet coef = ((largeArcFlag === sweepFlag) ? -1 : 1) * _sqrt(sq),\n\t\tcx1 = coef * ((rx * y1) / ry),\n\t\tcy1 = coef * -((ry * x1) / rx),\n\t\tsx2 = (lastX + x) / 2,\n\t\tsy2 = (lastY + y) / 2,\n\t\tcx = sx2 + (cosAngle * cx1 - sinAngle * cy1),\n\t\tcy = sy2 + (sinAngle * cx1 + cosAngle * cy1),\n\t\tux = (x1 - cx1) / rx,\n\t\tuy = (y1 - cy1) / ry,\n\t\tvx = (-x1 - cx1) / rx,\n\t\tvy = (-y1 - cy1) / ry,\n\t\ttemp = ux * ux + uy * uy,\n\t\tangleStart = ((uy < 0) ? -1 : 1) * Math.acos(ux / _sqrt(temp)),\n\t\tangleExtent = ((ux * vy - uy * vx < 0) ? -1 : 1) * Math.acos((ux * vx + uy * vy) / _sqrt(temp * (vx * vx + vy * vy)));\n\tisNaN(angleExtent) && (angleExtent = PI); //rare edge case. Math.cos(-1) is NaN.\n\tif (!sweepFlag && angleExtent > 0) {\n\t\tangleExtent -= TWOPI;\n\t} else if (sweepFlag && angleExtent < 0) {\n\t\tangleExtent += TWOPI;\n\t}\n\tangleStart %= TWOPI;\n\tangleExtent %= TWOPI;\n\tlet segments = Math.ceil(_abs(angleExtent) / (TWOPI / 4)),\n\t\trawPath = [],\n\t\tangleIncrement = angleExtent / segments,\n\t\tcontrolLength = 4 / 3 * _sin(angleIncrement / 2) / (1 + _cos(angleIncrement / 2)),\n\t\tma = cosAngle * rx,\n\t\tmb = sinAngle * rx,\n\t\tmc = sinAngle * -ry,\n\t\tmd = cosAngle * ry,\n\t\ti;\n\tfor (i = 0; i < segments; i++) {\n\t\tangle = angleStart + i * angleIncrement;\n\t\tx1 = _cos(angle);\n\t\ty1 = _sin(angle);\n\t\tux = _cos(angle += angleIncrement);\n\t\tuy = _sin(angle);\n\t\trawPath.push(x1 - controlLength * y1, y1 + controlLength * x1, ux + controlLength * uy, uy - controlLength * ux, ux, uy);\n\t}\n\t//now transform according to the actual size of the ellipse/arc (the beziers were noramlized, between 0 and 1 on a circle).\n\tfor (i = 0; i < rawPath.length; i+=2) {\n\t\tx1 = rawPath[i];\n\t\ty1 = rawPath[i+1];\n\t\trawPath[i] = x1 * ma + y1 * mc + cx;\n\t\trawPath[i+1] = x1 * mb + y1 * md + cy;\n\t}\n\trawPath[i-2] = x; //always set the end to exactly where it's supposed to be\n\trawPath[i-1] = y;\n\treturn rawPath;\n}\n\n//Spits back a RawPath with absolute coordinates. Each segment starts with a \"moveTo\" command (x coordinate, then y) and then 2 control points (x, y, x, y), then anchor. The goal is to minimize memory and maximize speed.\nexport function stringToRawPath(d) {\n\tlet a = (d + \"\").replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp) || [], //some authoring programs spit out very small numbers in scientific notation like \"1e-5\", so make sure we round that down to 0 first.\n\t\tpath = [],\n\t\trelativeX = 0,\n\t\trelativeY = 0,\n\t\ttwoThirds = 2 / 3,\n\t\telements = a.length,\n\t\tpoints = 0,\n\t\terrorMessage = \"ERROR: malformed path: \" + d,\n\t\ti, j, x, y, command, isRelative, segment, startX, startY, difX, difY, beziers, prevCommand, flag1, flag2,\n\t\tline = function(sx, sy, ex, ey) {\n\t\t\tdifX = (ex - sx) / 3;\n\t\t\tdifY = (ey - sy) / 3;\n\t\t\tsegment.push(sx + difX, sy + difY, ex - difX, ey - difY, ex, ey);\n\t\t};\n\tif (!d || !isNaN(a[0]) || isNaN(a[1])) {\n\t\tconsole.log(errorMessage);\n\t\treturn path;\n\t}\n\tfor (i = 0; i < elements; i++) {\n\t\tprevCommand = command;\n\t\tif (isNaN(a[i])) {\n\t\t\tcommand = a[i].toUpperCase();\n\t\t\tisRelative = (command !== a[i]); //lower case means relative\n\t\t} else { //commands like \"C\" can be strung together without any new command characters between.\n\t\t\ti--;\n\t\t}\n\t\tx = +a[i + 1];\n\t\ty = +a[i + 2];\n\t\tif (isRelative) {\n\t\t\tx += relativeX;\n\t\t\ty += relativeY;\n\t\t}\n\t\tif (!i) {\n\t\t\tstartX = x;\n\t\t\tstartY = y;\n\t\t}\n\n\t\t// \"M\" (move)\n\t\tif (command === \"M\") {\n\t\t\tif (segment) {\n\t\t\t\tif (segment.length < 8) { //if the path data was funky and just had a M with no actual drawing anywhere, skip it.\n\t\t\t\t\tpath.length -= 1;\n\t\t\t\t} else {\n\t\t\t\t\tpoints += segment.length;\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = startX = x;\n\t\t\trelativeY = startY = y;\n\t\t\tsegment = [x, y];\n\t\t\tpath.push(segment);\n\t\t\ti += 2;\n\t\t\tcommand = \"L\"; //an \"M\" with more than 2 values gets interpreted as \"lineTo\" commands (\"L\").\n\n\t\t// \"C\" (cubic bezier)\n\t\t} else if (command === \"C\") {\n\t\t\tif (!segment) {\n\t\t\t\tsegment = [0, 0];\n\t\t\t}\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\t//note: \"*1\" is just a fast/short way to cast the value as a Number. WAAAY faster in Chrome, slightly slower in Firefox.\n\t\t\tsegment.push(x,\ty, relativeX + a[i + 3] * 1, relativeY + a[i + 4] * 1, (relativeX += a[i + 5] * 1),\t(relativeY += a[i + 6] * 1));\n\t\t\ti += 6;\n\n\t\t// \"S\" (continuation of cubic bezier)\n\t\t} else if (command === \"S\") {\n\t\t\tdifX = relativeX;\n\t\t\tdifY = relativeY;\n\t\t\tif (prevCommand === \"C\" || prevCommand === \"S\") {\n\t\t\t\tdifX += relativeX - segment[segment.length - 4];\n\t\t\t\tdifY += relativeY - segment[segment.length - 3];\n\t\t\t}\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\tsegment.push(difX, difY, x,\ty, (relativeX += a[i + 3] * 1), (relativeY += a[i + 4] * 1));\n\t\t\ti += 4;\n\n\t\t// \"Q\" (quadratic bezier)\n\t\t} else if (command === \"Q\") {\n\t\t\tdifX = relativeX + (x - relativeX) * twoThirds;\n\t\t\tdifY = relativeY + (y - relativeY) * twoThirds;\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\trelativeX += a[i + 3] * 1;\n\t\t\trelativeY += a[i + 4] * 1;\n\t\t\tsegment.push(difX, difY, relativeX + (x - relativeX) * twoThirds, relativeY + (y - relativeY) * twoThirds, relativeX, relativeY);\n\t\t\ti += 4;\n\n\t\t// \"T\" (continuation of quadratic bezier)\n\t\t} else if (command === \"T\") {\n\t\t\tdifX = relativeX - segment[segment.length - 4];\n\t\t\tdifY = relativeY - segment[segment.length - 3];\n\t\t\tsegment.push(relativeX + difX, relativeY + difY, x + ((relativeX + difX * 1.5) - x) * twoThirds, y + ((relativeY + difY * 1.5) - y) * twoThirds, (relativeX = x), (relativeY = y));\n\t\t\ti += 2;\n\n\t\t// \"H\" (horizontal line)\n\t\t} else if (command === \"H\") {\n\t\t\tline(relativeX, relativeY, (relativeX = x), relativeY);\n\t\t\ti += 1;\n\n\t\t// \"V\" (vertical line)\n\t\t} else if (command === \"V\") {\n\t\t\t//adjust values because the first (and only one) isn't x in this case, it's y.\n\t\t\tline(relativeX, relativeY, relativeX, (relativeY = x + (isRelative ? relativeY - relativeX : 0)));\n\t\t\ti += 1;\n\n\t\t// \"L\" (line) or \"Z\" (close)\n\t\t} else if (command === \"L\" || command === \"Z\") {\n\t\t\tif (command === \"Z\") {\n\t\t\t\tx = startX;\n\t\t\t\ty = startY;\n\t\t\t\tsegment.closed = true;\n\t\t\t}\n\t\t\tif (command === \"L\" || _abs(relativeX - x) > 0.5 || _abs(relativeY - y) > 0.5) {\n\t\t\t\tline(relativeX, relativeY, x, y);\n\t\t\t\tif (command === \"L\") {\n\t\t\t\t\ti += 2;\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = x;\n\t\t\trelativeY = y;\n\n\t\t// \"A\" (arc)\n\t\t} else if (command === \"A\") {\n\t\t\tflag1 = a[i+4];\n\t\t\tflag2 = a[i+5];\n\t\t\tdifX = a[i+6];\n\t\t\tdifY = a[i+7];\n\t\t\tj = 7;\n\t\t\tif (flag1.length > 1) { // for cases when the flags are merged, like \"a8 8 0 018 8\" (the 0 and 1 flags are WITH the x value of 8, but it could also be \"a8 8 0 01-8 8\" so it may include x or not)\n\t\t\t\tif (flag1.length < 3) {\n\t\t\t\t\tdifY = difX;\n\t\t\t\t\tdifX = flag2;\n\t\t\t\t\tj--;\n\t\t\t\t} else {\n\t\t\t\t\tdifY = flag2;\n\t\t\t\t\tdifX = flag1.substr(2);\n\t\t\t\t\tj-=2;\n\t\t\t\t}\n\t\t\t\tflag2 = flag1.charAt(1);\n\t\t\t\tflag1 = flag1.charAt(0);\n\t\t\t}\n\t\t\tbeziers = arcToSegment(relativeX, relativeY, +a[i+1], +a[i+2], +a[i+3], +flag1, +flag2, (isRelative ? relativeX : 0) + difX*1, (isRelative ? relativeY : 0) + difY*1);\n\t\t\ti += j;\n\t\t\tif (beziers) {\n\t\t\t\tfor (j = 0; j < beziers.length; j++) {\n\t\t\t\t\tsegment.push(beziers[j]);\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = segment[segment.length-2];\n\t\t\trelativeY = segment[segment.length-1];\n\n\t\t} else {\n\t\t\tconsole.log(errorMessage);\n\t\t}\n\t}\n\ti = segment.length;\n\tif (i < 6) { //in case there's odd SVG like a M0,0 command at the very end.\n\t\tpath.pop();\n\t\ti = 0;\n\t} else if (segment[0] === segment[i-2] && segment[1] === segment[i-1]) {\n\t\tsegment.closed = true;\n\t}\n\tpath.totalPoints = points + i;\n\treturn path;\n}\n\n//populates the points array in alternating x/y values (like [x, y, x, y...] instead of individual point objects [{x, y}, {x, y}...] to conserve memory and stay in line with how we're handling segment arrays\nexport function bezierToPoints(x1, y1, x2, y2, x3, y3, x4, y4, threshold, points, index) {\n\tlet x12 = (x1 + x2) / 2,\n\t\ty12 = (y1 + y2) / 2,\n\t\tx23 = (x2 + x3) / 2,\n\t\ty23 = (y2 + y3) / 2,\n\t\tx34 = (x3 + x4) / 2,\n\t\ty34 = (y3 + y4) / 2,\n\t\tx123 = (x12 + x23) / 2,\n\t\ty123 = (y12 + y23) / 2,\n\t\tx234 = (x23 + x34) / 2,\n\t\ty234 = (y23 + y34) / 2,\n\t\tx1234 = (x123 + x234) / 2,\n\t\ty1234 = (y123 + y234) / 2,\n\t\tdx = x4 - x1,\n\t\tdy = y4 - y1,\n\t\td2 = _abs((x2 - x4) * dy - (y2 - y4) * dx),\n\t\td3 = _abs((x3 - x4) * dy - (y3 - y4) * dx),\n\t\tlength;\n\tif (!points) {\n\t\tpoints = [x1, y1, x4, y4];\n\t\tindex = 2;\n\t}\n\tpoints.splice(index || points.length - 2, 0, x1234, y1234);\n\tif ((d2 + d3) * (d2 + d3) > threshold * (dx * dx + dy * dy)) {\n\t\tlength = points.length;\n\t\tbezierToPoints(x1, y1, x12, y12, x123, y123, x1234, y1234, threshold, points, index);\n\t\tbezierToPoints(x1234, y1234, x234, y234, x34, y34, x4, y4, threshold, points, index + 2 + (points.length - length));\n\t}\n\treturn points;\n}\n\n/*\nfunction getAngleBetweenPoints(x0, y0, x1, y1, x2, y2) { //angle between 3 points in radians\n\tvar dx1 = x1 - x0,\n\t\tdy1 = y1 - y0,\n\t\tdx2 = x2 - x1,\n\t\tdy2 = y2 - y1,\n\t\tdx3 = x2 - x0,\n\t\tdy3 = y2 - y0,\n\t\ta = dx1 * dx1 + dy1 * dy1,\n\t\tb = dx2 * dx2 + dy2 * dy2,\n\t\tc = dx3 * dx3 + dy3 * dy3;\n\treturn Math.acos( (a + b - c) / _sqrt(4 * a * b) );\n},\n*/\n\n//pointsToSegment() doesn't handle flat coordinates (where y is always 0) the way we need (the resulting control points are always right on top of the anchors), so this function basically makes the control points go directly up and down, varying in length based on the curviness (more curvy, further control points)\nexport function flatPointsToSegment(points, curviness=1) {\n\tlet x = points[0],\n\t\ty = 0,\n\t\tsegment = [x, y],\n\t\ti = 2;\n\tfor (; i < points.length; i+=2) {\n\t\tsegment.push(\n\t\t\tx,\n\t\t\ty,\n\t\t\tpoints[i],\n\t\t\t(y = (points[i] - x) * curviness / 2),\n\t\t\t(x = points[i]),\n\t\t\t-y\n\t\t);\n\t}\n\treturn segment;\n}\n\n//points is an array of x/y points, like [x, y, x, y, x, y]\nexport function pointsToSegment(points, curviness) {\n\t//points = simplifyPoints(points, tolerance);\n\t_abs(points[0] - points[2]) < 1e-4 && _abs(points[1] - points[3]) < 1e-4 && (points = points.slice(2)); // if the first two points are super close, dump the first one.\n\tlet l = points.length-2,\n\t\tx = +points[0],\n\t\ty = +points[1],\n\t\tnextX = +points[2],\n\t\tnextY = +points[3],\n\t\tsegment = [x, y, x, y],\n\t\tdx2 = nextX - x,\n\t\tdy2 = nextY - y,\n\t\tclosed = Math.abs(points[l] - x) < 0.001 && Math.abs(points[l+1] - y) < 0.001,\n\t\tprevX, prevY, i, dx1, dy1, r1, r2, r3, tl, mx1, mx2, mxm, my1, my2, mym;\n\tif (closed) { // if the start and end points are basically on top of each other, close the segment by adding the 2nd point to the end, and the 2nd-to-last point to the beginning (we'll remove them at the end, but this allows the curvature to look perfect)\n\t\tpoints.push(nextX, nextY);\n\t\tnextX = x;\n\t\tnextY = y;\n\t\tx = points[l-2];\n\t\ty = points[l-1];\n\t\tpoints.unshift(x, y);\n\t\tl+=4;\n\t}\n\tcurviness = (curviness || curviness === 0) ? +curviness : 1;\n\tfor (i = 2; i < l; i+=2) {\n\t\tprevX = x;\n\t\tprevY = y;\n\t\tx = nextX;\n\t\ty = nextY;\n\t\tnextX = +points[i+2];\n\t\tnextY = +points[i+3];\n\t\tif (x === nextX && y === nextY) {\n\t\t\tcontinue;\n\t\t}\n\t\tdx1 = dx2;\n\t\tdy1 = dy2;\n\t\tdx2 = nextX - x;\n\t\tdy2 = nextY - y;\n\t\tr1 = _sqrt(dx1 * dx1 + dy1 * dy1); // r1, r2, and r3 correlate x and y (and z in the future). Basically 2D or 3D hypotenuse\n\t\tr2 = _sqrt(dx2 * dx2 + dy2 * dy2);\n\t\tr3 =  _sqrt((dx2 / r2 + dx1 / r1) ** 2 + (dy2 / r2 + dy1 / r1) ** 2);\n\t\ttl = ((r1 + r2) * curviness * 0.25) / r3;\n\t\tmx1 = x - (x - prevX) * (r1 ? tl / r1 : 0);\n\t\tmx2 = x + (nextX - x) * (r2 ? tl / r2 : 0);\n\t\tmxm = x - (mx1 + (((mx2 - mx1) * ((r1 * 3 / (r1 + r2)) + 0.5) / 4) || 0));\n\t\tmy1 = y - (y - prevY) * (r1 ? tl / r1 : 0);\n\t\tmy2 = y + (nextY - y) * (r2 ? tl / r2 : 0);\n\t\tmym = y - (my1 + (((my2 - my1) * ((r1 * 3 / (r1 + r2)) + 0.5) / 4) || 0));\n\t\tif (x !== prevX || y !== prevY) {\n\t\t\tsegment.push(\n\t\t\t\t_round(mx1 + mxm),  // first control point\n\t\t\t\t_round(my1 + mym),\n\t\t\t\t_round(x),          // anchor\n\t\t\t\t_round(y),\n\t\t\t\t_round(mx2 + mxm),  // second control point\n\t\t\t\t_round(my2 + mym)\n\t\t\t);\n\t\t}\n\t}\n\tx !== nextX || y !== nextY || segment.length < 4 ? segment.push(_round(nextX), _round(nextY), _round(nextX), _round(nextY)) : (segment.length -= 2);\n\tif (segment.length === 2) { // only one point!\n\t\tsegment.push(x, y, x, y, x, y);\n\t} else if (closed) {\n\t\tsegment.splice(0, 6);\n\t\tsegment.length = segment.length - 6;\n\t}\n\treturn segment;\n}\n\n//returns the squared distance between an x/y coordinate and a segment between x1/y1 and x2/y2\nfunction pointToSegDist(x, y, x1, y1, x2, y2) {\n\tlet dx = x2 - x1,\n\t\tdy = y2 - y1,\n\t\tt;\n\tif (dx || dy) {\n\t\tt = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n\t\tif (t > 1) {\n\t\t\tx1 = x2;\n\t\t\ty1 = y2;\n\t\t} else if (t > 0) {\n\t\t\tx1 += dx * t;\n\t\t\ty1 += dy * t;\n\t\t}\n\t}\n\treturn (x - x1) ** 2 + (y - y1) ** 2;\n}\n\nfunction simplifyStep(points, first, last, tolerance, simplified) {\n\tlet maxSqDist = tolerance,\n\t\tfirstX = points[first],\n\t\tfirstY = points[first+1],\n\t\tlastX = points[last],\n\t\tlastY = points[last+1],\n\t\tindex, i, d;\n\tfor (i = first + 2; i < last; i += 2) {\n\t\td = pointToSegDist(points[i], points[i+1], firstX, firstY, lastX, lastY);\n\t\tif (d > maxSqDist) {\n\t\t\tindex = i;\n\t\t\tmaxSqDist = d;\n\t\t}\n\t}\n\tif (maxSqDist > tolerance) {\n\t\tindex - first > 2 && simplifyStep(points, first, index, tolerance, simplified);\n\t\tsimplified.push(points[index], points[index+1]);\n\t\tlast - index > 2 && simplifyStep(points, index, last, tolerance, simplified);\n\t}\n}\n\n//points is an array of x/y values like [x, y, x, y, x, y]\nexport function simplifyPoints(points, tolerance) {\n\tlet prevX = parseFloat(points[0]),\n\t\tprevY = parseFloat(points[1]),\n\t\ttemp = [prevX, prevY],\n\t\tl = points.length - 2,\n\t\ti, x, y, dx, dy, result, last;\n\ttolerance = (tolerance || 1) ** 2;\n\tfor (i = 2; i < l; i += 2) {\n\t\tx = parseFloat(points[i]);\n\t\ty = parseFloat(points[i+1]);\n\t\tdx = prevX - x;\n\t\tdy = prevY - y;\n\t\tif (dx * dx + dy * dy > tolerance) {\n\t\t\ttemp.push(x, y);\n\t\t\tprevX = x;\n\t\t\tprevY = y;\n\t\t}\n\t}\n\ttemp.push(parseFloat(points[l]), parseFloat(points[l+1]));\n\tlast = temp.length - 2;\n\tresult = [temp[0], temp[1]];\n\tsimplifyStep(temp, 0, last, tolerance, result);\n\tresult.push(temp[last], temp[last+1]);\n\treturn result;\n}\n\nfunction getClosestProgressOnBezier(iterations, px, py, start, end, slices, x0, y0, x1, y1, x2, y2, x3, y3) {\n\tlet inc = (end - start) / slices,\n\t\tbest = 0,\n\t\tt = start,\n\t\tx, y, d, dx, dy, inv;\n\t_bestDistance = _largeNum;\n\twhile (t <= end) {\n\t\tinv = 1 - t;\n\t\tx = inv * inv * inv * x0 + 3 * inv * inv * t * x1 + 3 * inv * t * t * x2 + t * t * t * x3;\n\t\ty = inv * inv * inv * y0 + 3 * inv * inv * t * y1 + 3 * inv * t * t * y2 + t * t * t * y3;\n\t\tdx = x - px;\n\t\tdy = y - py;\n\t\td = dx * dx + dy * dy;\n\t\tif (d < _bestDistance) {\n\t\t\t_bestDistance = d;\n\t\t\tbest = t;\n\t\t}\n\t\tt += inc;\n\t}\n\treturn (iterations > 1) ? getClosestProgressOnBezier(iterations - 1, px, py, Math.max(best - inc, 0), Math.min(best + inc, 1), slices, x0, y0, x1, y1, x2, y2, x3, y3) : best;\n}\n\nexport function getClosestData(rawPath, x, y, slices) { //returns an object with the closest j, i, and t (j is the segment index, i is the index of the point in that segment, and t is the time/progress along that bezier)\n\tlet closest = {j:0, i:0, t:0},\n\t\tbestDistance = _largeNum,\n\t\ti, j, t, segment;\n\tfor (j = 0; j < rawPath.length; j++) {\n\t\tsegment = rawPath[j];\n\t\tfor (i = 0; i < segment.length; i+=6) {\n\t\t\tt = getClosestProgressOnBezier(1, x, y, 0, 1, slices || 20, segment[i], segment[i+1], segment[i+2], segment[i+3], segment[i+4], segment[i+5], segment[i+6], segment[i+7]);\n\t\t\tif (bestDistance > _bestDistance) {\n\t\t\t\tbestDistance = _bestDistance;\n\t\t\t\tclosest.j = j;\n\t\t\t\tclosest.i = i;\n\t\t\t\tclosest.t = t;\n\t\t\t}\n\t\t}\n\t}\n\treturn closest;\n}\n\n//subdivide a Segment closest to a specific x,y coordinate\nexport function subdivideSegmentNear(x, y, segment, slices, iterations) {\n\tlet l = segment.length,\n\t\tbestDistance = _largeNum,\n\t\tbestT = 0,\n\t\tbestSegmentIndex = 0,\n\t\tt, i;\n\tslices = slices || 20;\n\titerations = iterations || 3;\n\tfor (i = 0; i < l; i += 6) {\n\t\tt = getClosestProgressOnBezier(1, x, y, 0, 1, slices, segment[i], segment[i+1], segment[i+2], segment[i+3], segment[i+4], segment[i+5], segment[i+6], segment[i+7]);\n\t\tif (bestDistance > _bestDistance) {\n\t\t\tbestDistance = _bestDistance;\n\t\t\tbestT = t;\n\t\t\tbestSegmentIndex = i;\n\t\t}\n\t}\n\tt = getClosestProgressOnBezier(iterations, x, y, bestT - 0.05, bestT + 0.05, slices, segment[bestSegmentIndex], segment[bestSegmentIndex+1], segment[bestSegmentIndex+2], segment[bestSegmentIndex+3], segment[bestSegmentIndex+4], segment[bestSegmentIndex+5], segment[bestSegmentIndex+6], segment[bestSegmentIndex+7]);\n\tsubdivideSegment(segment, bestSegmentIndex, t);\n\treturn bestSegmentIndex + 6;\n}\n\n/*\nTakes any of the following and converts it to an all Cubic Bezier SVG data string:\n- A <path> data string like \"M0,0 L2,4 v20,15 H100\"\n- A RawPath, like [[x, y, x, y, x, y, x, y][[x, y, x, y, x, y, x, y]]\n- A Segment, like [x, y, x, y, x, y, x, y]\n\nNote: all numbers are rounded down to the closest 0.001 to minimize memory, maximize speed, and avoid odd numbers like 1e-13\n*/\nexport function rawPathToString(rawPath) {\n\tif (_isNumber(rawPath[0])) { //in case a segment is passed in instead\n\t\trawPath = [rawPath];\n\t}\n\tlet result = \"\",\n\t\tl = rawPath.length,\n\t\tsl, s, i, segment;\n\tfor (s = 0; s < l; s++) {\n\t\tsegment = rawPath[s];\n\t\tresult += \"M\" + _round(segment[0]) + \",\" + _round(segment[1]) + \" C\";\n\t\tsl = segment.length;\n\t\tfor (i = 2; i < sl; i++) {\n\t\t\tresult += _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i]) + \" \";\n\t\t}\n\t\tif (segment.closed) {\n\t\t\tresult += \"z\";\n\t\t}\n\t}\n\treturn result;\n}\n\n/*\n// takes a segment with coordinates [x, y, x, y, ...] and converts the control points into angles and lengths [x, y, angle, length, angle, length, x, y, angle, length, ...] so that it animates more cleanly and avoids odd breaks/kinks. For example, if you animate from 1 o'clock to 6 o'clock, it'd just go directly/linearly rather than around. So the length would be very short in the middle of the tween.\nexport function cpCoordsToAngles(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tx, y, i;\n\tfor (i = 0; i < segment.length; i+=6) {\n\t\tx = segment[i+2] - segment[i];\n\t\ty = segment[i+3] - segment[i+1];\n\t\tresult[i+2] = Math.atan2(y, x);\n\t\tresult[i+3] = Math.sqrt(x * x + y * y);\n\t\tx = segment[i+6] - segment[i+4];\n\t\ty = segment[i+7] - segment[i+5];\n\t\tresult[i+4] = Math.atan2(y, x);\n\t\tresult[i+5] = Math.sqrt(x * x + y * y);\n\t}\n\treturn result;\n}\n\n// takes a segment that was converted with cpCoordsToAngles() to have angles and lengths instead of coordinates for the control points, and converts it BACK into coordinates.\nexport function cpAnglesToCoords(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tlength = segment.length,\n\t\trnd = 1000,\n\t\tangle, l, i, j;\n\tfor (i = 0; i < length; i+=6) {\n\t\tangle = segment[i+2];\n\t\tl = segment[i+3]; //length\n\t\tresult[i+2] = (((segment[i] + Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+3] = (((segment[i+1] + Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t\tangle = segment[i+4];\n\t\tl = segment[i+5]; //length\n\t\tresult[i+4] = (((segment[i+6] - Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+5] = (((segment[i+7] - Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t}\n\treturn result;\n}\n\n//adds an \"isSmooth\" array to each segment and populates it with a boolean value indicating whether or not it's smooth (the control points have basically the same slope). For any smooth control points, it converts the coordinates into angle (x, in radians) and length (y) and puts them into the same index value in a smoothData array.\nexport function populateSmoothData(rawPath) {\n\tlet j = rawPath.length,\n\t\tsmooth, segment, x, y, x2, y2, i, l, a, a2, isSmooth, smoothData;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tisSmooth = segment.isSmooth = segment.isSmooth || [0, 0, 0, 0];\n\t\tsmoothData = segment.smoothData = segment.smoothData || [0, 0, 0, 0];\n\t\tisSmooth.length = 4;\n\t\tl = segment.length - 2;\n\t\tfor (i = 6; i < l; i += 6) {\n\t\t\tx = segment[i] - segment[i - 2];\n\t\t\ty = segment[i + 1] - segment[i - 1];\n\t\t\tx2 = segment[i + 2] - segment[i];\n\t\t\ty2 = segment[i + 3] - segment[i + 1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tsmooth = (Math.abs(a - a2) < 0.09);\n\t\t\tif (smooth) {\n\t\t\t\tsmoothData[i - 2] = a;\n\t\t\t\tsmoothData[i + 2] = a2;\n\t\t\t\tsmoothData[i - 1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[i + 3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t}\n\t\t\tisSmooth.push(smooth, smooth, 0, 0, smooth, smooth);\n\t\t}\n\t\t//if the first and last points are identical, check to see if there's a smooth transition. We must handle this a bit differently due to their positions in the array.\n\t\tif (segment[l] === segment[0] && segment[l+1] === segment[1]) {\n\t\t\tx = segment[0] - segment[l-2];\n\t\t\ty = segment[1] - segment[l-1];\n\t\t\tx2 = segment[2] - segment[0];\n\t\t\ty2 = segment[3] - segment[1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tif (Math.abs(a - a2) < 0.09) {\n\t\t\t\tsmoothData[l-2] = a;\n\t\t\t\tsmoothData[2] = a2;\n\t\t\t\tsmoothData[l-1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t\tisSmooth[l-2] = isSmooth[l-1] = true; //don't change indexes 2 and 3 because we'll trigger everything from the END, and this will optimize file size a bit.\n\t\t\t}\n\t\t}\n\t}\n\treturn rawPath;\n}\nexport function pointToScreen(svgElement, point) {\n\tif (arguments.length < 2) { //by default, take the first set of coordinates in the path as the point\n\t\tlet rawPath = getRawPath(svgElement);\n\t\tpoint = svgElement.ownerSVGElement.createSVGPoint();\n\t\tpoint.x = rawPath[0][0];\n\t\tpoint.y = rawPath[0][1];\n\t}\n\treturn point.matrixTransform(svgElement.getScreenCTM());\n}\n// takes a <path> and normalizes all of its coordinates to values between 0 and 1\nexport function normalizePath(path) {\n  path = gsap.utils.toArray(path);\n  if (!path[0].hasAttribute(\"d\")) {\n    path = gsap.utils.toArray(path[0].children);\n  }\n  if (path.length > 1) {\n    path.forEach(normalizePath);\n    return path;\n  }\n  let _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n      _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n      d = path[0].getAttribute(\"d\"),\n      a = d.replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp),\n      nums = a.filter(n => !isNaN(n)).map(n => +n),\n      normalize = gsap.utils.normalize(Math.min(...nums), Math.max(...nums)),\n      finals = a.map(val => isNaN(val) ? val : normalize(+val)),\n      s = \"\",\n      prevWasCommand;\n  finals.forEach((value, i) => {\n    let isCommand = isNaN(value)\n    s += (isCommand && i ? \" \" : prevWasCommand || !i ? \"\" : \",\") + value;\n    prevWasCommand = isCommand;\n  });\n  path[0].setAttribute(\"d\", s);\n}\n*/", "/*!\n * matrix 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _doc, _win, _doc<PERSON><PERSON>, _body,\t_div<PERSON><PERSON>r, _svg<PERSON><PERSON>r, _identityMatrix, _gEl,\n\t_transformProp = \"transform\",\n\t_transformOriginProp = _transformProp + \"Origin\",\n\t_hasOffsetBug,\n\t_setDoc = element => {\n\t\tlet doc = element.ownerDocument || element;\n\t\tif (!(_transformProp in element.style) && \"msTransform\" in element.style) { //to improve compatibility with old Microsoft browsers\n\t\t\t_transformProp = \"msTransform\";\n\t\t\t_transformOriginProp = _transformProp + \"Origin\";\n\t\t}\n\t\twhile (doc.parentNode && (doc = doc.parentNode)) {\t}\n\t\t_win = window;\n\t\t_identityMatrix = new Matrix2D();\n\t\tif (doc) {\n\t\t\t_doc = doc;\n\t\t\t_docElement = doc.documentElement;\n\t\t\t_body = doc.body;\n\t\t\t_gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n\t\t\t// prevent any existing CSS from transforming it\n\t\t\t_gEl.style.transform = \"none\";\n\t\t\t// now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon.\n\t\t\tlet d1 = doc.createElement(\"div\"),\n\t\t\t\td2 = doc.createElement(\"div\"),\n\t\t\t\troot = doc && (doc.body || doc.firstElementChild);\n\t\t\tif (root && root.appendChild) {\n\t\t\t\troot.appendChild(d1);\n\t\t\t\td1.appendChild(d2);\n\t\t\t\td1.setAttribute(\"style\", \"position:static;transform:translate3d(0,0,1px)\");\n\t\t\t\t_hasOffsetBug = (d2.offsetParent !== d1);\n\t\t\t\troot.removeChild(d1);\n\t\t\t}\n\t\t}\n\t\treturn doc;\n\t},\n\t_forceNonZeroScale = e => { // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n\t\tlet a, cache;\n\t\twhile (e && e !== _body) {\n\t\t\tcache = e._gsap;\n\t\t\tcache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\t\t\tif (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n\t\t\t\tcache.scaleX = cache.scaleY = 1e-4;\n\t\t\t\tcache.renderTransform(1, cache);\n\t\t\t\ta ? a.push(cache) : (a = [cache]);\n\t\t\t}\n\t\t\te = e.parentNode;\n\t\t}\n\t\treturn a;\n\t},\n\t// possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n\t// _forceDisplay = e => {\n\t// \tlet a = [],\n\t// \t\tparent;\n\t// \twhile (e && e !== _body) {\n\t// \t\tparent = e.parentNode;\n\t// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n\t// \t\tparent || _body.appendChild(e);\n\t// \t\te = parent;\n\t// \t}\n\t// \treturn a;\n\t// },\n\t// _revertDisplay = a => {\n\t// \tfor (let i = 0; i < a.length; i+=3) {\n\t// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n\t// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n\t// \t}\n\t// },\n\t_svgTemps = [], //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n\t_divTemps = [],\n\t_getDocScrollTop = () => _win.pageYOffset  || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0,\n\t_getDocScrollLeft = () => _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0,\n\t_svgOwner = element => element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null),\n\t_isFixed = element => {\n\t\tif (_win.getComputedStyle(element).position === \"fixed\") {\n\t\t\treturn true;\n\t\t}\n\t\telement = element.parentNode;\n\t\tif (element && element.nodeType === 1) { // avoid document fragments which will throw an error.\n\t\t\treturn _isFixed(element);\n\t\t}\n\t},\n\t_createSibling = (element, i) => {\n\t\tif (element.parentNode && (_doc || _setDoc(element))) {\n\t\t\tlet svg = _svgOwner(element),\n\t\t\t\tns = svg ? (svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\") : \"http://www.w3.org/1999/xhtml\",\n\t\t\t\ttype = svg ? (i ? \"rect\" : \"g\") : \"div\",\n\t\t\t\tx = i !== 2 ? 0 : 100,\n\t\t\t\ty = i === 3 ? 100 : 0,\n\t\t\t\tcss = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n\t\t\t\te = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n\t\t\tif (i) {\n\t\t\t\tif (!svg) {\n\t\t\t\t\tif (!_divContainer) {\n\t\t\t\t\t\t_divContainer = _createSibling(element);\n\t\t\t\t\t\t_divContainer.style.cssText = css;\n\t\t\t\t\t}\n\t\t\t\t\te.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n\t\t\t\t\t_divContainer.appendChild(e);\n\n\t\t\t\t} else {\n\t\t\t\t\t_svgContainer || (_svgContainer = _createSibling(element));\n\t\t\t\t\te.setAttribute(\"width\", 0.01);\n\t\t\t\t\te.setAttribute(\"height\", 0.01);\n\t\t\t\t\te.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n\t\t\t\t\t_svgContainer.appendChild(e);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn e;\n\t\t}\n\t\tthrow \"Need document and parent.\";\n\t},\n\t_consolidate = m => { // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\tlet c = new Matrix2D(),\n\t\t\ti = 0;\n\t\tfor (; i < m.numberOfItems; i++) {\n\t\t\tc.multiply(m.getItem(i).matrix);\n\t\t}\n\t\treturn c;\n\t},\n\t_getCTM = svg => {\n\t\tlet m = svg.getCTM(),\n\t\t\ttransform;\n\t\tif (!m) { // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n\t\t\ttransform = svg.style[_transformProp];\n\t\t\tsvg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\t\t\tsvg.appendChild(_gEl);\n\t\t\tm = _gEl.getCTM();\n\t\t\tsvg.removeChild(_gEl);\n\t\t\ttransform ? (svg.style[_transformProp] = transform) : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n\t\t}\n\t\treturn m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n\t},\n\t_placeSiblings = (element, adjustGOffset) => {\n\t\tlet svg = _svgOwner(element),\n\t\t\tisRootSVG = element === svg,\n\t\t\tsiblings = svg ? _svgTemps : _divTemps,\n\t\t\tparent = element.parentNode,\n\t\t\tappendToEl = parent && !svg && parent.shadowRoot && parent.shadowRoot.appendChild ? parent.shadowRoot : parent,\n\t\t\tcontainer, m, b, x, y, cs;\n\t\tif (element === _win) {\n\t\t\treturn element;\n\t\t}\n\t\tsiblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n\t\tcontainer = svg ? _svgContainer : _divContainer;\n\t\tif (svg) {\n\t\t\tif (isRootSVG) {\n\t\t\t\tb = _getCTM(element);\n\t\t\t\tx = -b.e / b.a;\n\t\t\t\ty = -b.f / b.d;\n\t\t\t\tm = _identityMatrix;\n\t\t\t} else if (element.getBBox) {\n\t\t\t\tb = element.getBBox();\n\t\t\t\tm = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\t\t\t\tm = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\t\t\tx = m.a * b.x + m.c * b.y;\n\t\t\t\ty = m.b * b.x + m.d * b.y;\n\t\t\t} else { // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n\t\t\t\tm = new Matrix2D();\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\tif (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\t(isRootSVG ? svg : parent).appendChild(container);\n\t\t\tcontainer.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n\t\t} else {\n\t\t\tx = y = 0;\n\t\t\tif (_hasOffsetBug) { // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n\t\t\t\tm = element.offsetParent;\n\t\t\t\tb = element;\n\t\t\t\twhile (b && (b = b.parentNode) && b !== m && b.parentNode) {\n\t\t\t\t\tif ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n\t\t\t\t\t\tx = b.offsetLeft;\n\t\t\t\t\t\ty = b.offsetTop;\n\t\t\t\t\t\tb = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcs = _win.getComputedStyle(element);\n\t\t\tif (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n\t\t\t\tm = element.offsetParent;\n\t\t\t\twhile (parent && parent !== m) { // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n\t\t\t\t\tx += parent.scrollLeft || 0;\n\t\t\t\t\ty += parent.scrollTop || 0;\n\t\t\t\t\tparent = parent.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\tb = container.style;\n\t\t\tb.top = (element.offsetTop - y) + \"px\";\n\t\t\tb.left = (element.offsetLeft - x) + \"px\";\n\t\t\tb[_transformProp] = cs[_transformProp];\n\t\t\tb[_transformOriginProp] = cs[_transformOriginProp];\n\t\t\t// b.border = m.border;\n\t\t\t// b.borderLeftStyle = m.borderLeftStyle;\n\t\t\t// b.borderTopStyle = m.borderTopStyle;\n\t\t\t// b.borderLeftWidth = m.borderLeftWidth;\n\t\t\t// b.borderTopWidth = m.borderTopWidth;\n\t\t\tb.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n\t\t\tappendToEl.appendChild(container);\n\t\t}\n\t\treturn container;\n\t},\n\t_setMatrix = (m, a, b, c, d, e, f) => {\n\t\tm.a = a;\n\t\tm.b = b;\n\t\tm.c = c;\n\t\tm.d = d;\n\t\tm.e = e;\n\t\tm.f = f;\n\t\treturn m;\n\t};\n\nexport class Matrix2D {\n\tconstructor(a=1, b=0, c=0, d=1, e=0, f=0) {\n\t\t_setMatrix(this, a, b, c, d, e, f);\n\t}\n\n\tinverse() {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\tdeterminant = (a * d - b * c) || 1e-10;\n\t\treturn _setMatrix(\n\t\t\tthis,\n\t\t\td / determinant,\n\t\t\t-b / determinant,\n\t\t\t-c / determinant,\n\t\t\ta / determinant,\n\t\t\t(c * f - d * e) / determinant,\n\t\t\t-(a * f - b * e) / determinant\n\t\t);\n\t}\n\n\tmultiply(matrix) {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\ta2 = matrix.a,\n\t\t\tb2 = matrix.c,\n\t\t\tc2 = matrix.b,\n\t\t\td2 = matrix.d,\n\t\t\te2 = matrix.e,\n\t\t\tf2 = matrix.f;\n\t\treturn _setMatrix(this,\n\t\t\ta2 * a + c2 * c,\n\t\t\ta2 * b + c2 * d,\n\t\t\tb2 * a + d2 * c,\n\t\t\tb2 * b + d2 * d,\n\t\t\te + e2 * a + f2 * c,\n\t\t\tf + e2 * b + f2 * d);\n\t}\n\n\tclone() {\n\t\treturn new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n\t}\n\n\tequals(matrix) {\n\t\tlet {a, b, c, d, e, f} = this;\n\t\treturn (a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f);\n\t}\n\n\tapply(point, decoratee={}) {\n\t\tlet {x, y} = point,\n\t\t\t{a, b, c, d, e, f} = this;\n\t\tdecoratee.x = (x * a + y * c + e) || 0;\n\t\tdecoratee.y = (x * b + y * d + f) || 0;\n\t\treturn decoratee;\n\t}\n\n}\n\n// Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) { // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n\tif (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n\t\treturn new Matrix2D();\n\t}\n\tlet zeroScales = _forceNonZeroScale(element),\n\t\tsvg = _svgOwner(element),\n\t\ttemps = svg ? _svgTemps : _divTemps,\n\t\tcontainer = _placeSiblings(element, adjustGOffset),\n\t\tb1 = temps[0].getBoundingClientRect(),\n\t\tb2 = temps[1].getBoundingClientRect(),\n\t\tb3 = temps[2].getBoundingClientRect(),\n\t\tparent = container.parentNode,\n\t\tisFixed = !includeScrollInFixed && _isFixed(element),\n\t\tm = new Matrix2D(\n\t\t\t(b2.left - b1.left) / 100,\n\t\t\t(b2.top - b1.top) / 100,\n\t\t\t(b3.left - b1.left) / 100,\n\t\t\t(b3.top - b1.top) / 100,\n\t\t\tb1.left + (isFixed ? 0 : _getDocScrollLeft()),\n\t\t\tb1.top + (isFixed ? 0 : _getDocScrollTop())\n\t\t);\n\tparent.removeChild(container);\n\tif (zeroScales) {\n\t\tb1 = zeroScales.length;\n\t\twhile (b1--) {\n\t\t\tb2 = zeroScales[b1];\n\t\t\tb2.scaleX = b2.scaleY = 0;\n\t\t\tb2.renderTransform(1, b2);\n\t\t}\n\t}\n\treturn inverse ? m.inverse() : m;\n}\n\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM };\n\n// export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }", "/*!\n * MotionPathPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { getRawPath, cacheRawPathMeasurements, getPositionOnPath, pointsToSegment, flatPointsToSegment, sliceRawPath, stringToRawPath, rawPathToString, transformRawPath, convertToPath } from \"./utils/paths.js\";\nimport { getGlobalMatrix } from \"./utils/matrix.js\";\n\nlet _xProps = \"x,translateX,left,marginLeft,xPercent\".split(\",\"),\n\t_yProps = \"y,translateY,top,marginTop,yPercent\".split(\",\"),\n\t_DEG2RAD = Math.PI / 180,\n\tgsap, PropTween, _getUnit, _toArray, _getStyleSaver, _reverting,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_populateSegmentFromArray = (segment, values, property, mode) => { //mode: 0 = x but don't fill y yet, 1 = y, 2 = x and fill y with 0.\n\t\tlet l = values.length,\n\t\t\tsi = mode === 2 ? 0 : mode,\n\t\t\ti = 0,\n\t\t\tv;\n\t\tfor (; i < l; i++) {\n\t\t\tsegment[si] = v = parseFloat(values[i][property]);\n\t\t\tmode === 2 && (segment[si+1] = 0);\n\t\t\tsi += 2;\n\t\t}\n\t\treturn segment;\n\t},\n\t_getPropNum = (target, prop, unit) => parseFloat(target._gsap.get(target, prop, unit || \"px\")) || 0,\n\t_relativize = segment => {\n\t\tlet x = segment[0],\n\t\t\ty = segment[1],\n\t\t\ti;\n\t\tfor (i = 2; i < segment.length; i+=2) {\n\t\t\tx = (segment[i] += x);\n\t\t\ty = (segment[i+1] += y);\n\t\t}\n\t},\n\t// feed in an array of quadratic bezier points like [{x: 0, y: 0}, ...] and it'll convert it to cubic bezier\n\t// _quadToCubic = points => {\n\t// \tlet cubic = [],\n\t// \t\tl = points.length - 1,\n\t// \t\ti = 1,\n\t// \t\ta, b, c;\n\t// \tfor (; i < l; i+=2) {\n\t// \t\ta = points[i-1];\n\t// \t\tb = points[i];\n\t// \t\tc = points[i+1];\n\t// \t\tcubic.push(a, {x: (2 * b.x + a.x) / 3, y: (2 * b.y + a.y) / 3}, {x: (2 * b.x + c.x) / 3, y: (2 * b.y + c.y) / 3});\n\t// \t}\n\t// \tcubic.push(points[l]);\n\t// \treturn cubic;\n\t// },\n\t_segmentToRawPath = (plugin, segment, target, x, y, slicer, vars, unitX, unitY) => {\n\t\tif (vars.type === \"cubic\") {\n\t\t\tsegment = [segment];\n\t\t} else {\n\t\t\tvars.fromCurrent !== false && segment.unshift(_getPropNum(target, x, unitX), y ? _getPropNum(target, y, unitY) : 0);\n\t\t\tvars.relative && _relativize(segment);\n\t\t\tlet pointFunc = y ? pointsToSegment : flatPointsToSegment;\n\t\t\tsegment = [pointFunc(segment, vars.curviness)];\n\t\t}\n\t\tsegment = slicer(_align(segment, target, vars));\n\t\t_addDimensionalPropTween(plugin, target, x, segment, \"x\", unitX);\n\t\ty && _addDimensionalPropTween(plugin, target, y, segment, \"y\", unitY);\n\t\treturn cacheRawPathMeasurements(segment, vars.resolution || (vars.curviness === 0 ? 20 : 12)); //when curviness is 0, it creates control points right on top of the anchors which makes it more sensitive to resolution, thus we change the default accordingly.\n\t},\n\t_emptyFunc = v => v,\n\t_numExp = /[-+\\.]*\\d+\\.?(?:e-|e\\+)?\\d*/g,\n\t_originToPoint = (element, origin, parentMatrix) => { // origin is an array of normalized values (0-1) in relation to the width/height, so [0.5, 0.5] would be the center. It can also be \"auto\" in which case it will be the top left unless it's a <path>, when it will start at the beginning of the path itself.\n\t\tlet m = getGlobalMatrix(element),\n\t\t\tx = 0,\n\t\t\ty = 0,\n\t\t\tsvg;\n\t\tif ((element.tagName + \"\").toLowerCase() === \"svg\") {\n\t\t\tsvg = element.viewBox.baseVal;\n\t\t\tsvg.width || (svg = {width: +element.getAttribute(\"width\"), height: +element.getAttribute(\"height\")});\n\t\t} else {\n\t\t\tsvg = origin && element.getBBox && element.getBBox();\n\t\t}\n\t\tif (origin && origin !== \"auto\") {\n\t\t\tx = origin.push ? origin[0] * (svg ? svg.width : element.offsetWidth || 0) : origin.x;\n\t\t\ty = origin.push ? origin[1] * (svg ? svg.height : element.offsetHeight || 0) : origin.y;\n\t\t}\n\t\treturn parentMatrix.apply( x || y ? m.apply({x: x, y: y}) : {x: m.e, y: m.f} );\n\t},\n\t_getAlignMatrix = (fromElement, toElement, fromOrigin, toOrigin) => {\n\t\tlet parentMatrix = getGlobalMatrix(fromElement.parentNode, true, true),\n\t\t\tm = parentMatrix.clone().multiply(getGlobalMatrix(toElement)),\n\t\t\tfromPoint = _originToPoint(fromElement, fromOrigin, parentMatrix),\n\t\t\t{x, y} = _originToPoint(toElement, toOrigin, parentMatrix),\n\t\t\tp;\n\t\tm.e = m.f = 0;\n\t\tif (toOrigin === \"auto\" && toElement.getTotalLength && toElement.tagName.toLowerCase() === \"path\") {\n\t\t\tp = toElement.getAttribute(\"d\").match(_numExp) || [];\n\t\t\tp = m.apply({x:+p[0], y:+p[1]});\n\t\t\tx += p.x;\n\t\t\ty += p.y;\n\t\t}\n\t\t//if (p || (toElement.getBBox && fromElement.getBBox && toElement.ownerSVGElement === fromElement.ownerSVGElement)) {\n\t\tif (p) {\n\t\t\tp = m.apply(toElement.getBBox());\n\t\t\tx -= p.x;\n\t\t\ty -= p.y;\n\t\t}\n\t\tm.e = x - fromPoint.x;\n\t\tm.f = y - fromPoint.y;\n\t\treturn m;\n\t},\n\t_align = (rawPath, target, {align, matrix, offsetX, offsetY, alignOrigin}) => {\n\t\tlet x = rawPath[0][0],\n\t\t\ty = rawPath[0][1],\n\t\t\tcurX = _getPropNum(target, \"x\"),\n\t\t\tcurY = _getPropNum(target, \"y\"),\n\t\t\talignTarget, m, p;\n\t\tif (!rawPath || !rawPath.length) {\n\t\t\treturn getRawPath(\"M0,0L0,0\");\n\t\t}\n\t\tif (align) {\n\t\t\tif (align === \"self\" || ((alignTarget = _toArray(align)[0] || target) === target)) {\n\t\t\t\ttransformRawPath(rawPath, 1, 0, 0, 1, curX - x, curY - y);\n\t\t\t} else {\n\t\t\t\tif (alignOrigin && alignOrigin[2] !== false) {\n\t\t\t\t\tgsap.set(target, {transformOrigin:(alignOrigin[0] * 100) + \"% \" + (alignOrigin[1] * 100) + \"%\"});\n\t\t\t\t} else {\n\t\t\t\t\talignOrigin = [_getPropNum(target, \"xPercent\") / -100, _getPropNum(target, \"yPercent\") / -100];\n\t\t\t\t}\n\t\t\t\tm = _getAlignMatrix(target, alignTarget, alignOrigin, \"auto\");\n\t\t\t\tp = m.apply({x: x, y: y});\n\t\t\t\ttransformRawPath(rawPath, m.a, m.b, m.c, m.d, curX + m.e - (p.x - m.e), curY + m.f - (p.y - m.f));\n\t\t\t}\n\t\t}\n\t\tif (matrix) {\n\t\t\ttransformRawPath(rawPath, matrix.a, matrix.b, matrix.c, matrix.d, matrix.e, matrix.f);\n\t\t} else if (offsetX || offsetY) {\n\t\t\ttransformRawPath(rawPath, 1, 0, 0, 1, offsetX || 0, offsetY || 0);\n\t\t}\n\t\treturn rawPath;\n\t},\n\t_addDimensionalPropTween = (plugin, target, property, rawPath, pathProperty, forceUnit) => {\n\t\tlet cache = target._gsap,\n\t\t\tharness = cache.harness,\n\t\t\talias = (harness && harness.aliases && harness.aliases[property]),\n\t\t\tprop = alias && alias.indexOf(\",\") < 0 ? alias : property,\n\t\t\tpt = plugin._pt = new PropTween(plugin._pt, target, prop, 0, 0, _emptyFunc, 0, cache.set(target, prop, plugin));\n\t\tpt.u = _getUnit(cache.get(target, prop, forceUnit)) || 0;\n\t\tpt.path = rawPath;\n\t\tpt.pp = pathProperty;\n\t\tplugin._props.push(prop);\n\t},\n\t_sliceModifier = (start, end) => rawPath => (start || end !== 1) ? sliceRawPath(rawPath, start, end) : rawPath;\n\n\nexport const MotionPathPlugin = {\n\tversion: \"3.13.0\",\n\tname: \"motionPath\",\n\tregister(core, Plugin, propTween) {\n\t\tgsap = core;\n\t\t_getUnit = gsap.utils.getUnit;\n\t\t_toArray = gsap.utils.toArray;\n\t\t_getStyleSaver = gsap.core.getStyleSaver;\n\t\t_reverting = gsap.core.reverting || function() {};\n\t\tPropTween = propTween;\n\t},\n\tinit(target, vars, tween) {\n\t\tif (!gsap) {\n\t\t\tconsole.warn(\"Please gsap.registerPlugin(MotionPathPlugin)\");\n\t\t\treturn false;\n\t\t}\n\t\tif (!(typeof(vars) === \"object\" && !vars.style) || !vars.path) {\n\t\t\tvars = {path:vars};\n\t\t}\n\t\tlet rawPaths = [],\n\t\t\t{path, autoRotate, unitX, unitY, x, y} = vars,\n\t\t\tfirstObj = path[0],\n\t\t\tslicer = _sliceModifier(vars.start, (\"end\" in vars) ? vars.end : 1),\n\t\t\trawPath, p;\n\t\tthis.rawPaths = rawPaths;\n\t\tthis.target = target;\n\t\tthis.tween = tween;\n\t\tthis.styles = _getStyleSaver && _getStyleSaver(target, \"transform\");\n\t\tif ((this.rotate = (autoRotate || autoRotate === 0))) { //get the rotational data FIRST so that the setTransform() method is called in the correct order in the render() loop - rotation gets set last.\n\t\t\tthis.rOffset = parseFloat(autoRotate) || 0;\n\t\t\tthis.radians = !!vars.useRadians;\n\t\t\tthis.rProp = vars.rotation || \"rotation\";                       // rotation property\n\t\t\tthis.rSet = target._gsap.set(target, this.rProp, this);         // rotation setter\n\t\t\tthis.ru = _getUnit(target._gsap.get(target, this.rProp)) || 0;  // rotation units\n\t\t}\n\t\tif (Array.isArray(path) && !(\"closed\" in path) && typeof(firstObj) !== \"number\") {\n\t\t\tfor (p in firstObj) {\n\t\t\t\tif (!x && ~_xProps.indexOf(p)) {\n\t\t\t\t\tx = p;\n\t\t\t\t} else if (!y && ~_yProps.indexOf(p)) {\n\t\t\t\t\ty = p;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (x && y) { //correlated values\n\t\t\t\trawPaths.push(_segmentToRawPath(this, _populateSegmentFromArray(_populateSegmentFromArray([], path, x, 0), path, y, 1), target, x, y, slicer, vars, unitX || _getUnit(path[0][x]), unitY || _getUnit(path[0][y])));\n\t\t\t} else {\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\tfor (p in firstObj) {\n\t\t\t\tp !== x && p !== y && rawPaths.push(_segmentToRawPath(this, _populateSegmentFromArray([], path, p, 2), target, p, 0, slicer, vars, _getUnit(path[0][p])));\n\t\t\t}\n\t\t} else {\n\t\t\trawPath = slicer(_align(getRawPath(vars.path), target, vars));\n\t\t\tcacheRawPathMeasurements(rawPath, vars.resolution);\n\t\t\trawPaths.push(rawPath);\n\t\t\t_addDimensionalPropTween(this, target, vars.x || \"x\", rawPath, \"x\", vars.unitX || \"px\");\n\t\t\t_addDimensionalPropTween(this, target, vars.y || \"y\", rawPath, \"y\", vars.unitY || \"px\");\n\t\t}\n\t\ttween.vars.immediateRender && this.render(tween.progress(), this);\n\t},\n\trender(ratio, data) {\n\t\tlet rawPaths = data.rawPaths,\n\t\t\ti = rawPaths.length,\n\t\t\tpt = data._pt;\n\t\tif (data.tween._time || !_reverting()) {\n\t\t\tif (ratio > 1) {\n\t\t\t\tratio = 1;\n\t\t\t} else if (ratio < 0) {\n\t\t\t\tratio = 0;\n\t\t\t}\n\t\t\twhile (i--) {\n\t\t\t\tgetPositionOnPath(rawPaths[i], ratio, !i && data.rotate, rawPaths[i]);\n\t\t\t}\n\t\t\twhile (pt) {\n\t\t\t\tpt.set(pt.t, pt.p, pt.path[pt.pp] + pt.u, pt.d, ratio);\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t\tdata.rotate && data.rSet(data.target, data.rProp, rawPaths[0].angle * (data.radians ? _DEG2RAD : 1) + data.rOffset + data.ru, data, ratio);\n\t\t} else {\n\t\t\tdata.styles.revert();\n\t\t}\n\t},\n\tgetLength(path) {\n\t\treturn cacheRawPathMeasurements(getRawPath(path)).totalLength;\n\t},\n\tsliceRawPath,\n\tgetRawPath,\n\tpointsToSegment,\n\tstringToRawPath,\n\trawPathToString,\n\ttransformRawPath,\n\tgetGlobalMatrix,\n\tgetPositionOnPath,\n\tcacheRawPathMeasurements,\n\tconvertToPath: (targets, swap) => _toArray(targets).map(target => convertToPath(target, swap !== false)),\n\tconvertCoordinates(fromElement, toElement, point) {\n\t\tlet m = getGlobalMatrix(toElement, true, true).multiply(getGlobalMatrix(fromElement));\n\t\treturn point ? m.apply(point) : m;\n\t},\n\tgetAlignMatrix: _getAlignMatrix,\n\tgetRelativePosition(fromElement, toElement, fromOrigin, toOrigin) {\n\t\tlet m =_getAlignMatrix(fromElement, toElement, fromOrigin, toOrigin);\n\t\treturn {x: m.e, y: m.f};\n\t},\n\tarrayToRawPath(value, vars) {\n\t\tvars = vars || {};\n\t\tlet segment = _populateSegmentFromArray(_populateSegmentFromArray([], value, vars.x || \"x\", 0), value, vars.y || \"y\", 1);\n\t\tvars.relative && _relativize(segment);\n\t\treturn [(vars.type === \"cubic\") ? segment : pointsToSegment(segment, vars.curviness)];\n\t}\n};\n\n_getGSAP() && gsap.registerPlugin(MotionPathPlugin);\n\nexport { MotionPathPlugin as default };"], "names": ["_isString", "value", "_roundPrecise", "Math", "round", "_splitSegment", "rawPath", "segIndex", "i", "t", "segment", "shift", "subdivideSegment", "length", "splice", "slice", "_appendOrMerge", "index", "prevSeg", "l", "concat", "_svgPathExp", "_numbersExp", "_scientific", "_selectorExp", "_DEG2RAD", "PI", "_RAD2DEG", "_sin", "sin", "_cos", "cos", "_abs", "abs", "_sqrt", "sqrt", "_atan2", "atan2", "_largeNum", "_isNumber", "_temp", "_temp2", "_roundingNum", "_wrapProgress", "progress", "_round", "_getSampleIndex", "samples", "_copyMetaData", "source", "copy", "totalLength", "lookup", "<PERSON><PERSON><PERSON><PERSON>", "resolution", "totalPoints", "getRawPath", "e", "test", "document", "querySelector", "getAttribute", "_gsPath", "_dirty", "stringToRawPath", "console", "warn", "reverseSegment", "y", "reverse", "reversed", "_typeAttrs", "rect", "circle", "ellipse", "line", "convertToPath", "element", "swap", "data", "x", "r", "ry", "path", "rcirc", "rycirc", "points", "w", "h", "x2", "x3", "x4", "x5", "x6", "y2", "y3", "y4", "y5", "y6", "attr", "type", "tagName", "toLowerCase", "circ", "getBBox", "_createPath", "ignore", "name", "createElementNS", "call", "attributes", "nodeName", "indexOf", "setAttributeNS", "nodeValue", "_attrToObj", "attrs", "props", "split", "obj", "rx", "width", "height", "join", "cx", "cy", "x1", "y1", "match", "setAttribute", "rawPathToString", "_gsRawPath", "parentNode", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "getRotationAtBezierT", "a", "b", "c", "sliceRawPath", "start", "end", "_isUndefined", "loops", "max", "copyRawPath", "_reverseRawPath", "skip<PERSON>uter", "offset", "min", "cacheRawPathMeasurements", "wraps<PERSON><PERSON><PERSON>", "sShift", "eShift", "totalSegments", "j", "wrap", "s", "getProgressData", "eSeg", "sSeg", "eSegIndex", "sSegIndex", "ei", "si", "sameSegment", "sameBezier", "angle", "push", "measureSegment", "startIndex", "bezier<PERSON><PERSON>", "xd", "xd1", "yd", "yd1", "inv", "lengthIndex", "seg<PERSON><PERSON><PERSON>", "inc", "endIndex", "samplesIndex", "prevLength", "<PERSON><PERSON><PERSON><PERSON>", "ax", "ay", "cp1x", "cp1y", "cp2x", "cp2y", "x1a", "y1a", "x2a", "y2a", "decoratee", "pushToNextIfAtEnd", "getPositionOnPath", "includeAngle", "point", "result", "transformRawPath", "d", "tx", "ty", "arcToSegment", "lastX", "lastY", "largeArcFlag", "sweepFlag", "angleRad", "cosAngle", "sinAngle", "TWOPI", "dx2", "dy2", "x1_sq", "y1_sq", "radiiCheck", "rx_sq", "ry_sq", "sq", "coef", "cx1", "cy1", "ux", "uy", "vx", "vy", "temp", "angleStart", "acos", "angleExtent", "isNaN", "segments", "ceil", "angleIncrement", "controlLength", "ma", "mb", "mc", "md", "sx", "sy", "ex", "ey", "difX", "difY", "command", "isRelative", "startX", "startY", "beziers", "prevCommand", "flag1", "flag2", "replace", "m", "n", "relativeX", "relativeY", "elements", "errorMessage", "log", "toUpperCase", "closed", "substr", "char<PERSON>t", "pop", "flatPointsToSegment", "curviness", "pointsToSegment", "prevX", "prevY", "dx1", "dy1", "r1", "r2", "tl", "mx1", "mx2", "mxm", "my1", "my2", "mym", "nextX", "nextY", "unshift", "sl", "_setDoc", "doc", "ownerDocument", "_transformProp", "style", "_transformOriginProp", "_win", "window", "_identityMatrix", "Matrix2D", "_doc<PERSON>lement", "_doc", "documentElement", "_body", "body", "_gEl", "transform", "d1", "createElement", "d2", "root", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "_hasOffsetBug", "offsetParent", "_svgOwner", "ownerSVGElement", "_createSibling", "svg", "ns", "css", "_svgContainer", "_divContainer", "cssText", "_placeSiblings", "adjustGOffset", "container", "cs", "isRootSVG", "siblings", "_svgTemps", "_divTemps", "parent", "appendToEl", "shadowRoot", "_getCTM", "getCTM", "removeProperty", "clone", "f", "baseVal", "numberOfItems", "_consolidate", "multiply", "getItem", "matrix", "getComputedStyle", "offsetLeft", "offsetTop", "position", "scrollLeft", "scrollTop", "top", "left", "_setMatrix", "inverse", "this", "determinant", "a2", "b2", "c2", "e2", "f2", "equals", "apply", "getGlobalMatrix", "includeScrollInFixed", "zeroScales", "_forceNonZeroScale", "cache", "_gsap", "uncache", "get", "scaleX", "scaleY", "renderTransform", "temps", "b1", "getBoundingClientRect", "b3", "isFixed", "_isFixed", "nodeType", "_getDocScrollLeft", "pageXOffset", "_getDocScrollTop", "pageYOffset", "_populateSegmentFromArray", "values", "property", "mode", "parseFloat", "_getPropNum", "target", "prop", "unit", "_relativize", "_segmentToRawPath", "plugin", "slicer", "vars", "unitX", "unitY", "fromCurrent", "relative", "_align", "_addDimensionalPropTween", "_emptyFunc", "v", "_originToPoint", "origin", "parentMatrix", "viewBox", "offsetWidth", "offsetHeight", "_getAlignMatrix", "fromElement", "toElement", "fromOrigin", "<PERSON><PERSON><PERSON><PERSON>", "p", "fromPoint", "getTotalLength", "_numExp", "gsap", "PropTween", "_getUnit", "_toArray", "_getStyleSaver", "_reverting", "_xProps", "_yProps", "alignTarget", "align", "offsetX", "offsetY", "align<PERSON><PERSON>in", "curX", "curY", "set", "transform<PERSON><PERSON>in", "pathProperty", "forceUnit", "harness", "alias", "aliases", "pt", "_pt", "u", "pp", "_props", "MotionPathPlugin", "version", "register", "core", "Plugin", "propTween", "utils", "getUnit", "toArray", "getStyleSaver", "reverting", "init", "tween", "rawPaths", "autoRotate", "firstObj", "_sliceModifier", "styles", "rotate", "rOffset", "radians", "useRadians", "rProp", "rotation", "rSet", "ru", "Array", "isArray", "immediateRender", "render", "ratio", "_time", "_next", "revert", "<PERSON><PERSON><PERSON><PERSON>", "targets", "map", "convertCoordinates", "getAlignMatrix", "getRelativePosition", "arrayToRawPath", "_getGSAP", "registerPlugin"], "mappings": ";;;;;;;;;6MAsBa,SAAZA,EAAYC,SAA2B,iBAAXA,EAQZ,SAAhBC,EAAgBD,UAAUE,KAAKC,MAAc,KAARH,GAAgB,MAAS,EAC9C,SAAhBI,EAAiBC,EAASC,EAAUC,EAAGC,OAClCC,EAAUJ,EAAQC,GACrBI,EAAc,IAANF,EAAU,EAAIG,iBAAiBF,EAASF,EAAGC,OAC/CE,IAAUF,IAAME,EAAQH,EAAI,EAAIE,EAAQG,cAC5CP,EAAQQ,OAAOP,EAAU,EAAGG,EAAQK,MAAM,EAAGP,EAAIG,EAAQ,IACzDD,EAAQI,OAAO,EAAGN,EAAIG,GACf,EAmCQ,SAAjBK,EAAkBV,EAASI,OACtBO,EAAQX,EAAQO,OACnBK,EAAUZ,EAAQW,EAAQ,IAAM,GAChCE,EAAID,EAAQL,OACTI,GAASP,EAAQ,KAAOQ,EAAQC,EAAE,IAAMT,EAAQ,KAAOQ,EAAQC,EAAE,KACpET,EAAUQ,EAAQE,OAAOV,EAAQK,MAAM,IACvCE,KAEDX,EAAQW,GAASP,MAtEfW,EAAc,mDACjBC,EAAc,0CACdC,EAAc,gCACdC,EAAe,4BACfC,EAAWtB,KAAKuB,GAAK,IACrBC,EAAW,IAAMxB,KAAKuB,GACtBE,EAAOzB,KAAK0B,IACZC,EAAO3B,KAAK4B,IACZC,EAAO7B,KAAK8B,IACZC,EAAQ/B,KAAKgC,KACbC,EAASjC,KAAKkC,MACdC,EAAY,IAEZC,EAAY,SAAZA,UAAYtC,SAA2B,iBAAXA,GAE5BuC,EAAQ,GACRC,EAAS,GACTC,EAAe,IACfC,EAAgB,SAAhBA,cAAgBC,UAAazC,KAAKC,OAAOwC,EAAWN,GAAa,EAAII,GAAgBA,IAAmBE,EAAW,EAAK,EAAI,IAC5HC,EAAS,SAATA,OAAS5C,UAAUE,KAAKC,MAAMH,EAAQyC,GAAgBA,GAAiB,GAWvEI,EAAkB,SAAlBA,gBAAmBC,EAASlC,EAAQ+B,OAE/BzB,EAAI4B,EAAQlC,OACfL,KAAOoC,EAAWzB,MACf4B,EAAQvC,GAAKK,EAAQ,QACfL,GAAKuC,EAAQvC,GAAKK,IAC3BL,EAAI,IAAMA,EAAI,aAEPuC,IAAUvC,GAAKK,GAAUL,EAAIW,WAE9BX,EAAIW,EAAIX,EAAIW,EAAI,GASxB6B,EAAgB,SAAhBA,cAAiBC,EAAQC,UACxBA,EAAKC,YAAcF,EAAOE,YACtBF,EAAOF,SACVG,EAAKH,QAAUE,EAAOF,QAAQhC,MAAM,GACpCmC,EAAKE,OAASH,EAAOG,OAAOrC,MAAM,GAClCmC,EAAKG,UAAYJ,EAAOI,UACxBH,EAAKI,WAAaL,EAAOK,YACfL,EAAOM,cACjBL,EAAKK,YAAcN,EAAOM,aAEpBL,GAuBF,SAASM,WAAWvD,OAGzBK,EADGmD,GADJxD,EAASD,EAAUC,IAAUuB,EAAakC,KAAKzD,IAAU0D,SAASC,cAAc3D,IAAkBA,GACpF4D,aAAe5D,EAAQ,SAEjCwD,IAAMxD,EAAQA,EAAM4D,aAAa,OAE/BJ,EAAEK,UACNL,EAAEK,QAAU,KAEbxD,EAAUmD,EAAEK,QAAQ7D,MACAK,EAAQyD,OAAUzD,EAAWmD,EAAEK,QAAQ7D,GAAS+D,gBAAgB/D,IAE7EA,EAAgFD,EAAUC,GAAS+D,gBAAgB/D,GAAUsC,EAAUtC,EAAM,IAAO,CAACA,GAASA,EAAtJgE,QAAQC,KAAK,yDAavB,SAASC,eAAezD,OAE7B0D,EADG5D,EAAI,MAERE,EAAQ2D,UACD7D,EAAIE,EAAQG,OAAQL,GAAK,EAC/B4D,EAAI1D,EAAQF,GACZE,EAAQF,GAAKE,EAAQF,EAAE,GACvBE,EAAQF,EAAE,GAAK4D,EAEhB1D,EAAQ4D,UAAY5D,EAAQ4D,SAK7B,IAcCC,EAAa,CACZC,KAAK,yBACLC,OAAO,UACPC,QAAQ,cACRC,KAAK,eAaA,SAASC,cAAcC,EAASC,OAGrCC,EAAMC,EAAGZ,EAAGa,EAAGC,EAAIC,EAAMC,EAAOC,EAAQC,EAAQC,EAAGC,EAAGC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAF3FC,EAAOvB,EAAQwB,QAAQC,cAC1BC,EAAO,oBAEK,SAATH,GAAoBvB,EAAQ2B,SAGhCrB,EAtCiB,SAAdsB,YAAehD,EAAGiD,OAInBC,EAHGxB,EAAOxB,SAASiD,gBAAgB,6BAA8B,QACjET,EAAO,GAAGpF,MAAM8F,KAAKpD,EAAEqD,YACvBtG,EAAI2F,EAAKtF,WAEV6F,EAAS,IAAMA,EAAS,KACV,IAALlG,GACRmG,EAAOR,EAAK3F,GAAGuG,SAAST,cACpBI,EAAOM,QAAQ,IAAML,EAAO,KAAO,GACtCxB,EAAK8B,eAAe,KAAMN,EAAMR,EAAK3F,GAAG0G,kBAGnC/B,EA0BDsB,CAAY5B,EAAS,qDAC5BsB,EAnBa,SAAbgB,WAAc1D,EAAG2D,WACZC,EAAQD,EAAQA,EAAME,MAAM,KAAO,GACtCC,EAAM,GACN/G,EAAI6G,EAAMxG,QACG,IAALL,GACR+G,EAAIF,EAAM7G,KAAOiD,EAAEI,aAAawD,EAAM7G,KAAO,SAEvC+G,EAYDJ,CAAWtC,EAASN,EAAW6B,IACzB,SAATA,GACHnB,EAAIkB,EAAKqB,GACTtC,EAAKiB,EAAKjB,IAAMD,EAChBD,EAAImB,EAAKnB,EACTZ,EAAI+B,EAAK/B,EACTmB,EAAIY,EAAKsB,MAAY,EAAJxC,EACjBO,EAAIW,EAAKuB,OAAc,EAALxC,EAYjBH,EAXGE,GAAKC,EAWD,KANPW,GAFAF,GADAD,EAAKV,EAAIC,GACCM,GAEAN,GAMQ,KAJlBc,EAAK3B,EAAIc,GAIoB,MAH7Bc,EAAKD,EAAKP,GAG+B,KAAO,CAACK,EAFjDI,EAAKD,EAAKd,EAAKqB,EALfX,EAAKD,EAAKV,EAAIsB,EAMdL,EAAKF,EAAKd,EACuDS,EAAIO,EAAIP,GAAMA,EAAKD,GAAM,EAAGQ,EAAIR,GAAMC,EAAKD,GAAM,EAAGQ,EAAIR,EAAIQ,EAV7HT,EAAKT,EAAIC,GAAK,EAAIsB,GAUmHL,EAAIlB,EAAGiB,EAAIjB,EAAGgB,EAAIhB,EAAGgB,GAAMA,EAAKD,GAAM,EAAGf,EAAGe,GAAMC,EAAKD,GAAM,EAAGf,EAAGe,EAAIf,EAL5Mc,EAAK1B,EAAIc,GAAM,EAAIqB,GAKgMd,EAAIrB,EAAGsB,EAAItB,EAAGsB,GAAMC,EAAKD,GAAM,EAAGtB,EAAGuB,GAAMA,EAAKD,GAAM,EAAGtB,EAAGuB,EAAIvB,EAAGwB,EAAIxB,EAAGyB,EAAIC,EAAID,EAAIE,GAAI4B,KAAK,KAAO,IAElT,KAAO3C,EAAIO,GAAK,IAAMnB,EAAI,KAAOoB,EAAI,MAASD,EAAK,MAASC,EAAK,KAAOD,EAAI,KAGjE,WAATa,GAA8B,YAATA,GAG9Bf,EAFY,WAATe,GACHnB,EAAIC,EAAKiB,EAAKlB,GACDsB,GAEbtB,EAAIkB,EAAKqB,IACTtC,EAAKiB,EAAKjB,IACIqB,GAKfxB,EAAO,MAHPC,EAAImB,EAAKyB,IAGO3C,GAAK,KAFrBb,EAAI+B,EAAK0B,IAEsB,KAAO,CAAC7C,EAAEC,EAAGb,EAAIiB,EAAQL,GADxDI,EAAQH,EAAIsB,GACuDnC,EAAIc,EAAIF,EAAGZ,EAAIc,EAAIF,EAAII,EAAOhB,EAAIc,EAAIF,EAAIC,EAAGb,EAAIiB,EAAQL,EAAIC,EAAGb,EAAGY,EAAIC,EAAGb,EAAIiB,EAAQL,EAAII,EAAOhB,EAAIc,EAAIF,EAAGZ,EAAIc,EAAIF,EAAII,EAAOhB,EAAIc,EAAIF,EAAIC,EAAGb,EAAIiB,EAAQL,EAAIC,EAAGb,GAAGuD,KAAK,KAAO,KAChO,SAATvB,EACVrB,EAAO,IAAMoB,EAAK2B,GAAK,IAAM3B,EAAK4B,GAAK,KAAO5B,EAAKV,GAAK,IAAMU,EAAKL,GAChD,aAATM,GAAgC,YAATA,IAIjCrB,EAAO,KAFPC,GADAM,GAAUT,EAAQhB,aAAa,UAAY,IAAImE,MAAM1G,IAAgB,IAC1DX,SAEM,KADjByD,EAAIkB,EAAO3E,SACgB,KAAO2E,EAAOqC,KAAK,KACjC,YAATvB,IACHrB,GAAQ,IAAMC,EAAI,IAAMZ,EAAI,MAG9Be,EAAK8C,aAAa,IAAKC,gBAAgB/C,EAAKgD,WAAanE,gBAAgBe,KACrED,GAAQD,EAAQuD,aACnBvD,EAAQuD,WAAWC,aAAalD,EAAMN,GACtCA,EAAQuD,WAAWE,YAAYzD,IAEzBM,GAxDCN,EAmET,SAAS0D,qBAAqB7H,EAASF,EAAGC,OAIxCuE,EAHGwD,EAAI9H,EAAQF,GACfiI,EAAI/H,EAAQF,EAAE,GACdkI,EAAIhI,EAAQF,EAAE,UAEfgI,IAAMC,EAAID,GAAK/H,EAEf+H,KADAC,IAAMC,EAAID,GAAKhI,GACL+H,GAAK/H,EACfuE,EAAIyD,GAAMC,GAAKhI,EAAQF,EAAE,GAAKkI,GAAKjI,EAAKgI,GAAKhI,EAAI+H,EACjDA,EAAI9H,EAAQF,EAAE,GAGdgI,KAFAC,EAAI/H,EAAQF,EAAE,IAEJgI,GAAK/H,EAEf+H,KADAC,KAFAC,EAAIhI,EAAQF,EAAE,IAEJiI,GAAKhI,GACL+H,GAAK/H,EACRoC,EAAOT,EAAOqG,GAAMC,GAAKhI,EAAQF,EAAE,GAAKkI,GAAKjI,EAAKgI,GAAKhI,EAAI+H,EAAGxD,GAAKrD,GAGpE,SAASgH,aAAarI,EAASsI,EAAOC,GAC5CA,EArOe,SAAfC,aAAe7I,eAA2B,IAAXA,EAqOzB6I,CAAaD,GAAO,EAAI3I,EAAc2I,IAAQ,EACpDD,EAAQ1I,EAAc0I,IAAU,MAC5BG,EAAQ5I,KAAK6I,IAAI,KAAMhH,EAAK6G,EAAMD,GAAS,OAC9CzD,EApJK,SAAS8D,YAAY3I,WACvBkI,EAAI,GACPhI,EAAI,EACEA,EAAIF,EAAQO,OAAQL,IAC1BgI,EAAEhI,GAAKwC,EAAc1C,EAAQE,GAAIF,EAAQE,GAAGO,MAAM,WAE5CiC,EAAc1C,EAASkI,GA8ItBS,CAAY3I,MACRuI,EAARD,IACHA,EAAQ,EAAIA,EACZC,EAAM,EAAIA,EA/MO,SAAlBK,gBAAmB5I,EAAS6I,OACvB3I,EAAIF,EAAQO,WAChBsI,GAAa7I,EAAQ+D,UACd7D,KACNF,EAAQE,GAAG8D,UAAYH,eAAe7D,EAAQE,IA4M/C0I,CAAgB/D,GAChBA,EAAKhC,YAAc,GAEhByF,EAAQ,GAAKC,EAAM,EAAG,KACrBO,EAASjJ,KAAK8B,MAAM9B,KAAKkJ,IAAIT,EAAOC,IAAQ,EAChDD,GAASQ,EACTP,GAAOO,EAERjE,EAAKhC,aAAemG,yBAAyBnE,OAY5CoE,EAAaC,EAAQC,EAAQjJ,EAAG0C,EAAMwG,EAAevI,EAAGwI,EAXrDC,EAAc,EAANf,EACXgB,EAAIC,gBAAgB3E,EAAMyD,EAAOpG,GAAO,GACxCiB,EAAIqG,gBAAgB3E,EAAM0D,EAAKpG,GAC/BsH,EAAOtG,EAAE/C,QACTsJ,EAAOH,EAAEnJ,QACTuJ,EAAYxG,EAAElD,SACd2J,EAAYL,EAAEtJ,SACd4J,EAAK1G,EAAEjD,EACP4J,EAAKP,EAAErJ,EACP6J,EAAeH,IAAcD,EAC7BK,EAAcH,IAAOC,GAAMC,KAExBT,GAAQb,EAAO,KAClBQ,EAAcU,EAAYC,GAAcG,GAAeF,EAAKC,GAAQE,GAAc7G,EAAEhD,EAAIoJ,EAAEpJ,EACtFJ,EAAc8E,EAAM+E,EAAWE,EAAIP,EAAEpJ,KACxCyJ,IACKX,IACJU,IACIK,GACH7G,EAAEhD,GAAKgD,EAAEhD,EAAIoJ,EAAEpJ,IAAM,EAAIoJ,EAAEpJ,GAC3B0J,EAAK,GACKE,IACVF,GAAMC,KAILjK,KAAK8B,IAAI,GAAK4G,EAAMD,IAAU,KACjCqB,EAAYC,EAAY,GACbzG,EAAEhD,GAAKwJ,EAClBA,IACU5J,EAAc8E,EAAM8E,EAAWE,EAAI1G,EAAEhD,IAAM8I,GACrDW,IAEW,IAARL,EAAEpJ,IACLyJ,GAAaA,EAAY,GAAK/E,EAAKtE,QAEpCqC,EAAO,GAEP/B,EAAI,GADJuI,EAAgBvE,EAAKtE,QACGkI,EAExB5H,IAAOuI,GADPC,EAAIO,GACgCD,GAAaP,EAC5ClJ,EAAI,EAAGA,EAAIW,EAAGX,IAClBQ,EAAekC,EAAMiC,EAAKwE,IAAMD,IAEjCvE,EAAOjC,UAEPuG,EAAiB,IAARhG,EAAEhD,EAAU,EAAIG,iBAAiBmJ,EAAMI,EAAI1G,EAAEhD,GAClDmI,IAAUC,MACbW,EAAS5I,iBAAiBoJ,EAAMI,EAAIE,EAAaT,EAAEpJ,EAAIgD,EAAEhD,EAAIoJ,EAAEpJ,GAC/D4J,IAAgBZ,GAAUD,GAC1BO,EAAKjJ,OAAOqJ,EAAKV,EAAS,IACzBD,GAAUY,IAAOJ,EAAKlJ,OAAO,EAAGsJ,EAAKZ,GACtChJ,EAAI2E,EAAKtE,OACFL,MAELA,EAAI0J,GAAiBD,EAAJzJ,IAAkB2E,EAAKrE,OAAON,EAAG,QAGpDuJ,EAAKQ,MAAQhC,qBAAqBwB,EAAMI,EAAKV,EAAQ,GAErDI,EAAIE,EADJI,GAAMV,GAENhG,EAAIsG,EAAKI,EAAG,GACZJ,EAAKlJ,OAASkJ,EAAK5G,YAAc,EACjC4G,EAAKxG,YAAc4B,EAAK5B,YAAc,EACtCwG,EAAKS,KAAKX,EAAGpG,EAAGoG,EAAGpG,EAAGoG,EAAGpG,EAAGoG,EAAGpG,UAGjC0B,EAAKhC,YAAc,EACZgC,EAIR,SAASsF,eAAe/J,EAASgK,EAAYC,GAC5CD,EAAaA,GAAc,EACtBhK,EAAQqC,UACZrC,EAAQqC,QAAU,GAClBrC,EAAQ0C,OAAS,QAajB5C,EAAGmJ,EAAGhE,EAAID,EAAID,EAAImF,EAAIC,EAAK7E,EAAID,EAAID,EAAIgF,EAAIC,EAAKC,EAAKvK,EAAGwK,EAAa9J,EAAG+J,EAXrE5H,IAAe5C,EAAQ4C,YAAc,GACxC6H,EAAM,EAAI7H,EACV8H,EAAWT,EAAYD,EAAyB,EAAZC,EAAgB,EAAIjK,EAAQG,OAChEiH,EAAKpH,EAAQgK,GACb3C,EAAKrH,EAAQgK,EAAa,GAC1BW,EAAeX,EAAcA,EAAa,EAAKpH,EAAa,EAC5DP,EAAUrC,EAAQqC,QAClBK,EAAS1C,EAAQ0C,OACjBiG,GAAOqB,EAAahK,EAAQ2C,UAAYf,IAAcA,EACtDgJ,EAAavI,EAAQsI,EAAeV,EAAYrH,EAAa,GAC7DzC,EAAS6J,EAAa3H,EAAQsI,EAAa,GAAK,MAEjDtI,EAAQlC,OAASuC,EAAOvC,OAAS,EAC5B8I,EAAIe,EAAa,EAAGf,EAAIyB,EAAUzB,GAAK,EAAG,IAC9ChE,EAAKjF,EAAQiJ,EAAI,GAAK7B,EACtBpC,EAAKhF,EAAQiJ,EAAI,GAAK7B,EACtBrC,EAAK/E,EAAQiJ,GAAK7B,EAClB9B,EAAKtF,EAAQiJ,EAAI,GAAK5B,EACtBhC,EAAKrF,EAAQiJ,EAAI,GAAK5B,EACtBjC,EAAKpF,EAAQiJ,EAAI,GAAK5B,EACtB6C,EAAKC,EAAMC,EAAKC,EAAM,EAClB/I,EAAK2D,GAAM,KAAO3D,EAAKgE,GAAM,KAAOhE,EAAKyD,GAAMzD,EAAK8D,GAAM,IACxC,EAAjBpF,EAAQG,SACXH,EAAQI,OAAO6I,EAAG,GAClBA,GAAK,EACLyB,GAAY,YAGR5K,EAAI,EAAGA,GAAK8C,EAAY9C,IAG5BoK,EAAKC,GAAOA,IAFZpK,EAAI0K,EAAM3K,GAEaC,EAAIkF,EAAK,GADhCqF,EAAM,EAAIvK,IACiCA,EAAIiF,EAAKsF,EAAMvF,IAAOhF,GACjEqK,EAAKC,GAAOA,GAAOtK,EAAIA,EAAIuF,EAAK,EAAIgF,GAAOvK,EAAIsF,EAAKiF,EAAMlF,IAAOrF,IACjEU,EAAIe,EAAM4I,EAAKA,EAAKF,EAAKA,IACjBvB,IACPA,EAAMlI,GAEPN,GAAUM,EACV4B,EAAQsI,KAAkBxK,EAG5BiH,GAAMnC,EACNoC,GAAM/B,KAEHsF,MACHA,GAAczK,EACPwK,EAAetI,EAAQlC,OAAQwK,IACrCtI,EAAQsI,IAAiBC,KAGvBvI,EAAQlC,QAAUwI,MACrB3I,EAAQyC,YAAc+H,EAAYnI,EAAQA,EAAQlC,OAAO,IAAM,EAE3DqK,GADJxK,EAAQ2C,UAAYgG,GACE,SACrBlI,EAAI8J,EAAc,EACbzK,EAAI,EAAGA,EAAI0K,EAAW1K,GAAK6I,EAC/BjG,EAAOjC,KAAQ4B,EAAQkI,GAAezK,IAAOyK,EAAcA,OAI7DvK,EAAQyC,YAAcJ,EAAQ,GAAK,SAE7B2H,EAAa7J,EAASkC,EAAQ2H,EAAa,EAAI,GAAK7J,EAGrD,SAASyI,yBAAyBhJ,EAASgD,OAC7CiI,EAAYjG,EAAQ9E,MACnBA,EAAI+K,EAAajG,EAAS,EAAG9E,EAAIF,EAAQO,OAAQL,IACrDF,EAAQE,GAAG8C,aAAeA,GAAc,GACxCgC,GAAUhF,EAAQE,GAAGK,OACrB0K,GAAcd,eAAenK,EAAQE,WAEtCF,EAAQiD,YAAc+B,EACtBhF,EAAQ6C,YAAcoI,EACfjL,EAID,SAASM,iBAAiBF,EAASF,EAAGC,MACxCA,GAAK,GAAU,GAALA,SACN,MAEJ+K,EAAK9K,EAAQF,GAChBiL,EAAK/K,EAAQF,EAAE,GACfkL,EAAOhL,EAAQF,EAAE,GACjBmL,EAAOjL,EAAQF,EAAE,GACjBoL,EAAOlL,EAAQF,EAAE,GACjBqL,EAAOnL,EAAQF,EAAE,GAGjBsL,EAAMN,GAAME,EAAOF,GAAM/K,EACzBgF,EAAKiG,GAAQE,EAAOF,GAAQjL,EAC5BsL,EAAMN,GAAME,EAAOF,GAAMhL,EACzBqF,EAAK6F,GAAQE,EAAOF,GAAQlL,EAC5BqH,EAAKgE,GAAOrG,EAAKqG,GAAOrL,EACxBsH,EAAKgE,GAAOjG,EAAKiG,GAAOtL,EACxBuL,EAAMJ,GARDlL,EAAQF,EAAE,GAQIoL,GAAQnL,EAC3BwL,EAAMJ,GARDnL,EAAQF,EAAE,GAQIqL,GAAQpL,SAC5BgF,IAAOuG,EAAMvG,GAAMhF,EACnBqF,IAAOmG,EAAMnG,GAAMrF,EACnBC,EAAQI,OAAON,EAAI,EAAG,EACrBqC,EAAOiJ,GACPjJ,EAAOkJ,GACPlJ,EAAOiF,GACPjF,EAAOkF,GACPlF,EAAOiF,GAAMrC,EAAKqC,GAAMrH,GACxBoC,EAAOkF,GAAMjC,EAAKiC,GAAMtH,GACxBoC,EAAO4C,GACP5C,EAAOiD,GACPjD,EAAOmJ,GACPnJ,EAAOoJ,IAERvL,EAAQqC,SAAWrC,EAAQqC,QAAQjC,OAASN,EAAI,EAAKE,EAAQ4C,WAAc,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACzF,EAIR,SAASwG,gBAAgBxJ,EAASsC,EAAUsJ,EAAWC,GACtDD,EAAYA,GAAa,GACzB5L,EAAQ6C,aAAemG,yBAAyBhJ,IAC5CsC,EAAW,GAAgB,EAAXA,KACnBA,EAAWD,EAAcC,QAIzBG,EAASO,EAAYzC,EAAQwI,EAAKL,EAAKxI,EAAGC,EAFvCF,EAAW,EACdG,EAAUJ,EAAQ,MAEdsC,EAGE,GAAiB,IAAbA,EACVnC,EAAI,EAGJD,GADAE,EAAUJ,EADVC,EAAWD,EAAQO,OAAS,IAEhBA,OAAS,MACf,IACe,EAAjBP,EAAQO,OAAY,KACvBA,EAASP,EAAQ6C,YAAcP,EAC/BoG,EAAMxI,EAAI,GACFwI,GAAO1I,EAAQE,KAAK2C,aAAetC,GAC1CN,EAAWC,EAIZoC,GAAa/B,GADbwI,EAAML,GADNtI,EAAUJ,EAAQC,IACE4C,eACU6F,EAAMK,IAAS,EAE9CtG,EAAUrC,EAAQqC,QAClBO,EAAa5C,EAAQ4C,WACrBzC,EAASH,EAAQyC,YAAcP,EAE/ByG,GADA7I,EAAIE,EAAQ0C,OAAOvC,OAASH,EAAQ0C,UAAUvC,EAASH,EAAQ2C,aAAe,EAAIP,EAAgBC,EAASlC,EAAQ+B,IACzGG,EAAQvC,EAAE,GAAK,GACzBwI,EAAMjG,EAAQvC,IACJK,IACTwI,EAAML,EACNA,EAAMjG,IAAUvC,IAEjBC,EAAK,EAAI6C,IAAiBzC,EAASwI,IAAQL,EAAMK,GAAU7I,EAAI8C,GAC/D9C,EAAyB,KAAlBA,EAAI8C,GACP6I,GAA2B,IAAN1L,IACpBD,EAAI,EAAIE,EAAQG,QACnBL,GAAK,EACLC,EAAI,GACMF,EAAW,EAAID,EAAQO,SACjCL,EAAIC,EAAI,EACRC,EAAUJ,IAAUC,UApCtBE,EAAID,EAAID,EAAW,EACnBG,EAAUJ,EAAQ,UAuCnB4L,EAAUzL,EAAIA,EACdyL,EAAU1L,EAAIA,EACd0L,EAAU/G,KAAO7E,EACjB4L,EAAUxL,QAAUA,EACpBwL,EAAU3L,SAAWA,EACd2L,EAGD,SAASE,kBAAkB9L,EAASsC,EAAUyJ,EAAcC,OAGjEvJ,EAASO,EAAYzC,EAAQwI,EAAKL,EAAKxI,EAAGC,EAAG+H,EAAGwC,EAF7CtK,EAAUJ,EAAQ,GACrBiM,EAASD,GAAS,OAEf1J,EAAW,GAAgB,EAAXA,KACnBA,EAAWD,EAAcC,IAE1BlC,EAAQ0C,QAAUkG,yBAAyBhJ,GACtB,EAAjBA,EAAQO,OAAY,KACvBA,EAASP,EAAQ6C,YAAcP,EAC/BoG,EAAMxI,EAAI,GACFwI,GAAO1I,EAAQE,KAAK2C,aAAetC,GAC1CH,EAAUJ,EAAQE,GAGnBoC,GAAa/B,GADbwI,EAAML,EAAMtI,EAAQyC,eACU6F,EAAMK,IAAS,SAE9CtG,EAAUrC,EAAQqC,QAClBO,EAAa5C,EAAQ4C,WACrBzC,EAASH,EAAQyC,YAAcP,EAE/ByG,GADA7I,EAAIE,EAAQ0C,OAAOvC,OAASH,EAAQ0C,OAAOR,EAAW,KAAO/B,EAASH,EAAQ2C,WAAa3C,EAAQ0C,OAAOvC,OAAS,IAAM,EAAIiC,EAAgBC,EAASlC,EAAQ+B,IACpJG,EAAQvC,EAAE,GAAK,GACzBwI,EAAMjG,EAAQvC,IACJK,IACTwI,EAAML,EACNA,EAAMjG,IAAUvC,IAGjBwK,EAAM,GADNvK,EAAM,EAAI6C,IAAiBzC,EAASwI,IAAQL,EAAMK,GAAU7I,EAAI8C,IAAkB,GAGlFkF,EAAI9H,EADJF,EAAyB,KAAlBA,EAAI8C,IAEXiJ,EAAOvH,EAAInC,GAAQpC,EAAIA,GAAKC,EAAQF,EAAI,GAAKgI,GAAK,EAAIwC,GAAOvK,GAAKC,EAAQF,EAAI,GAAKgI,GAAKwC,GAAOtK,EAAQF,EAAI,GAAKgI,KAAO/H,EAAI+H,GAC3H+D,EAAOnI,EAAIvB,GAAQpC,EAAIA,GAAKC,EAAQF,EAAI,IAAMgI,EAAI9H,EAAQF,EAAE,KAAO,EAAIwK,GAAOvK,GAAKC,EAAQF,EAAI,GAAKgI,GAAKwC,GAAOtK,EAAQF,EAAI,GAAKgI,KAAO/H,EAAI+H,GACxI6D,IACHE,EAAOhC,MAAQ7J,EAAQyC,YAAcoF,qBAAqB7H,EAASF,EAAQ,GAALC,EAAS,EAAI,KAAOA,GAAQ,MAAQC,EAAQ6J,OAAS,GAErHgC,EAMD,SAASC,iBAAiBlM,EAASkI,EAAGC,EAAGC,EAAG+D,EAAGC,EAAIC,WAExDjM,EAASS,EAAGX,EAAGwE,EAAGZ,EADfuF,EAAIrJ,EAAQO,QAEF,IAAL8I,OAERxI,GADAT,EAAUJ,EAAQqJ,IACN9I,OACPL,EAAI,EAAGA,EAAIW,EAAGX,GAAK,EACvBwE,EAAItE,EAAQF,GACZ4D,EAAI1D,EAAQF,EAAE,GACdE,EAAQF,GAAKwE,EAAIwD,EAAIpE,EAAIsE,EAAIgE,EAC7BhM,EAAQF,EAAE,GAAKwE,EAAIyD,EAAIrE,EAAIqI,EAAIE,SAGjCrM,EAAQyD,OAAS,EACVzD,EAMR,SAASsM,aAAaC,EAAOC,EAAOtF,EAAItC,EAAIqF,EAAOwC,EAAcC,EAAWhI,EAAGZ,MAC1EyI,IAAU7H,GAAK8H,IAAU1I,GAG7BoD,EAAKxF,EAAKwF,GACVtC,EAAKlD,EAAKkD,OACN+H,EAAY1C,EAAQ,IAAO9I,EAC9ByL,EAAWpL,EAAKmL,GAChBE,EAAWvL,EAAKqL,GAChBvL,EAAKvB,KAAKuB,GACV0L,EAAa,EAAL1L,EACR2L,GAAOR,EAAQ7H,GAAK,EACpBsI,GAAOR,EAAQ1I,GAAK,EACpB0D,EAAMoF,EAAWG,EAAMF,EAAWG,EAClCvF,GAAOoF,EAAWE,EAAMH,EAAWI,EACnCC,EAAQzF,EAAKA,EACb0F,EAAQzF,EAAKA,EACb0F,EAAaF,GAAS/F,EAAKA,GAAMgG,GAAStI,EAAKA,GAC/B,EAAbuI,IACHjG,EAAKtF,EAAMuL,GAAcjG,EACzBtC,EAAKhD,EAAMuL,GAAcvI,OAEtBwI,EAAQlG,EAAKA,EAChBmG,EAAQzI,EAAKA,EACb0I,GAAOF,EAAQC,EAAUD,EAAQF,EAAUG,EAAQJ,IAAYG,EAAQF,EAAUG,EAAQJ,GACtFK,EAAK,IACRA,EAAK,OAEFC,GAASd,IAAiBC,GAAc,EAAI,GAAK9K,EAAM0L,GAC1DE,EAAetG,EAAKO,EAAM7C,EAApB2I,EACNE,GAAgB7I,EAAK4C,EAAMN,EAArBqG,EAGNjG,EAAYsF,EAAWY,EAAMX,EAAWY,GAFjClB,EAAQ7H,GAAK,EAGpB6C,EAAYsF,EAAWW,EAAMZ,EAAWa,GAFjCjB,EAAQ1I,GAAK,EAGpB4J,GAAMlG,EAAKgG,GAAOtG,EAClByG,GAAMlG,EAAKgG,GAAO7I,EAClBgJ,IAAOpG,EAAKgG,GAAOtG,EACnB2G,IAAOpG,EAAKgG,GAAO7I,EACnBkJ,EAAOJ,EAAKA,EAAKC,EAAKA,EACtBI,GAAeJ,EAAK,GAAM,EAAI,GAAK9N,KAAKmO,KAAKN,EAAK9L,EAAMkM,IACxDG,GAAgBP,EAAKG,EAAKF,EAAKC,EAAK,GAAM,EAAI,GAAK/N,KAAKmO,MAAMN,EAAKE,EAAKD,EAAKE,GAAMjM,EAAMkM,GAAQF,EAAKA,EAAKC,EAAKA,KACjHK,MAAMD,KAAiBA,EAAc7M,IAChCsL,GAA2B,EAAduB,EACjBA,GAAenB,EACLJ,GAAauB,EAAc,IACrCA,GAAenB,GAEhBiB,GAAcjB,EACdmB,GAAenB,MASd5M,EARGiO,EAAWtO,KAAKuO,KAAK1M,EAAKuM,IAAgBnB,EAAQ,IACrD9M,EAAU,GACVqO,EAAiBJ,EAAcE,EAC/BG,EAAgB,EAAI,EAAIhN,EAAK+M,EAAiB,IAAM,EAAI7M,EAAK6M,EAAiB,IAC9EE,EAAK3B,EAAW1F,EAChBsH,EAAK3B,EAAW3F,EAChBuH,EAAK5B,GAAYjI,EACjB8J,EAAK9B,EAAWhI,MAEZ1E,EAAI,EAAGA,EAAIiO,EAAUjO,IAEzBsH,EAAKhG,EADLyI,EAAQ8D,EAAa7N,EAAImO,GAEzB5G,EAAKnG,EAAK2I,GACVyD,EAAKlM,EAAKyI,GAASoE,GACnBV,EAAKrM,EAAK2I,GACVjK,EAAQkK,KAAK1C,EAAK8G,EAAgB7G,EAAIA,EAAK6G,EAAgB9G,EAAIkG,EAAKY,EAAgBX,EAAIA,EAAKW,EAAgBZ,EAAIA,EAAIC,OAGjHzN,EAAI,EAAGA,EAAIF,EAAQO,OAAQL,GAAG,EAClCsH,EAAKxH,EAAQE,GACbuH,EAAKzH,EAAQE,EAAE,GACfF,EAAQE,GAAKsH,EAAK+G,EAAK9G,EAAKgH,EAAKnH,EACjCtH,EAAQE,EAAE,GAAKsH,EAAKgH,EAAK/G,EAAKiH,EAAKnH,SAEpCvH,EAAQE,EAAE,GAAKwE,EACf1E,EAAQE,EAAE,GAAK4D,EACR9D,GAID,SAAS0D,gBAAgByI,GAUvB,SAAP9H,GAAgBsK,EAAIC,EAAIC,EAAIC,GAC3BC,GAAQF,EAAKF,GAAM,EACnBK,GAAQF,EAAKF,GAAM,EACnBxO,EAAQ8J,KAAKyE,EAAKI,EAAMH,EAAKI,EAAMH,EAAKE,EAAMD,EAAKE,EAAMH,EAAIC,OAJ9D5O,EAAGmJ,EAAG3E,EAAGZ,EAAGmL,EAASC,EAAY9O,EAAS+O,EAAQC,EAAQL,EAAMC,EAAMK,EAASC,EAAaC,EAAOC,EARhGtH,GAAKiE,EAAI,IAAIsD,QAAQxO,EAAa,SAAAyO,OAAWC,GAAKD,SAAWC,EAAI,OAAe,KAALA,EAAe,EAAIA,IAAMjI,MAAM3G,IAAgB,GAC7H8D,EAAO,GACP+K,EAAY,EACZC,EAAY,EAEZC,EAAW5H,EAAE3H,OACbyE,EAAS,EACT+K,EAAe,0BAA4B5D,MAOvCA,IAAM+B,MAAMhG,EAAE,KAAOgG,MAAMhG,EAAE,WACjCvE,QAAQqM,IAAID,GACLlL,MAEH3E,EAAI,EAAGA,EAAI4P,EAAU5P,OACzBoP,EAAcL,EACVf,MAAMhG,EAAEhI,IAEXgP,GADAD,EAAU/G,EAAEhI,GAAG+P,iBACW/H,EAAEhI,GAE5BA,IAEDwE,GAAKwD,EAAEhI,EAAI,GACX4D,GAAKoE,EAAEhI,EAAI,GACPgP,IACHxK,GAAKkL,EACL9L,GAAK+L,GAED3P,IACJiP,EAASzK,EACT0K,EAAStL,GAIM,MAAZmL,EACC7O,IACCA,EAAQG,OAAS,IACpBsE,EAAKtE,OAELyE,GAAU5E,EAAQG,QAGpBqP,EAAYT,EAASzK,EACrBmL,EAAYT,EAAStL,EACrB1D,EAAU,CAACsE,EAAGZ,GACde,EAAKqF,KAAK9J,GACVF,GAAK,EACL+O,EAAU,SAGJ,GAAgB,MAAZA,EAILC,IACJU,EAAYC,EAAY,IAHxBzP,EADIA,GACM,CAAC,EAAG,IAMP8J,KAAKxF,EAAGZ,EAAG8L,EAAuB,EAAX1H,EAAEhI,EAAI,GAAQ2P,EAAuB,EAAX3H,EAAEhI,EAAI,GAAS0P,GAAwB,EAAX1H,EAAEhI,EAAI,GAAU2P,GAAwB,EAAX3H,EAAEhI,EAAI,IACxHA,GAAK,OAGC,GAAgB,MAAZ+O,EACVF,EAAOa,EACPZ,EAAOa,EACa,MAAhBP,GAAuC,MAAhBA,IAC1BP,GAAQa,EAAYxP,EAAQA,EAAQG,OAAS,GAC7CyO,GAAQa,EAAYzP,EAAQA,EAAQG,OAAS,IAEzC2O,IACJU,EAAYC,EAAY,GAEzBzP,EAAQ8J,KAAK6E,EAAMC,EAAMtK,EAAGZ,EAAI8L,GAAwB,EAAX1H,EAAEhI,EAAI,GAAU2P,GAAwB,EAAX3H,EAAEhI,EAAI,IAChFA,GAAK,OAGC,GAAgB,MAAZ+O,EACVF,EAAOa,EA7EI,EAAI,GA6EKlL,EAAIkL,GACxBZ,EAAOa,EA9EI,EAAI,GA8EK/L,EAAI+L,GACnBX,IACJU,EAAYC,EAAY,GAEzBD,GAAwB,EAAX1H,EAAEhI,EAAI,GACnB2P,GAAwB,EAAX3H,EAAEhI,EAAI,GACnBE,EAAQ8J,KAAK6E,EAAMC,EAAMY,EApFd,EAAI,GAoFuBlL,EAAIkL,GAAwBC,EApFvD,EAAI,GAoFgE/L,EAAI+L,GAAwBD,EAAWC,GACtH3P,GAAK,OAGC,GAAgB,MAAZ+O,EACVF,EAAOa,EAAYxP,EAAQA,EAAQG,OAAS,GAC5CyO,EAAOa,EAAYzP,EAAQA,EAAQG,OAAS,GAC5CH,EAAQ8J,KAAK0F,EAAYb,EAAMc,EAAYb,EAAMtK,EA3FtC,EAAI,GA2FwCkL,EAAmB,IAAPb,EAAcrK,GAAgBZ,EA3FtF,EAAI,GA2FwF+L,EAAmB,IAAPb,EAAclL,GAAiB8L,EAAYlL,EAAKmL,EAAY/L,GAC/K5D,GAAK,OAGC,GAAgB,MAAZ+O,EACV5K,GAAKuL,EAAWC,EAAYD,EAAYlL,EAAImL,GAC5C3P,GAAK,OAGC,GAAgB,MAAZ+O,EAEV5K,GAAKuL,EAAWC,EAAWD,EAAYC,EAAYnL,GAAKwK,EAAaW,EAAYD,EAAY,IAC7F1P,GAAK,OAGC,GAAgB,MAAZ+O,GAA+B,MAAZA,EACb,MAAZA,IACHvK,EAAIyK,EACJrL,EAAIsL,EACJhP,EAAQ8P,QAAS,IAEF,MAAZjB,GAAyC,GAAtBvN,EAAKkO,EAAYlL,IAAkC,GAAtBhD,EAAKmO,EAAY/L,MACpEO,GAAKuL,EAAWC,EAAWnL,EAAGZ,GACd,MAAZmL,IACH/O,GAAK,IAGP0P,EAAYlL,EACZmL,EAAY/L,OAGN,GAAgB,MAAZmL,EAAiB,IAC3BM,EAAQrH,EAAEhI,EAAE,GACZsP,EAAQtH,EAAEhI,EAAE,GACZ6O,EAAO7G,EAAEhI,EAAE,GACX8O,EAAO9G,EAAEhI,EAAE,GACXmJ,EAAI,EACe,EAAfkG,EAAMhP,SACLgP,EAAMhP,OAAS,GAClByO,EAAOD,EACPA,EAAOS,EACPnG,MAEA2F,EAAOQ,EACPT,EAAOQ,EAAMY,OAAO,GACpB9G,GAAG,GAEJmG,EAAQD,EAAMa,OAAO,GACrBb,EAAQA,EAAMa,OAAO,IAEtBf,EAAU/C,aAAasD,EAAWC,GAAY3H,EAAEhI,EAAE,IAAKgI,EAAEhI,EAAE,IAAKgI,EAAEhI,EAAE,IAAKqP,GAAQC,GAAQN,EAAaU,EAAY,GAAU,EAALb,GAASG,EAAaW,EAAY,GAAU,EAALb,GAC9J9O,GAAKmJ,EACDgG,MACEhG,EAAI,EAAGA,EAAIgG,EAAQ9O,OAAQ8I,IAC/BjJ,EAAQ8J,KAAKmF,EAAQhG,IAGvBuG,EAAYxP,EAAQA,EAAQG,OAAO,GACnCsP,EAAYzP,EAAQA,EAAQG,OAAO,QAGnCoD,QAAQqM,IAAID,UAGd7P,EAAIE,EAAQG,QACJ,GACPsE,EAAKwL,MACLnQ,EAAI,GACME,EAAQ,KAAOA,EAAQF,EAAE,IAAME,EAAQ,KAAOA,EAAQF,EAAE,KAClEE,EAAQ8P,QAAS,GAElBrL,EAAK5B,YAAc+B,EAAS9E,EACrB2E,EAmDD,SAASyL,oBAAoBtL,EAAQuL,YAAAA,IAAAA,EAAU,WACjD7L,EAAIM,EAAO,GACdlB,EAAI,EACJ1D,EAAU,CAACsE,EAAGZ,GACd5D,EAAI,EACEA,EAAI8E,EAAOzE,OAAQL,GAAG,EAC5BE,EAAQ8J,KACPxF,EACAZ,EACAkB,EAAO9E,GACN4D,GAAKkB,EAAO9E,GAAKwE,GAAK6L,EAAY,EAClC7L,EAAIM,EAAO9E,IACX4D,UAGI1D,EAID,SAASoQ,gBAAgBxL,EAAQuL,GAEvC7O,EAAKsD,EAAO,GAAKA,EAAO,IAAM,MAAQtD,EAAKsD,EAAO,GAAKA,EAAO,IAAM,OAASA,EAASA,EAAOvE,MAAM,QAUlGgQ,EAAOC,EAAOxQ,EAAGyQ,EAAKC,EAAKC,EAAIC,EAAQC,EAAIC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EATjExQ,EAAImE,EAAOzE,OAAO,EACrBmE,GAAKM,EAAO,GACZlB,GAAKkB,EAAO,GACZsM,GAAStM,EAAO,GAChBuM,GAASvM,EAAO,GAChB5E,EAAU,CAACsE,EAAGZ,EAAGY,EAAGZ,GACpBiJ,EAAMuE,EAAQ5M,EACdsI,EAAMuE,EAAQzN,EACdoM,EAASrQ,KAAK8B,IAAIqD,EAAOnE,GAAK6D,GAAK,MAAS7E,KAAK8B,IAAIqD,EAAOnE,EAAE,GAAKiD,GAAK,SAErEoM,IACHlL,EAAOkF,KAAKoH,EAAOC,GACnBD,EAAQ5M,EACR6M,EAAQzN,EACRY,EAAIM,EAAOnE,EAAE,GACbiD,EAAIkB,EAAOnE,EAAE,GACbmE,EAAOwM,QAAQ9M,EAAGZ,GAClBjD,GAAG,GAEJ0P,EAAaA,GAA2B,IAAdA,GAAoBA,EAAY,EACrDrQ,EAAI,EAAGA,EAAIW,EAAGX,GAAG,EACrBuQ,EAAQ/L,EACRgM,EAAQ5M,EACRY,EAAI4M,EACJxN,EAAIyN,EACJD,GAAStM,EAAO9E,EAAE,GAClBqR,GAASvM,EAAO9E,EAAE,GACdwE,IAAM4M,GAASxN,IAAMyN,IAGzBZ,EAAM5D,EACN6D,EAAM5D,EACND,EAAMuE,EAAQ5M,EACdsI,EAAMuE,EAAQzN,EAIdiN,IAHAF,EAAKjP,EAAM+O,EAAMA,EAAMC,EAAMA,KAC7BE,EAAKlP,EAAMmL,EAAMA,EAAMC,EAAMA,KAEXuD,EAAY,IADxB3O,EAAM/B,SAACkN,EAAM+D,EAAKH,EAAME,EAAO,YAAK7D,EAAM8D,EAAKF,EAAMC,EAAO,IAIlEK,EAAMxM,IAFNsM,EAAMtM,GAAKA,EAAI+L,IAAUI,EAAKE,EAAKF,EAAK,OACxCI,EAAMvM,GAAK4M,EAAQ5M,IAAMoM,EAAKC,EAAKD,EAAK,IACdE,IAAc,EAALH,GAAUA,EAAKC,GAAO,IAAO,GAAM,IAGtEO,EAAMvN,IAFNqN,EAAMrN,GAAKA,EAAI4M,IAAUG,EAAKE,EAAKF,EAAK,OACxCO,EAAMtN,GAAKyN,EAAQzN,IAAMgN,EAAKC,EAAKD,EAAK,IACdK,IAAc,EAALN,GAAUA,EAAKC,GAAO,IAAO,GAAM,IAClEpM,IAAM+L,GAAS3M,IAAM4M,GACxBtQ,EAAQ8J,KACP3H,EAAOyO,EAAME,GACb3O,EAAO4O,EAAME,GACb9O,EAAOmC,GACPnC,EAAOuB,GACPvB,EAAO0O,EAAMC,GACb3O,EAAO6O,EAAMC,YAIhB3M,IAAM4M,GAASxN,IAAMyN,GAASnR,EAAQG,OAAS,EAAIH,EAAQ8J,KAAK3H,EAAO+O,GAAQ/O,EAAOgP,GAAQhP,EAAO+O,GAAQ/O,EAAOgP,IAAWnR,EAAQG,QAAU,EAC1H,IAAnBH,EAAQG,OACXH,EAAQ8J,KAAKxF,EAAGZ,EAAGY,EAAGZ,EAAGY,EAAGZ,GAClBoM,IACV9P,EAAQI,OAAO,EAAG,GAClBJ,EAAQG,OAASH,EAAQG,OAAS,GAE5BH,EA4ID,SAASwH,gBAAgB5H,GAC3BiC,EAAUjC,EAAQ,MACrBA,EAAU,CAACA,QAIXyR,EAAIlI,EAAGrJ,EAAGE,EAFP6L,EAAS,GACZpL,EAAIb,EAAQO,WAERgJ,EAAI,EAAGA,EAAI1I,EAAG0I,IAAK,KACvBnJ,EAAUJ,EAAQuJ,GAClB0C,GAAU,IAAM1J,EAAOnC,EAAQ,IAAM,IAAMmC,EAAOnC,EAAQ,IAAM,KAChEqR,EAAKrR,EAAQG,OACRL,EAAI,EAAGA,EAAIuR,EAAIvR,IACnB+L,GAAU1J,EAAOnC,EAAQF,MAAQ,IAAMqC,EAAOnC,EAAQF,MAAQ,IAAMqC,EAAOnC,EAAQF,MAAQ,IAAMqC,EAAOnC,EAAQF,MAAQ,IAAMqC,EAAOnC,EAAQF,MAAQ,IAAMqC,EAAOnC,EAAQF,IAAM,IAE7KE,EAAQ8P,SACXjE,GAAU,YAGLA,ECvlCG,SAAVyF,EAAUnN,OACLoN,EAAMpN,EAAQqN,eAAiBrN,IAC7BsN,KAAkBtN,EAAQuN,QAAU,gBAAiBvN,EAAQuN,QAElEC,GADAF,EAAiB,eACuB,eAElCF,EAAI7J,aAAe6J,EAAMA,EAAI7J,iBACpCkK,EAAOC,OACPC,EAAkB,IAAIC,EAClBR,EAAK,CAERS,GADAC,EAAOV,GACWW,gBAClBC,EAAQZ,EAAIa,MACZC,EAAOJ,EAAK/L,gBAAgB,6BAA8B,MAErDwL,MAAMY,UAAY,WAEnBC,EAAKhB,EAAIiB,cAAc,OAC1BC,EAAKlB,EAAIiB,cAAc,OACvBE,EAAOnB,IAAQA,EAAIa,MAAQb,EAAIoB,mBAC5BD,GAAQA,EAAKE,cAChBF,EAAKE,YAAYL,GACjBA,EAAGK,YAAYH,GACfF,EAAGhL,aAAa,QAAS,kDACzBsL,EAAiBJ,EAAGK,eAAiBP,EACrCG,EAAK9K,YAAY2K,WAGZhB,EAsCI,SAAZwB,EAAY5O,UAAWA,EAAQ6O,kBAA6D,SAAxC7O,EAAQwB,QAAU,IAAIC,cAA0BzB,EAAU,MAU7F,SAAjB8O,EAAkB9O,EAASrE,MACtBqE,EAAQuD,aAAeuK,GAAQX,EAAQnN,IAAW,KACjD+O,EAAMH,EAAU5O,GACnBgP,EAAKD,EAAOA,EAAI/P,aAAa,UAAY,6BAAgC,+BACzEuC,EAAOwN,EAAOpT,EAAI,OAAS,IAAO,MAClCwE,EAAU,IAANxE,EAAU,EAAI,IAClB4D,EAAU,IAAN5D,EAAU,IAAM,EACpBsT,EAAM,0EACNrQ,EAAIkP,EAAK/L,gBAAkB+L,EAAK/L,gBAAgBiN,EAAG9D,QAAQ,SAAU,QAAS3J,GAAQuM,EAAKO,cAAc9M,UACtG5F,IACEoT,GAScG,EAAlBA,GAAkCJ,EAAe9O,GACjDpB,EAAEwE,aAAa,QAAS,KACxBxE,EAAEwE,aAAa,SAAU,KACzBxE,EAAEwE,aAAa,YAAa,aAAejD,EAAI,IAAMZ,EAAI,KACzD2P,EAAcT,YAAY7P,KAZrBuQ,KACJA,EAAgBL,EAAe9O,IACjBuN,MAAM6B,QAAUH,GAE/BrQ,EAAE2O,MAAM6B,QAAUH,EAAM,gCAAkC1P,EAAI,WAAaY,EAAI,KAC/EgP,EAAcV,YAAY7P,KAUrBA,OAEF,4BAuBU,SAAjByQ,GAAkBrP,EAASsP,OAMzBC,EAAWpE,EAAGvH,EAAGzD,EAAGZ,EAAGiQ,EALpBT,EAAMH,EAAU5O,GACnByP,EAAYzP,IAAY+O,EACxBW,EAAWX,EAAMY,EAAYC,EAC7BC,EAAS7P,EAAQuD,WACjBuM,EAAaD,IAAWd,GAAOc,EAAOE,YAAcF,EAAOE,WAAWtB,YAAcoB,EAAOE,WAAaF,KAErG7P,IAAYyN,SACRzN,KAER0P,EAAS1T,QAAU0T,EAAS/J,KAAKmJ,EAAe9O,EAAS,GAAI8O,EAAe9O,EAAS,GAAI8O,EAAe9O,EAAS,IACjHuP,EAAYR,EAAMG,EAAgBC,EAC9BJ,EACCU,GAEHtP,IADAyD,EA3BO,SAAVoM,QAAUjB,OAERZ,EADGhD,EAAI4D,EAAIkB,gBAEP9E,IACJgD,EAAYY,EAAIxB,MAAMD,GACtByB,EAAIxB,MAAMD,GAAkB,OAC5ByB,EAAIN,YAAYP,GAChB/C,EAAI+C,EAAK+B,SACTlB,EAAItL,YAAYyK,GAChBC,EAAaY,EAAIxB,MAAMD,GAAkBa,EAAaY,EAAIxB,MAAM2C,eAAe5C,EAAepC,QAAQ,WAAY,OAAOzJ,gBAEnH0J,GAAKwC,EAAgBwC,QAgBtBH,CAAQhQ,IACLpB,EAAIgF,EAAED,EACbpE,GAAKqE,EAAEwM,EAAIxM,EAAEgE,EACbuD,EAAIwC,GACM3N,EAAQ2B,SAClBiC,EAAI5D,EAAQ2B,UAGZxB,GADAgL,GADAA,EAAInL,EAAQmO,UAAYnO,EAAQmO,UAAUkC,QAAU,IAC7CC,cAAoD,EAAlBnF,EAAEmF,cA1C/B,SAAfC,aAAepF,WACVtH,EAAI,IAAI+J,EACXjS,EAAI,EACEA,EAAIwP,EAAEmF,cAAe3U,IAC3BkI,EAAE2M,SAASrF,EAAEsF,QAAQ9U,GAAG+U,eAElB7M,EAoC0D0M,CAAapF,GAAKA,EAAEsF,QAAQ,GAAGC,OAAvE/C,GACjBhK,EAAIC,EAAEzD,EAAIgL,EAAEtH,EAAID,EAAErE,EACxBA,EAAI4L,EAAEvH,EAAIA,EAAEzD,EAAIgL,EAAEvD,EAAIhE,EAAErE,IAExB4L,EAAI,IAAIyC,EACRzN,EAAIZ,EAAI,GAEL+P,GAAmD,MAAlCtP,EAAQwB,QAAQC,gBACpCtB,EAAIZ,EAAI,IAERkQ,EAAYV,EAAMc,GAAQpB,YAAYc,GACvCA,EAAUnM,aAAa,YAAa,UAAY+H,EAAExH,EAAI,IAAMwH,EAAEvH,EAAI,IAAMuH,EAAEtH,EAAI,IAAMsH,EAAEvD,EAAI,KAAOuD,EAAEvM,EAAIuB,GAAK,KAAOgL,EAAEiF,EAAI7Q,GAAK,SACxH,IACNY,EAAIZ,EAAI,EACJmP,MACHvD,EAAInL,EAAQ2O,aACZ/K,EAAI5D,GACS4D,EAANA,GAAUA,EAAEL,aAAeK,IAAMuH,GAAKvH,EAAEL,YACe,GAAxDkK,EAAKkD,iBAAiB/M,GAAG0J,GAAkB,IAAItR,SACnDmE,EAAIyD,EAAEgN,WACNrR,EAAIqE,EAAEiN,UACNjN,EAAI,MAKa,cADpB4L,EAAK/B,EAAKkD,iBAAiB3Q,IACpB8Q,UAA2C,UAAhBtB,EAAGsB,aACpC3F,EAAInL,EAAQ2O,aACLkB,GAAUA,IAAW1E,GAC3BhL,GAAK0P,EAAOkB,YAAc,EAC1BxR,GAAKsQ,EAAOmB,WAAa,EACzBnB,EAASA,EAAOtM,YAGlBK,EAAI2L,EAAUhC,OACZ0D,IAAOjR,EAAQ6Q,UAAYtR,EAAK,KAClCqE,EAAEsN,KAAQlR,EAAQ4Q,WAAazQ,EAAK,KACpCyD,EAAE0J,GAAkBkC,EAAGlC,GACvB1J,EAAE4J,GAAwBgC,EAAGhC,GAM7B5J,EAAEkN,SAA2B,UAAhBtB,EAAGsB,SAAuB,QAAU,WACjDhB,EAAWrB,YAAYc,UAEjBA,EAEK,SAAb4B,GAAchG,EAAGxH,EAAGC,EAAGC,EAAG+D,EAAGhJ,EAAGwR,UAC/BjF,EAAExH,EAAIA,EACNwH,EAAEvH,EAAIA,EACNuH,EAAEtH,EAAIA,EACNsH,EAAEvD,EAAIA,EACNuD,EAAEvM,EAAIA,EACNuM,EAAEiF,EAAIA,EACCjF,EAhNT,IAAI2C,EAAML,EAAMI,EAAaG,EAAOmB,EAAeD,EAAevB,EAAiBO,EAGlFQ,IAFApB,EAAiB,YACjBE,EAAuBF,EAAiB,SAgExCqC,EAAY,GACZC,EAAY,GAgJAhC,0BAKZwD,QAAA,uBACMzN,EAAoB0N,KAApB1N,EAAGC,EAAiByN,KAAjBzN,EAAGC,EAAcwN,KAAdxN,EAAG+D,EAAWyJ,KAAXzJ,EAAGhJ,EAAQyS,KAARzS,EAAGwR,EAAKiB,KAALjB,EACnBkB,EAAe3N,EAAIiE,EAAIhE,EAAIC,GAAM,aAC3BsN,GACNE,KACAzJ,EAAI0J,GACH1N,EAAI0N,GACJzN,EAAIyN,EACL3N,EAAI2N,GACHzN,EAAIuM,EAAIxI,EAAIhJ,GAAK0S,IAChB3N,EAAIyM,EAAIxM,EAAIhF,GAAK0S,MAIrBd,SAAA,kBAASE,OACH/M,EAAoB0N,KAApB1N,EAAGC,EAAiByN,KAAjBzN,EAAGC,EAAcwN,KAAdxN,EAAG+D,EAAWyJ,KAAXzJ,EAAGhJ,EAAQyS,KAARzS,EAAGwR,EAAKiB,KAALjB,EACnBmB,EAAKb,EAAO/M,EACZ6N,EAAKd,EAAO7M,EACZ4N,EAAKf,EAAO9M,EACZ0K,EAAKoC,EAAO9I,EACZ8J,EAAKhB,EAAO9R,EACZ+S,EAAKjB,EAAON,SACNe,GAAWE,KACjBE,EAAK5N,EAAI8N,EAAK5N,EACd0N,EAAK3N,EAAI6N,EAAK7J,EACd4J,EAAK7N,EAAI2K,EAAKzK,EACd2N,EAAK5N,EAAI0K,EAAK1G,EACdhJ,EAAI8S,EAAK/N,EAAIgO,EAAK9N,EAClBuM,EAAIsB,EAAK9N,EAAI+N,EAAK/J,MAGpBuI,MAAA,wBACQ,IAAIvC,SAASyD,KAAK1N,EAAG0N,KAAKzN,EAAGyN,KAAKxN,EAAGwN,KAAKzJ,EAAGyJ,KAAKzS,EAAGyS,KAAKjB,MAGlEwB,OAAA,gBAAOlB,OACD/M,EAAoB0N,KAApB1N,EAAGC,EAAiByN,KAAjBzN,EAAGC,EAAcwN,KAAdxN,EAAG+D,EAAWyJ,KAAXzJ,EAAGhJ,EAAQyS,KAARzS,EAAGwR,EAAKiB,KAALjB,SACZzM,IAAM+M,EAAO/M,GAAKC,IAAM8M,EAAO9M,GAAKC,IAAM6M,EAAO7M,GAAK+D,IAAM8I,EAAO9I,GAAKhJ,IAAM8R,EAAO9R,GAAKwR,IAAMM,EAAON,KAGhHyB,MAAA,eAAMpK,EAAOJ,YAAAA,IAAAA,EAAU,QACjBlH,EAAQsH,EAARtH,EAAGZ,EAAKkI,EAALlI,EACNoE,EAAoB0N,KAApB1N,EAAGC,EAAiByN,KAAjBzN,EAAGC,EAAcwN,KAAdxN,EAAG+D,EAAWyJ,KAAXzJ,EAAGhJ,EAAQyS,KAARzS,EAAGwR,EAAKiB,KAALjB,SACjB/I,EAAUlH,EAAKA,EAAIwD,EAAIpE,EAAIsE,EAAIjF,GAAM,EACrCyI,EAAU9H,EAAKY,EAAIyD,EAAIrE,EAAIqI,EAAIwI,GAAM,EAC9B/I,+BAjDI1D,EAAKC,EAAKC,EAAK+D,EAAKhJ,EAAKwR,YAAzBzM,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAG+D,IAAAA,EAAE,YAAGhJ,IAAAA,EAAE,YAAGwR,IAAAA,EAAE,GACtCe,GAAWE,KAAM1N,EAAGC,EAAGC,EAAG+D,EAAGhJ,EAAGwR,GA4D3B,SAAS0B,gBAAgB9R,EAASoR,EAAS9B,EAAeyC,OAC3D/R,IAAYA,EAAQuD,aAAeuK,GAAQX,EAAQnN,IAAU+N,kBAAoB/N,SAC9E,IAAI4N,MAERoE,EAnPiB,SAArBC,mBAAqBrT,WAChB+E,EAAGuO,EACAtT,GAAKA,IAAMoP,IACjBkE,EAAQtT,EAAEuT,QACDD,EAAME,SAAWF,EAAMG,IAAIzT,EAAG,KACnCsT,IAAUA,EAAMI,SAAWJ,EAAMK,QAAUL,EAAMM,kBACpDN,EAAMI,OAASJ,EAAMK,OAAS,KAC9BL,EAAMM,gBAAgB,EAAGN,GACzBvO,EAAIA,EAAEgC,KAAKuM,GAAUvO,EAAI,CAACuO,IAE3BtT,EAAIA,EAAE2E,kBAEAI,EAuOSsO,CAAmBjS,GAEnCyS,EADM7D,EAAU5O,GACF2P,EAAYC,EAC1BL,EAAYF,GAAerP,EAASsP,GACpCoD,EAAKD,EAAM,GAAGE,wBACdnB,EAAKiB,EAAM,GAAGE,wBACdC,EAAKH,EAAM,GAAGE,wBACd9C,EAASN,EAAUhM,WACnBsP,GAAWd,GAtND,SAAXe,SAAW9S,SACsC,UAA5CyN,EAAKkD,iBAAiB3Q,GAAS8Q,YAGnC9Q,EAAUA,EAAQuD,aACkB,IAArBvD,EAAQ+S,SACfD,SAAS9S,WAgNkB8S,CAAS9S,GAC5CmL,EAAI,IAAIyC,GACN4D,EAAGN,KAAOwB,EAAGxB,MAAQ,KACrBM,EAAGP,IAAMyB,EAAGzB,KAAO,KACnB2B,EAAG1B,KAAOwB,EAAGxB,MAAQ,KACrB0B,EAAG3B,IAAMyB,EAAGzB,KAAO,IACpByB,EAAGxB,MAAQ2B,EAAU,EA9NH,SAApBG,2BAA0BvF,EAAKwF,aAAenF,EAAKiD,YAAclD,EAAYkD,YAAc/C,EAAM+C,YAAc,EA8NpFiC,IACzBN,EAAGzB,KAAO4B,EAAU,EAhOH,SAAnBK,0BAAyBzF,EAAK0F,aAAgBrF,EAAKkD,WAAanD,EAAYmD,WAAahD,EAAMgD,WAAa,EAgOlFkC,QAE1BrD,EAAOpM,YAAY8L,GACfyC,MACHU,EAAKV,EAAWhW,OACT0W,MACNlB,EAAKQ,EAAWU,IACbJ,OAASd,EAAGe,OAAS,EACxBf,EAAGgB,gBAAgB,EAAGhB,UAGjBJ,EAAUjG,EAAEiG,UAAYjG,ECvSH,SAA5BiI,GAA6BvX,EAASwX,EAAQC,EAAUC,WACnDjX,EAAI+W,EAAOrX,OACduJ,EAAc,IAATgO,EAAa,EAAIA,EACtB5X,EAAI,EAEEA,EAAIW,EAAGX,IACbE,EAAQ0J,GAAUiO,WAAWH,EAAO1X,GAAG2X,IAC9B,IAATC,IAAe1X,EAAQ0J,EAAG,GAAK,GAC/BA,GAAM,SAEA1J,EAEM,SAAd4X,GAAeC,EAAQC,EAAMC,UAASJ,WAAWE,EAAOvB,MAAME,IAAIqB,EAAQC,EAAMC,GAAQ,QAAU,EACpF,SAAdC,GAAchY,OAGZF,EAFGwE,EAAItE,EAAQ,GACf0D,EAAI1D,EAAQ,OAERF,EAAI,EAAGA,EAAIE,EAAQG,OAAQL,GAAG,EAClCwE,EAAKtE,EAAQF,IAAMwE,EACnBZ,EAAK1D,EAAQF,EAAE,IAAM4D,EAkBH,SAApBuU,GAAqBC,EAAQlY,EAAS6X,EAAQvT,EAAGZ,EAAGyU,EAAQC,EAAMC,EAAOC,UAEvEtY,EADiB,UAAdoY,EAAK1S,KACE,CAAC1F,KAEU,IAArBoY,EAAKG,aAAyBvY,EAAQoR,QAAQwG,GAAYC,EAAQvT,EAAG+T,GAAQ3U,EAAIkU,GAAYC,EAAQnU,EAAG4U,GAAS,GACjHF,EAAKI,UAAYR,GAAYhY,GAEnB,EADM0D,EAAI0M,gBAAkBF,qBACjBlQ,EAASoY,EAAKjI,aAEpCnQ,EAAUmY,EAAOM,GAAOzY,EAAS6X,EAAQO,IACzCM,GAAyBR,EAAQL,EAAQvT,EAAGtE,EAAS,IAAKqY,GAC1D3U,GAAKgV,GAAyBR,EAAQL,EAAQnU,EAAG1D,EAAS,IAAKsY,GACxD1P,yBAAyB5I,EAASoY,EAAKxV,aAAkC,IAAnBwV,EAAKjI,UAAkB,GAAK,KAE7E,SAAbwI,GAAaC,UAAKA,EAED,SAAjBC,GAAkB1U,EAAS2U,EAAQC,OAIjC7F,EAHG5D,EAAI2G,gBAAgB9R,GACvBG,EAAI,EACJZ,EAAI,QAEwC,SAAxCS,EAAQwB,QAAU,IAAIC,eAC1BsN,EAAM/O,EAAQ6U,QAAQxE,SAClBzN,QAAUmM,EAAM,CAACnM,OAAQ5C,EAAQhB,aAAa,SAAU6D,QAAS7C,EAAQhB,aAAa,YAE1F+P,EAAM4F,GAAU3U,EAAQ2B,SAAW3B,EAAQ2B,UAExCgT,GAAqB,SAAXA,IACbxU,EAAIwU,EAAOhP,KAAOgP,EAAO,IAAM5F,EAAMA,EAAInM,MAAQ5C,EAAQ8U,aAAe,GAAKH,EAAOxU,EACpFZ,EAAIoV,EAAOhP,KAAOgP,EAAO,IAAM5F,EAAMA,EAAIlM,OAAS7C,EAAQ+U,cAAgB,GAAKJ,EAAOpV,GAEhFqV,EAAa/C,MAAO1R,GAAKZ,EAAI4L,EAAE0G,MAAM,CAAC1R,EAAGA,EAAGZ,EAAGA,IAAM,CAACY,EAAGgL,EAAEvM,EAAGW,EAAG4L,EAAEiF,IAEzD,SAAlB4E,GAAmBC,EAAaC,EAAWC,EAAYC,OAKrDC,EAJGT,EAAe9C,gBAAgBmD,EAAY1R,YAAY,GAAM,GAChE4H,EAAIyJ,EAAazE,QAAQK,SAASsB,gBAAgBoD,IAClDI,EAAYZ,GAAeO,EAAaE,EAAYP,KAC3CF,GAAeQ,EAAWE,EAAUR,GAA5CzU,IAAAA,EAAGZ,IAAAA,SAEL4L,EAAEvM,EAAIuM,EAAEiF,EAAI,EACK,SAAbgF,GAAuBF,EAAUK,gBAAsD,SAApCL,EAAU1T,QAAQC,gBACxE4T,EAAIH,EAAUlW,aAAa,KAAKmE,MAAMqS,IAAY,GAElDrV,IADAkV,EAAIlK,EAAE0G,MAAM,CAAC1R,GAAGkV,EAAE,GAAI9V,GAAG8V,EAAE,MACpBlV,EACPZ,GAAK8V,EAAE9V,GAGJ8V,IAEHlV,IADAkV,EAAIlK,EAAE0G,MAAMqD,EAAUvT,YACfxB,EACPZ,GAAK8V,EAAE9V,GAER4L,EAAEvM,EAAIuB,EAAImV,EAAUnV,EACpBgL,EAAEiF,EAAI7Q,EAAI+V,EAAU/V,EACb4L,EAhGT,IAGCsK,EAAMC,EAAWC,EAAUC,EAAUC,EAAgBC,EAHlDC,GAAU,wCAAwCtT,MAAM,KAC3DuT,GAAU,sCAAsCvT,MAAM,KACtD7F,EAAWtB,KAAKuB,GAAK,IAuDrB2Y,EAAU,+BAyCVlB,GAAS,SAATA,OAAU7Y,EAASiY,SAKjBuC,EAAa9K,EAAGkK,EALUa,IAAAA,MAAOxF,IAAAA,OAAQyF,IAAAA,QAASC,IAAAA,QAASC,IAAAA,YACxDlW,EAAI1E,EAAQ,GAAG,GAClB8D,EAAI9D,EAAQ,GAAG,GACf6a,EAAO7C,GAAYC,EAAQ,KAC3B6C,EAAO9C,GAAYC,EAAQ,YAEvBjY,GAAYA,EAAQO,QAGrBka,IACW,SAAVA,IAAsBD,EAAcL,EAASM,GAAO,IAAMxC,KAAYA,EACzE/L,iBAAiBlM,EAAS,EAAG,EAAG,EAAG,EAAG6a,EAAOnW,EAAGoW,EAAOhX,IAEnD8W,IAAkC,IAAnBA,EAAY,GAC9BZ,EAAKe,IAAI9C,EAAQ,CAAC+C,gBAAkC,IAAjBJ,EAAY,GAAY,KAAyB,IAAjBA,EAAY,GAAY,MAE3FA,EAAc,CAAC5C,GAAYC,EAAQ,aAAe,IAAKD,GAAYC,EAAQ,aAAe,KAG3F2B,GADAlK,EAAI6J,GAAgBtB,EAAQuC,EAAaI,EAAa,SAChDxE,MAAM,CAAC1R,EAAGA,EAAGZ,EAAGA,IACtBoI,iBAAiBlM,EAAS0P,EAAExH,EAAGwH,EAAEvH,EAAGuH,EAAEtH,EAAGsH,EAAEvD,EAAG0O,EAAOnL,EAAEvM,GAAKyW,EAAElV,EAAIgL,EAAEvM,GAAI2X,EAAOpL,EAAEiF,GAAKiF,EAAE9V,EAAI4L,EAAEiF,MAG5FM,EACH/I,iBAAiBlM,EAASiV,EAAO/M,EAAG+M,EAAO9M,EAAG8M,EAAO7M,EAAG6M,EAAO9I,EAAG8I,EAAO9R,EAAG8R,EAAON,IACzE+F,GAAWC,IACrBzO,iBAAiBlM,EAAS,EAAG,EAAG,EAAG,EAAG0a,GAAW,EAAGC,GAAW,GAEzD3a,GArBCkD,WAAW,aAuBpB4V,GAA2B,SAA3BA,yBAA4BR,EAAQL,EAAQJ,EAAU7X,EAASib,EAAcC,OACxEzE,EAAQwB,EAAOvB,MAClByE,EAAU1E,EAAM0E,QAChBC,EAASD,GAAWA,EAAQE,SAAWF,EAAQE,QAAQxD,GACvDK,EAAOkD,GAASA,EAAM1U,QAAQ,KAAO,EAAI0U,EAAQvD,EACjDyD,EAAKhD,EAAOiD,IAAM,IAAItB,EAAU3B,EAAOiD,IAAKtD,EAAQC,EAAM,EAAG,EAAGa,GAAY,EAAGtC,EAAMsE,IAAI9C,EAAQC,EAAMI,IACxGgD,EAAGE,EAAItB,EAASzD,EAAMG,IAAIqB,EAAQC,EAAMgD,KAAe,EACvDI,EAAGzW,KAAO7E,EACVsb,EAAGG,GAAKR,EACR3C,EAAOoD,OAAOxR,KAAKgO,IAKRyD,EAAmB,CAC/BC,QAAS,SACTvV,KAAM,aACNwV,2BAASC,EAAMC,EAAQC,GAEtB9B,GADAF,EAAO8B,GACSG,MAAMC,QACtB/B,EAAWH,EAAKiC,MAAME,QACtB/B,EAAiBJ,EAAK8B,KAAKM,cAC3B/B,EAAaL,EAAK8B,KAAKO,WAAa,aACpCpC,EAAY+B,GAEbM,mBAAKrE,EAAQO,EAAM+D,OACbvC,SACJrW,QAAQC,KAAK,iDACN,EAEe,iBAAV4U,IAAuBA,EAAK1G,OAAW0G,EAAK3T,OACxD2T,EAAO,CAAC3T,KAAK2T,QAMbxY,EAAS4Z,EAJN4C,EAAW,GACb3X,EAAwC2T,EAAxC3T,KAAM4X,EAAkCjE,EAAlCiE,WAAYhE,EAAsBD,EAAtBC,MAAOC,EAAeF,EAAfE,MAAOhU,EAAQ8T,EAAR9T,EAAGZ,EAAK0U,EAAL1U,EACpC4Y,EAAW7X,EAAK,GAChB0T,EAzBe,SAAjBoE,eAAkBrU,EAAOC,UAAQ,SAAAvI,UAAYsI,GAAiB,IAARC,EAAaF,aAAarI,EAASsI,EAAOC,GAAOvI,GAyB5F2c,CAAenE,EAAKlQ,MAAQ,QAASkQ,EAAQA,EAAKjQ,IAAM,WAE7DiU,SAAWA,OACXvE,OAASA,OACTsE,MAAQA,OACRK,OAASxC,GAAkBA,EAAenC,EAAQ,cAClDrC,KAAKiH,OAAUJ,GAA6B,IAAfA,UAC5BK,QAAU/E,WAAW0E,IAAe,OACpCM,UAAYvE,EAAKwE,gBACjBC,MAAQzE,EAAK0E,UAAY,gBACzBC,KAAOlF,EAAOvB,MAAMqE,IAAI9C,EAAQrC,KAAKqH,MAAOrH,WAC5CwH,GAAKlD,EAASjC,EAAOvB,MAAME,IAAIqB,EAAQrC,KAAKqH,SAAW,IAEzDI,MAAMC,QAAQzY,IAAW,WAAYA,GAA8B,iBAAd6X,EAkBxD1T,yBADAhJ,EAAUuY,EAAOM,GAAO3V,WAAWsV,EAAK3T,MAAOoT,EAAQO,IACrBA,EAAKxV,YACvCwZ,EAAStS,KAAKlK,GACd8Y,GAAyBlD,KAAMqC,EAAQO,EAAK9T,GAAK,IAAK1E,EAAS,IAAKwY,EAAKC,OAAS,MAClFK,GAAyBlD,KAAMqC,EAAQO,EAAK1U,GAAK,IAAK9D,EAAS,IAAKwY,EAAKE,OAAS,UArBF,KAC3EkB,KAAK8C,GACJhY,IAAM4V,GAAQ5T,QAAQkT,GAC1BlV,EAAIkV,GACO9V,IAAMyW,GAAQ7T,QAAQkT,KACjC9V,EAAI8V,OAQDA,KALDlV,GAAKZ,EACR0Y,EAAStS,KAAKmO,GAAkBzC,KAAM+B,GAA0BA,GAA0B,GAAI9S,EAAMH,EAAG,GAAIG,EAAMf,EAAG,GAAImU,EAAQvT,EAAGZ,EAAGyU,EAAQC,EAAMC,GAASyB,EAASrV,EAAK,GAAGH,IAAKgU,GAASwB,EAASrV,EAAK,GAAGf,MAE7MY,EAAIZ,EAAI,EAEC4Y,EACT9C,IAAMlV,GAAKkV,IAAM9V,GAAK0Y,EAAStS,KAAKmO,GAAkBzC,KAAM+B,GAA0B,GAAI9S,EAAM+U,EAAG,GAAI3B,EAAQ2B,EAAG,EAAGrB,EAAQC,EAAM0B,EAASrV,EAAK,GAAG+U,MAStJ2C,EAAM/D,KAAK+E,iBAAmB3H,KAAK4H,OAAOjB,EAAMja,WAAYsT,OAE7D4H,uBAAOC,EAAOhZ,OACT+X,EAAW/X,EAAK+X,SACnBtc,EAAIsc,EAASjc,OACb+a,EAAK7W,EAAK8W,OACP9W,EAAK8X,MAAMmB,QAAUrD,IAAc,KAC1B,EAARoD,EACHA,EAAQ,EACEA,EAAQ,IAClBA,EAAQ,GAEFvd,KACN4L,kBAAkB0Q,EAAStc,GAAIud,GAAQvd,GAAKuE,EAAKoY,OAAQL,EAAStc,SAE5Dob,GACNA,EAAGP,IAAIO,EAAGnb,EAAGmb,EAAG1B,EAAG0B,EAAGzW,KAAKyW,EAAGG,IAAMH,EAAGE,EAAGF,EAAGnP,EAAGsR,GAChDnC,EAAKA,EAAGqC,MAETlZ,EAAKoY,QAAUpY,EAAK0Y,KAAK1Y,EAAKwT,OAAQxT,EAAKwY,MAAOT,EAAS,GAAGvS,OAASxF,EAAKsY,QAAU5b,EAAW,GAAKsD,EAAKqY,QAAUrY,EAAK2Y,GAAI3Y,EAAMgZ,QAEpIhZ,EAAKmY,OAAOgB,UAGdC,6BAAUhZ,UACFmE,yBAAyB9F,WAAW2B,IAAOhC,aAEnDwF,aAAAA,aACAnF,WAAAA,WACAsN,gBAAAA,gBACA9M,gBAAAA,gBACAkE,gBAAAA,gBACAsE,iBAAAA,iBACAmK,gBAAAA,gBACAvK,kBAAAA,kBACA9C,yBAAAA,yBACA1E,cAAe,yBAACwZ,EAAStZ,UAAS2V,EAAS2D,GAASC,IAAI,SAAA9F,UAAU3T,cAAc2T,GAAiB,IAATzT,MACxFwZ,+CAAmBxE,EAAaC,EAAWzN,OACtC0D,EAAI2G,gBAAgBoD,GAAW,GAAM,GAAM1E,SAASsB,gBAAgBmD,WACjExN,EAAQ0D,EAAE0G,MAAMpK,GAAS0D,GAEjCuO,eAAgB1E,GAChB2E,iDAAoB1E,EAAaC,EAAWC,EAAYC,OACnDjK,EAAG6J,GAAgBC,EAAaC,EAAWC,EAAYC,SACpD,CAACjV,EAAGgL,EAAEvM,EAAGW,EAAG4L,EAAEiF,IAEtBwJ,uCAAexe,EAAO6Y,OAEjBpY,EAAUuX,GAA0BA,GAA0B,GAAIhY,GADtE6Y,EAAOA,GAAQ,IACmE9T,GAAK,IAAK,GAAI/E,EAAO6Y,EAAK1U,GAAK,IAAK,UACtH0U,EAAKI,UAAYR,GAAYhY,GACtB,CAAgB,UAAdoY,EAAK1S,KAAoB1F,EAAUoQ,gBAAgBpQ,EAASoY,EAAKjI,eAtPhE,SAAX6N,kBAAiBpE,GAA4B,oBAAZ/H,SAA4B+H,EAAO/H,OAAO+H,OAASA,EAAKqE,gBAAkBrE,EA0P5GoE,IAAcpE,EAAKqE,eAAe1C"}