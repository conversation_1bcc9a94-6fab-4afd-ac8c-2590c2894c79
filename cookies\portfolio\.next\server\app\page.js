/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYXJraXQlNUNEZXNrdG9wJTVDY29va2llcyU1Q3BvcnRmb2xpbyU1Q3NyYyU1Q2FwcCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BvcnRmb2xpby8/ZmU0OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFya2l0XFxcXERlc2t0b3BcXFxcY29va2llc1xcXFxwb3J0Zm9saW9cXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/Hero */ \"(ssr)/./src/components/sections/Hero.tsx\");\n/* harmony import */ var _components_sections_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/About */ \"(ssr)/./src/components/sections/About.tsx\");\n/* harmony import */ var _components_sections_Skills__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/Skills */ \"(ssr)/./src/components/sections/Skills.tsx\");\n/* harmony import */ var _components_sections_Experience__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/Experience */ \"(ssr)/./src/components/sections/Experience.tsx\");\n/* harmony import */ var _components_sections_Projects__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sections/Projects */ \"(ssr)/./src/components/sections/Projects.tsx\");\n/* harmony import */ var _components_sections_Contact__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/sections/Contact */ \"(ssr)/./src/components/sections/Contact.tsx\");\n/* harmony import */ var _components_ScrollAnimations__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ScrollAnimations */ \"(ssr)/./src/components/ScrollAnimations.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Home() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Smooth scrolling for anchor links\n        const handleSmoothScroll = (e)=>{\n            const target = e.target;\n            if (target.hash) {\n                e.preventDefault();\n                const element = document.querySelector(target.hash);\n                element?.scrollIntoView({\n                    behavior: \"smooth\"\n                });\n            }\n        };\n        document.addEventListener(\"click\", handleSmoothScroll);\n        return ()=>document.removeEventListener(\"click\", handleSmoothScroll);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollAnimations__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-md border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white font-bold text-xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"PORTFOLIO\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#about\",\n                                        className: \"text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide\",\n                                        children: \"About Me\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#experience\",\n                                        className: \"text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide\",\n                                        children: \"Experience\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#projects\",\n                                        className: \"text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide\",\n                                        children: \"Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide\",\n                                    children: \"GET IN TOUCH\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_About__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Skills__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Experience__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Projects__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Contact__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ScrollAnimations.tsx":
/*!*********************************************!*\
  !*** ./src/components/ScrollAnimations.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollAnimations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! gsap */ \"(ssr)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(ssr)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ScrollAnimations() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Register ScrollTrigger plugin\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__.ScrollTrigger);\n        // Smooth scrolling\n        const initLenis = async ()=>{\n            try {\n                const Lenis = (await __webpack_require__.e(/*! import() */ \"vendor-chunks/lenis\").then(__webpack_require__.bind(__webpack_require__, /*! lenis */ \"(ssr)/./node_modules/lenis/dist/lenis.mjs\"))).default;\n                const lenis = new Lenis({\n                    duration: 1.2,\n                    easing: (t)=>Math.min(1, 1.001 - Math.pow(2, -10 * t)),\n                    smooth: true\n                });\n                function raf(time) {\n                    lenis.raf(time);\n                    requestAnimationFrame(raf);\n                }\n                requestAnimationFrame(raf);\n            } catch (error) {\n                console.log(\"Lenis not available, using default scroll\");\n            }\n        };\n        // Initialize smooth scrolling\n        initLenis();\n        // Parallax background elements\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(\".parallax-slow\", {\n            yPercent: -50,\n            ease: \"none\",\n            scrollTrigger: {\n                trigger: \"body\",\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: true\n            }\n        });\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(\".parallax-fast\", {\n            yPercent: -100,\n            ease: \"none\",\n            scrollTrigger: {\n                trigger: \"body\",\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: true\n            }\n        });\n        // Text reveal animations\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.utils.toArray(\".reveal-text\").forEach((element)=>{\n            gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.fromTo(element, {\n                y: 100,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 1,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: element,\n                    start: \"top 80%\",\n                    end: \"bottom 20%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n        });\n        // Stagger animations for cards\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.utils.toArray(\".stagger-item\").forEach((element, index)=>{\n            gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.fromTo(element, {\n                y: 60,\n                opacity: 0,\n                scale: 0.9\n            }, {\n                y: 0,\n                opacity: 1,\n                scale: 1,\n                duration: 0.8,\n                delay: index * 0.1,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: element,\n                    start: \"top 85%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n        });\n        // Horizontal scroll for projects\n        const projectsContainer = document.querySelector(\".horizontal-scroll\");\n        if (projectsContainer) {\n            const projects = gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.utils.toArray(\".project-card\");\n            gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(projects, {\n                xPercent: -100 * (projects.length - 1),\n                ease: \"none\",\n                scrollTrigger: {\n                    trigger: projectsContainer,\n                    pin: true,\n                    scrub: 1,\n                    snap: 1 / (projects.length - 1),\n                    end: ()=>\"+=\" + projectsContainer.offsetWidth\n                }\n            });\n        }\n        // Magnetic effect for buttons\n        const magneticElements = document.querySelectorAll(\".magnetic\");\n        magneticElements.forEach((element)=>{\n            const handleMouseMove = (e)=>{\n                const rect = element.getBoundingClientRect();\n                const x = e.clientX - rect.left - rect.width / 2;\n                const y = e.clientY - rect.top - rect.height / 2;\n                gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(element, {\n                    x: x * 0.3,\n                    y: y * 0.3,\n                    duration: 0.3,\n                    ease: \"power2.out\"\n                });\n            };\n            const handleMouseLeave = ()=>{\n                gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(element, {\n                    x: 0,\n                    y: 0,\n                    duration: 0.5,\n                    ease: \"elastic.out(1, 0.3)\"\n                });\n            };\n            element.addEventListener(\"mousemove\", handleMouseMove);\n            element.addEventListener(\"mouseleave\", handleMouseLeave);\n        });\n        // Cleanup\n        return ()=>{\n            gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__.ScrollTrigger.getAll().forEach((trigger)=>trigger.kill());\n        };\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ScrollAnimations.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/About.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/About.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction About() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)({\n        target: ref,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const x = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        -100,\n        100\n    ]);\n    const rotate = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        360\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        className: \"py-20 px-6 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                style: {\n                    x,\n                    rotate\n                },\n                className: \"absolute top-20 right-10 w-20 h-20 border border-gray-800 rounded-full opacity-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-6xl\",\n                ref: ref,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-16 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                x: 0\n                            } : {\n                                opacity: 0,\n                                x: -50\n                            },\n                            transition: {\n                                duration: 0.8,\n                                ease: \"easeOut\"\n                            },\n                            className: \"reveal-text\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                    className: \"text-5xl md:text-6xl font-bold text-white mb-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: isInView ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    children: \"This is me.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 text-gray-400 text-lg leading-relaxed\",\n                                    children: [\n                                        \"Hi, I'm Your Name.\",\n                                        \"I'm a full stack web developer dedicated to turning ideas into creative solutions. I specialize in creating seamless and intuitive user experiences while building robust backend systems that power them.\",\n                                        \"My approach focuses on creating scalable, high-performing solutions tailored to both user needs and business objectives. From frontend interfaces to backend APIs and databases, I strive to deliver complete solutions that not only engage users but also drive tangible results.\"\n                                    ].map((text, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: isInView ? {\n                                                opacity: 1,\n                                                y: 0\n                                            } : {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.4 + index * 0.2\n                                            },\n                                            className: index === 0 ? \"text-white font-semibold\" : \"\",\n                                            children: index === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    \"Hi, I'm \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: \"Your Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 31\n                                                    }, this),\n                                                    \".\"\n                                                ]\n                                            }, void 0, true) : text\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50,\n                                scale: 0.9\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                x: 0,\n                                scale: 1\n                            } : {\n                                opacity: 0,\n                                x: 50,\n                                scale: 0.9\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.3\n                            },\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"relative w-full h-[500px] rounded-2xl overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900\",\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        initial: {\n                                            scale: 0\n                                        },\n                                        animate: isInView ? {\n                                            scale: 1\n                                        } : {\n                                            scale: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.6,\n                                            type: \"spring\",\n                                            stiffness: 100\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"w-32 h-32 rounded-full bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center\",\n                                            whileHover: {\n                                                scale: 1.1,\n                                                rotate: 360,\n                                                transition: {\n                                                    duration: 0.6\n                                                }\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-4xl font-bold text-white\",\n                                                children: \"YN\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"absolute top-10 left-10 w-4 h-4 bg-blue-500/30 rounded-full\",\n                                        animate: {\n                                            y: [\n                                                0,\n                                                -20,\n                                                0\n                                            ],\n                                            opacity: [\n                                                0.3,\n                                                0.8,\n                                                0.3\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 3,\n                                            repeat: Infinity,\n                                            ease: \"easeInOut\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"absolute bottom-20 right-10 w-3 h-3 bg-purple-500/40 rounded-full\",\n                                        animate: {\n                                            y: [\n                                                0,\n                                                -15,\n                                                0\n                                            ],\n                                            opacity: [\n                                                0.4,\n                                                0.9,\n                                                0.4\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 4,\n                                            repeat: Infinity,\n                                            ease: \"easeInOut\",\n                                            delay: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9zZWN0aW9ucy9BYm91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUUwRTtBQUM1QztBQUVmLFNBQVNLO0lBQ3RCLE1BQU1DLE1BQU1GLDZDQUFNQSxDQUFDO0lBQ25CLE1BQU1HLFdBQVdOLHdEQUFTQSxDQUFDSyxLQUFLO1FBQUVFLE1BQU07UUFBTUMsUUFBUTtJQUFTO0lBQy9ELE1BQU0sRUFBRUMsZUFBZSxFQUFFLEdBQUdSLHdEQUFTQSxDQUFDO1FBQ3BDUyxRQUFRTDtRQUNSTSxRQUFRO1lBQUM7WUFBYTtTQUFZO0lBQ3BDO0lBRUEsTUFBTUMsSUFBSVYsMkRBQVlBLENBQUNPLGlCQUFpQjtRQUFDO1FBQUc7S0FBRSxFQUFFO1FBQUMsQ0FBQztRQUFLO0tBQUk7SUFDM0QsTUFBTUksU0FBU1gsMkRBQVlBLENBQUNPLGlCQUFpQjtRQUFDO1FBQUc7S0FBRSxFQUFFO1FBQUM7UUFBRztLQUFJO0lBRTdELHFCQUNFLDhEQUFDSztRQUFRQyxJQUFHO1FBQVFDLFdBQVU7OzBCQUU1Qiw4REFBQ2pCLGlEQUFNQSxDQUFDa0IsR0FBRztnQkFDVEMsT0FBTztvQkFBRU47b0JBQUdDO2dCQUFPO2dCQUNuQkcsV0FBVTs7Ozs7OzBCQUdaLDhEQUFDQztnQkFBSUQsV0FBVTtnQkFBOEJYLEtBQUtBOzBCQUNoRCw0RUFBQ1k7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDakIsaURBQU1BLENBQUNrQixHQUFHOzRCQUNURSxTQUFTO2dDQUFFQyxTQUFTO2dDQUFHUixHQUFHLENBQUM7NEJBQUc7NEJBQzlCUyxTQUFTZixXQUFXO2dDQUFFYyxTQUFTO2dDQUFHUixHQUFHOzRCQUFFLElBQUk7Z0NBQUVRLFNBQVM7Z0NBQUdSLEdBQUcsQ0FBQzs0QkFBRzs0QkFDaEVVLFlBQVk7Z0NBQUVDLFVBQVU7Z0NBQUtDLE1BQU07NEJBQVU7NEJBQzdDUixXQUFVOzs4Q0FFViw4REFBQ2pCLGlEQUFNQSxDQUFDMEIsRUFBRTtvQ0FDUlQsV0FBVTtvQ0FDVkcsU0FBUzt3Q0FBRUMsU0FBUzt3Q0FBR00sR0FBRztvQ0FBRztvQ0FDN0JMLFNBQVNmLFdBQVc7d0NBQUVjLFNBQVM7d0NBQUdNLEdBQUc7b0NBQUUsSUFBSTt3Q0FBRU4sU0FBUzt3Q0FBR00sR0FBRztvQ0FBRztvQ0FDL0RKLFlBQVk7d0NBQUVDLFVBQVU7d0NBQUtJLE9BQU87b0NBQUk7OENBQ3pDOzs7Ozs7OENBSUQsOERBQUNWO29DQUFJRCxXQUFVOzhDQUNaO3dDQUNDO3dDQUNBO3dDQUNBO3FDQUNELENBQUNZLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDWCw4REFBQy9CLGlEQUFNQSxDQUFDZ0MsQ0FBQzs0Q0FFUFosU0FBUztnREFBRUMsU0FBUztnREFBR00sR0FBRzs0Q0FBRzs0Q0FDN0JMLFNBQVNmLFdBQVc7Z0RBQUVjLFNBQVM7Z0RBQUdNLEdBQUc7NENBQUUsSUFBSTtnREFBRU4sU0FBUztnREFBR00sR0FBRzs0Q0FBRzs0Q0FDL0RKLFlBQVk7Z0RBQUVDLFVBQVU7Z0RBQUtJLE9BQU8sTUFBTUcsUUFBUTs0Q0FBSTs0Q0FDdERkLFdBQVdjLFVBQVUsSUFBSSw2QkFBNkI7c0RBRXJEQSxVQUFVLGtCQUNUOztvREFBRTtrRUFDUSw4REFBQ0U7d0RBQUtoQixXQUFVO2tFQUEyQjs7Ozs7O29EQUFnQjs7K0RBR3JFYTsyQ0FYR0M7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBbUJiLDhEQUFDL0IsaURBQU1BLENBQUNrQixHQUFHOzRCQUNURSxTQUFTO2dDQUFFQyxTQUFTO2dDQUFHUixHQUFHO2dDQUFJcUIsT0FBTzs0QkFBSTs0QkFDekNaLFNBQVNmLFdBQVc7Z0NBQUVjLFNBQVM7Z0NBQUdSLEdBQUc7Z0NBQUdxQixPQUFPOzRCQUFFLElBQUk7Z0NBQUViLFNBQVM7Z0NBQUdSLEdBQUc7Z0NBQUlxQixPQUFPOzRCQUFJOzRCQUNyRlgsWUFBWTtnQ0FBRUMsVUFBVTtnQ0FBS0ksT0FBTzs0QkFBSTs0QkFDeENYLFdBQVU7c0NBRVYsNEVBQUNqQixpREFBTUEsQ0FBQ2tCLEdBQUc7Z0NBQ1RELFdBQVU7Z0NBQ1ZrQixZQUFZO29DQUFFRCxPQUFPO2dDQUFLO2dDQUMxQlgsWUFBWTtvQ0FBRUMsVUFBVTtnQ0FBSTs7a0RBRzVCLDhEQUFDeEIsaURBQU1BLENBQUNrQixHQUFHO3dDQUNURCxXQUFVO3dDQUNWRyxTQUFTOzRDQUFFYyxPQUFPO3dDQUFFO3dDQUNwQlosU0FBU2YsV0FBVzs0Q0FBRTJCLE9BQU87d0NBQUUsSUFBSTs0Q0FBRUEsT0FBTzt3Q0FBRTt3Q0FDOUNYLFlBQVk7NENBQUVDLFVBQVU7NENBQUtJLE9BQU87NENBQUtRLE1BQU07NENBQVVDLFdBQVc7d0NBQUk7a0RBRXhFLDRFQUFDckMsaURBQU1BLENBQUNrQixHQUFHOzRDQUNURCxXQUFVOzRDQUNWa0IsWUFBWTtnREFDVkQsT0FBTztnREFDUHBCLFFBQVE7Z0RBQ1JTLFlBQVk7b0RBQUVDLFVBQVU7Z0RBQUk7NENBQzlCO3NEQUVBLDRFQUFDUztnREFBS2hCLFdBQVU7MERBQWdDOzs7Ozs7Ozs7Ozs7Ozs7O2tEQUtwRCw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7OztrREFHZiw4REFBQ2pCLGlEQUFNQSxDQUFDa0IsR0FBRzt3Q0FDVEQsV0FBVTt3Q0FDVkssU0FBUzs0Q0FDUEssR0FBRztnREFBQztnREFBRyxDQUFDO2dEQUFJOzZDQUFFOzRDQUNkTixTQUFTO2dEQUFDO2dEQUFLO2dEQUFLOzZDQUFJO3dDQUMxQjt3Q0FDQUUsWUFBWTs0Q0FDVkMsVUFBVTs0Q0FDVmMsUUFBUUM7NENBQ1JkLE1BQU07d0NBQ1I7Ozs7OztrREFFRiw4REFBQ3pCLGlEQUFNQSxDQUFDa0IsR0FBRzt3Q0FDVEQsV0FBVTt3Q0FDVkssU0FBUzs0Q0FDUEssR0FBRztnREFBQztnREFBRyxDQUFDO2dEQUFJOzZDQUFFOzRDQUNkTixTQUFTO2dEQUFDO2dEQUFLO2dEQUFLOzZDQUFJO3dDQUMxQjt3Q0FDQUUsWUFBWTs0Q0FDVkMsVUFBVTs0Q0FDVmMsUUFBUUM7NENBQ1JkLE1BQU07NENBQ05HLE9BQU87d0NBQ1Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3J0Zm9saW8vLi9zcmMvY29tcG9uZW50cy9zZWN0aW9ucy9BYm91dC50c3g/N2M0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgbW90aW9uLCB1c2VJblZpZXcsIHVzZVNjcm9sbCwgdXNlVHJhbnNmb3JtIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IHVzZVJlZiB9IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBYm91dCgpIHtcbiAgY29uc3QgcmVmID0gdXNlUmVmKG51bGwpXG4gIGNvbnN0IGlzSW5WaWV3ID0gdXNlSW5WaWV3KHJlZiwgeyBvbmNlOiB0cnVlLCBtYXJnaW46IFwiLTEwMHB4XCIgfSlcbiAgY29uc3QgeyBzY3JvbGxZUHJvZ3Jlc3MgfSA9IHVzZVNjcm9sbCh7XG4gICAgdGFyZ2V0OiByZWYsXG4gICAgb2Zmc2V0OiBbXCJzdGFydCBlbmRcIiwgXCJlbmQgc3RhcnRcIl1cbiAgfSlcblxuICBjb25zdCB4ID0gdXNlVHJhbnNmb3JtKHNjcm9sbFlQcm9ncmVzcywgWzAsIDFdLCBbLTEwMCwgMTAwXSlcbiAgY29uc3Qgcm90YXRlID0gdXNlVHJhbnNmb3JtKHNjcm9sbFlQcm9ncmVzcywgWzAsIDFdLCBbMCwgMzYwXSlcblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGlkPVwiYWJvdXRcIiBjbGFzc05hbWU9XCJweS0yMCBweC02IHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgey8qIEJhY2tncm91bmQgZGVjb3JhdGlvbiAqL31cbiAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgIHN0eWxlPXt7IHgsIHJvdGF0ZSB9fVxuICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMjAgcmlnaHQtMTAgdy0yMCBoLTIwIGJvcmRlciBib3JkZXItZ3JheS04MDAgcm91bmRlZC1mdWxsIG9wYWNpdHktMjBcIlxuICAgICAgLz5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBtYXgtdy02eGxcIiByZWY9e3JlZn0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtMiBnYXAtMTYgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgey8qIFRleHQgQ29udGVudCAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtNTAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e2lzSW5WaWV3ID8geyBvcGFjaXR5OiAxLCB4OiAwIH0gOiB7IG9wYWNpdHk6IDAsIHg6IC01MCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44LCBlYXNlOiBcImVhc2VPdXRcIiB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicmV2ZWFsLXRleHRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxtb3Rpb24uaDJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC02eGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItOFwiXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17aXNJblZpZXcgPyB7IG9wYWNpdHk6IDEsIHk6IDAgfSA6IHsgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44LCBkZWxheTogMC4yIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFRoaXMgaXMgbWUuXG4gICAgICAgICAgICA8L21vdGlvbi5oMj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTYgdGV4dC1ncmF5LTQwMCB0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICB7W1xuICAgICAgICAgICAgICAgIFwiSGksIEknbSBZb3VyIE5hbWUuXCIsXG4gICAgICAgICAgICAgICAgXCJJJ20gYSBmdWxsIHN0YWNrIHdlYiBkZXZlbG9wZXIgZGVkaWNhdGVkIHRvIHR1cm5pbmcgaWRlYXMgaW50byBjcmVhdGl2ZSBzb2x1dGlvbnMuIEkgc3BlY2lhbGl6ZSBpbiBjcmVhdGluZyBzZWFtbGVzcyBhbmQgaW50dWl0aXZlIHVzZXIgZXhwZXJpZW5jZXMgd2hpbGUgYnVpbGRpbmcgcm9idXN0IGJhY2tlbmQgc3lzdGVtcyB0aGF0IHBvd2VyIHRoZW0uXCIsXG4gICAgICAgICAgICAgICAgXCJNeSBhcHByb2FjaCBmb2N1c2VzIG9uIGNyZWF0aW5nIHNjYWxhYmxlLCBoaWdoLXBlcmZvcm1pbmcgc29sdXRpb25zIHRhaWxvcmVkIHRvIGJvdGggdXNlciBuZWVkcyBhbmQgYnVzaW5lc3Mgb2JqZWN0aXZlcy4gRnJvbSBmcm9udGVuZCBpbnRlcmZhY2VzIHRvIGJhY2tlbmQgQVBJcyBhbmQgZGF0YWJhc2VzLCBJIHN0cml2ZSB0byBkZWxpdmVyIGNvbXBsZXRlIHNvbHV0aW9ucyB0aGF0IG5vdCBvbmx5IGVuZ2FnZSB1c2VycyBidXQgYWxzbyBkcml2ZSB0YW5naWJsZSByZXN1bHRzLlwiXG4gICAgICAgICAgICAgIF0ubWFwKCh0ZXh0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxtb3Rpb24ucFxuICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e2lzSW5WaWV3ID8geyBvcGFjaXR5OiAxLCB5OiAwIH0gOiB7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjQgKyBpbmRleCAqIDAuMiB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtpbmRleCA9PT0gMCA/IFwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCIgOiBcIlwifVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtpbmRleCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICBIaSwgSSdtIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiPllvdXIgTmFtZTwvc3Bhbj4uXG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgdGV4dFxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L21vdGlvbi5wPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgIHsvKiBJbWFnZSAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiA1MCwgc2NhbGU6IDAuOSB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17aXNJblZpZXcgPyB7IG9wYWNpdHk6IDEsIHg6IDAsIHNjYWxlOiAxIH0gOiB7IG9wYWNpdHk6IDAsIHg6IDUwLCBzY2FsZTogMC45IH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGRlbGF5OiAwLjMgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGwgaC1bNTAwcHhdIHJvdW5kZWQtMnhsIG92ZXJmbG93LWhpZGRlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyYXktODAwIHRvLWdyYXktOTAwXCJcbiAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIFBsYWNlaG9sZGVyIGZvciBwcm9maWxlIGltYWdlICovfVxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgc2NhbGU6IDAgfX1cbiAgICAgICAgICAgICAgICBhbmltYXRlPXtpc0luVmlldyA/IHsgc2NhbGU6IDEgfSA6IHsgc2NhbGU6IDAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGRlbGF5OiAwLjYsIHR5cGU6IFwic3ByaW5nXCIsIHN0aWZmbmVzczogMTAwIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zMiBoLTMyIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS00MDAgdG8tcHVycGxlLTYwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7XG4gICAgICAgICAgICAgICAgICAgIHNjYWxlOiAxLjEsXG4gICAgICAgICAgICAgICAgICAgIHJvdGF0ZTogMzYwLFxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiB7IGR1cmF0aW9uOiAwLjYgfVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPllOPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBPdmVybGF5IGdyYWRpZW50ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tdCBmcm9tLWJsYWNrLzUwIHRvLXRyYW5zcGFyZW50XCIgLz5cblxuICAgICAgICAgICAgICB7LyogRmxvYXRpbmcgZWxlbWVudHMgKi99XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEwIGxlZnQtMTAgdy00IGgtNCBiZy1ibHVlLTUwMC8zMCByb3VuZGVkLWZ1bGxcIlxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgICAgICAgIHk6IFswLCAtMjAsIDBdLFxuICAgICAgICAgICAgICAgICAgb3BhY2l0eTogWzAuMywgMC44LCAwLjNdXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMyxcbiAgICAgICAgICAgICAgICAgIHJlcGVhdDogSW5maW5pdHksXG4gICAgICAgICAgICAgICAgICBlYXNlOiBcImVhc2VJbk91dFwiXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMjAgcmlnaHQtMTAgdy0zIGgtMyBiZy1wdXJwbGUtNTAwLzQwIHJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17e1xuICAgICAgICAgICAgICAgICAgeTogWzAsIC0xNSwgMF0sXG4gICAgICAgICAgICAgICAgICBvcGFjaXR5OiBbMC40LCAwLjksIDAuNF1cbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3tcbiAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiA0LFxuICAgICAgICAgICAgICAgICAgcmVwZWF0OiBJbmZpbml0eSxcbiAgICAgICAgICAgICAgICAgIGVhc2U6IFwiZWFzZUluT3V0XCIsXG4gICAgICAgICAgICAgICAgICBkZWxheTogMVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1vdGlvbiIsInVzZUluVmlldyIsInVzZVNjcm9sbCIsInVzZVRyYW5zZm9ybSIsInVzZVJlZiIsIkFib3V0IiwicmVmIiwiaXNJblZpZXciLCJvbmNlIiwibWFyZ2luIiwic2Nyb2xsWVByb2dyZXNzIiwidGFyZ2V0Iiwib2Zmc2V0IiwieCIsInJvdGF0ZSIsInNlY3Rpb24iLCJpZCIsImNsYXNzTmFtZSIsImRpdiIsInN0eWxlIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJhbmltYXRlIiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiZWFzZSIsImgyIiwieSIsImRlbGF5IiwibWFwIiwidGV4dCIsImluZGV4IiwicCIsInNwYW4iLCJzY2FsZSIsIndoaWxlSG92ZXIiLCJ0eXBlIiwic3RpZmZuZXNzIiwicmVwZWF0IiwiSW5maW5pdHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/About.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Contact.tsx":
/*!*********************************************!*\
  !*** ./src/components/sections/Contact.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Contact() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"py-20 px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-8\",\n                            children: \"Have a project in mind?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"mailto:<EMAIL>\",\n                                className: \"inline-flex items-center text-2xl md:text-3xl text-white hover:text-blue-400 transition-colors duration-300 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"mr-4 group-hover:scale-110 transition-transform duration-300\",\n                                        size: 32\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"<EMAIL>\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"flex justify-center space-x-8 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://github.com\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-4 bg-gray-800 hover:bg-gray-700 rounded-full transition-colors duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 24,\n                                        className: \"text-white group-hover:scale-110 transition-transform duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://linkedin.com\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-4 bg-gray-800 hover:bg-gray-700 rounded-full transition-colors duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        className: \"text-white group-hover:scale-110 transition-transform duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center border-t border-gray-800 pt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2 text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://github.com/Tajmirul/portfolio-2.0\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"hover:text-white transition-colors duration-300 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Design & built by Your Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-white mb-2\",\n                                    children: \"YOUR NAME\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                    children: \"<EMAIL>\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Contact.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Experience.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/Experience.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Experience)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst experiences = [\n    {\n        company: \"Strativ AB\",\n        position: \"Software Engineer (Frontend)\",\n        period: \"Dec 2024 - Present\",\n        description: \"Leading frontend development initiatives and building scalable web applications.\"\n    },\n    {\n        company: \"Epikcoders\",\n        position: \"Frontend Developer\",\n        period: \"Oct 2023 - Nov 2024\",\n        description: \"Developed responsive web applications using React and modern frontend technologies.\"\n    },\n    {\n        company: \"Anchorblock Technology\",\n        position: \"FRONTEND ENGINEER\",\n        period: \"Oct 2022 - Sep 2023\",\n        description: \"Built user interfaces and implemented frontend solutions for various client projects.\"\n    },\n    {\n        company: \"Branex IT\",\n        position: \"Frontend Developer (Part-time)\",\n        period: \"Jan 2022 - Oct 2022\",\n        description: \"Collaborated on frontend development projects while maintaining high code quality standards.\"\n    }\n];\nfunction Experience() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"experience\",\n        className: \"py-20 px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                        children: \"My Experience\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: experiences.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"relative\",\n                            children: [\n                                index !== experiences.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-6 top-16 w-px h-16 bg-gray-700\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-3 h-3 bg-blue-500 rounded-full mt-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 bg-gray-900/50 border border-gray-800 rounded-xl p-6 hover:border-gray-700 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-white mb-1\",\n                                                                    children: exp.company\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                                    lineNumber: 71,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-400 font-medium\",\n                                                                    children: exp.position\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                                    lineNumber: 74,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm mt-2 md:mt-0\",\n                                                            children: exp.period\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 leading-relaxed\",\n                                                    children: exp.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Experience.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Hero() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)({\n        target: ref,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"50%\"\n    ]);\n    const opacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        1,\n        0\n    ]);\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        element?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    // Animation variants\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            y: 50,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                type: \"spring\",\n                stiffness: 100,\n                damping: 12\n            }\n        }\n    };\n    const floatingVariants = {\n        animate: {\n            y: [\n                0,\n                -20,\n                0\n            ],\n            transition: {\n                duration: 6,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.section, {\n        ref: ref,\n        style: {\n            y,\n            opacity\n        },\n        className: \"pt-32 pb-20 px-6 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: floatingVariants,\n                        animate: \"animate\",\n                        className: \"absolute top-20 left-10 w-2 h-2 bg-blue-500/30 rounded-full parallax-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: floatingVariants,\n                        animate: \"animate\",\n                        style: {\n                            animationDelay: \"2s\"\n                        },\n                        className: \"absolute top-40 right-20 w-3 h-3 bg-purple-500/20 rounded-full parallax-fast\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: floatingVariants,\n                        animate: \"animate\",\n                        style: {\n                            animationDelay: \"4s\"\n                        },\n                        className: \"absolute bottom-40 left-1/4 w-1 h-1 bg-white/20 rounded-full parallax-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-5xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    className: \"text-center space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            className: \"space-y-2 reveal-text\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                    className: \"text-gray-400 text-lg\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: 0.5,\n                                        duration: 0.8\n                                    },\n                                    children: \"Hello, I'm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                                    className: \"text-5xl md:text-7xl font-bold text-white tracking-tight\",\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.7,\n                                        duration: 1,\n                                        type: \"spring\",\n                                        stiffness: 100\n                                    },\n                                    children: \"Your Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                className: \"text-2xl md:text-4xl font-medium text-gray-300\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 1.2,\n                                    duration: 0.8\n                                },\n                                children: \"Full Stack Developer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            className: \"max-w-2xl mx-auto reveal-text\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                className: \"text-lg text-gray-400 leading-relaxed\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 1.5,\n                                    duration: 0.8\n                                },\n                                children: \"I create beautiful, functional, and user-centered digital experiences. Passionate about clean code, modern design, and innovative solutions.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"magnetic\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-white text-black hover:bg-gray-200 px-8 py-3 rounded-lg font-medium transition-all duration-300\",\n                                        onClick: ()=>scrollToSection(\"projects\"),\n                                        children: \"View My Work\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"magnetic\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        size: \"lg\",\n                                        className: \"border-gray-600 text-gray-300 hover:bg-gray-800 hover:border-gray-500 px-8 py-3 rounded-lg font-medium transition-all duration-300\",\n                                        onClick: ()=>scrollToSection(\"contact\"),\n                                        children: \"Get In Touch\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            className: \"grid grid-cols-3 gap-8 max-w-md mx-auto pt-8\",\n                            children: [\n                                {\n                                    number: \"3+\",\n                                    label: \"Years\"\n                                },\n                                {\n                                    number: \"15+\",\n                                    label: \"Projects\"\n                                },\n                                {\n                                    number: \"50+\",\n                                    label: \"Clients\"\n                                }\n                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    className: \"text-center stagger-item\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 2 + index * 0.1,\n                                        duration: 0.6\n                                    },\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                delay: 2.2 + index * 0.1,\n                                                duration: 0.5,\n                                                type: \"spring\"\n                                            },\n                                            children: stat.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, stat.label, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            className: \"flex justify-center space-x-6 pt-8\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                    href: \"https://github.com\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                    href: \"https://linkedin.com\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                    href: \"mailto:<EMAIL>\"\n                                }\n                            ].map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.a, {\n                                    href: social.href,\n                                    target: social.icon !== _barrel_optimize_names_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"] ? \"_blank\" : undefined,\n                                    rel: social.icon !== _barrel_optimize_names_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"] ? \"noopener noreferrer\" : undefined,\n                                    className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                    whileHover: {\n                                        scale: 1.2,\n                                        rotate: 5\n                                    },\n                                    whileTap: {\n                                        scale: 0.9\n                                    },\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 2.5 + index * 0.1,\n                                        duration: 0.6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9zZWN0aW9ucy9IZXJvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRStEO0FBQ2hCO0FBQ29CO0FBQ3JDO0FBRWYsU0FBU1E7SUFDdEIsTUFBTUMsTUFBTUYsNkNBQU1BLENBQUM7SUFDbkIsTUFBTSxFQUFFRyxlQUFlLEVBQUUsR0FBR1Qsd0RBQVNBLENBQUM7UUFDcENVLFFBQVFGO1FBQ1JHLFFBQVE7WUFBQztZQUFlO1NBQVk7SUFDdEM7SUFFQSxNQUFNQyxJQUFJWCwyREFBWUEsQ0FBQ1EsaUJBQWlCO1FBQUM7UUFBRztLQUFFLEVBQUU7UUFBQztRQUFNO0tBQU07SUFDN0QsTUFBTUksVUFBVVosMkRBQVlBLENBQUNRLGlCQUFpQjtRQUFDO1FBQUc7S0FBRSxFQUFFO1FBQUM7UUFBRztLQUFFO0lBRTVELE1BQU1LLGtCQUFrQixDQUFDQztRQUN2QixNQUFNQyxVQUFVQyxTQUFTQyxjQUFjLENBQUNIO1FBQ3hDQyxTQUFTRyxlQUFlO1lBQUVDLFVBQVU7UUFBUztJQUMvQztJQUVBLHFCQUFxQjtJQUNyQixNQUFNQyxvQkFBb0I7UUFDeEJDLFFBQVE7WUFBRVQsU0FBUztRQUFFO1FBQ3JCVSxTQUFTO1lBQ1BWLFNBQVM7WUFDVFcsWUFBWTtnQkFDVkMsaUJBQWlCO2dCQUNqQkMsZUFBZTtZQUNqQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxlQUFlO1FBQ25CTCxRQUFRO1lBQUVWLEdBQUc7WUFBSUMsU0FBUztRQUFFO1FBQzVCVSxTQUFTO1lBQ1BYLEdBQUc7WUFDSEMsU0FBUztZQUNUVyxZQUFZO2dCQUNWSSxNQUFNO2dCQUNOQyxXQUFXO2dCQUNYQyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTUMsbUJBQW1CO1FBQ3ZCQyxTQUFTO1lBQ1BwQixHQUFHO2dCQUFDO2dCQUFHLENBQUM7Z0JBQUk7YUFBRTtZQUNkWSxZQUFZO2dCQUNWUyxVQUFVO2dCQUNWQyxRQUFRQztnQkFDUkMsTUFBTTtZQUNSO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDckMsaURBQU1BLENBQUNzQyxPQUFPO1FBQ2I3QixLQUFLQTtRQUNMOEIsT0FBTztZQUFFMUI7WUFBR0M7UUFBUTtRQUNwQjBCLFdBQVU7OzBCQUdWLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUN4QyxpREFBTUEsQ0FBQ3lDLEdBQUc7d0JBQ1RDLFVBQVVWO3dCQUNWQyxTQUFRO3dCQUNSTyxXQUFVOzs7Ozs7a0NBRVosOERBQUN4QyxpREFBTUEsQ0FBQ3lDLEdBQUc7d0JBQ1RDLFVBQVVWO3dCQUNWQyxTQUFRO3dCQUNSTSxPQUFPOzRCQUFFSSxnQkFBZ0I7d0JBQUs7d0JBQzlCSCxXQUFVOzs7Ozs7a0NBRVosOERBQUN4QyxpREFBTUEsQ0FBQ3lDLEdBQUc7d0JBQ1RDLFVBQVVWO3dCQUNWQyxTQUFRO3dCQUNSTSxPQUFPOzRCQUFFSSxnQkFBZ0I7d0JBQUs7d0JBQzlCSCxXQUFVOzs7Ozs7Ozs7Ozs7MEJBSWQsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDeEMsaURBQU1BLENBQUN5QyxHQUFHO29CQUNUQyxVQUFVcEI7b0JBQ1ZzQixTQUFRO29CQUNSWCxTQUFRO29CQUNSTyxXQUFVOztzQ0FHViw4REFBQ3hDLGlEQUFNQSxDQUFDeUMsR0FBRzs0QkFBQ0MsVUFBVWQ7NEJBQWNZLFdBQVU7OzhDQUM1Qyw4REFBQ3hDLGlEQUFNQSxDQUFDNkMsQ0FBQztvQ0FDUEwsV0FBVTtvQ0FDVkksU0FBUzt3Q0FBRTlCLFNBQVM7d0NBQUdnQyxHQUFHLENBQUM7b0NBQUc7b0NBQzlCYixTQUFTO3dDQUFFbkIsU0FBUzt3Q0FBR2dDLEdBQUc7b0NBQUU7b0NBQzVCckIsWUFBWTt3Q0FBRXNCLE9BQU87d0NBQUtiLFVBQVU7b0NBQUk7OENBQ3pDOzs7Ozs7OENBR0QsOERBQUNsQyxpREFBTUEsQ0FBQ2dELEVBQUU7b0NBQ1JSLFdBQVU7b0NBQ1ZJLFNBQVM7d0NBQUU5QixTQUFTO3dDQUFHbUMsT0FBTztvQ0FBSTtvQ0FDbENoQixTQUFTO3dDQUFFbkIsU0FBUzt3Q0FBR21DLE9BQU87b0NBQUU7b0NBQ2hDeEIsWUFBWTt3Q0FDVnNCLE9BQU87d0NBQ1BiLFVBQVU7d0NBQ1ZMLE1BQU07d0NBQ05DLFdBQVc7b0NBQ2I7OENBQ0Q7Ozs7Ozs7Ozs7OztzQ0FNSCw4REFBQzlCLGlEQUFNQSxDQUFDeUMsR0FBRzs0QkFBQ0MsVUFBVWQ7c0NBQ3BCLDRFQUFDNUIsaURBQU1BLENBQUNrRCxFQUFFO2dDQUNSVixXQUFVO2dDQUNWSSxTQUFTO29DQUFFOUIsU0FBUztvQ0FBR0QsR0FBRztnQ0FBRztnQ0FDN0JvQixTQUFTO29DQUFFbkIsU0FBUztvQ0FBR0QsR0FBRztnQ0FBRTtnQ0FDNUJZLFlBQVk7b0NBQUVzQixPQUFPO29DQUFLYixVQUFVO2dDQUFJOzBDQUN6Qzs7Ozs7Ozs7Ozs7c0NBTUgsOERBQUNsQyxpREFBTUEsQ0FBQ3lDLEdBQUc7NEJBQ1RDLFVBQVVkOzRCQUNWWSxXQUFVO3NDQUVWLDRFQUFDeEMsaURBQU1BLENBQUM2QyxDQUFDO2dDQUNQTCxXQUFVO2dDQUNWSSxTQUFTO29DQUFFOUIsU0FBUztvQ0FBR0QsR0FBRztnQ0FBRztnQ0FDN0JvQixTQUFTO29DQUFFbkIsU0FBUztvQ0FBR0QsR0FBRztnQ0FBRTtnQ0FDNUJZLFlBQVk7b0NBQUVzQixPQUFPO29DQUFLYixVQUFVO2dDQUFJOzBDQUN6Qzs7Ozs7Ozs7Ozs7c0NBT0gsOERBQUNsQyxpREFBTUEsQ0FBQ3lDLEdBQUc7NEJBQ1RDLFVBQVVkOzRCQUNWWSxXQUFVOzs4Q0FFViw4REFBQ3hDLGlEQUFNQSxDQUFDeUMsR0FBRztvQ0FDVFUsWUFBWTt3Q0FBRUYsT0FBTztvQ0FBSztvQ0FDMUJHLFVBQVU7d0NBQUVILE9BQU87b0NBQUs7b0NBQ3hCVCxXQUFVOzhDQUVWLDRFQUFDckMseURBQU1BO3dDQUNMa0QsTUFBSzt3Q0FDTGIsV0FBVTt3Q0FDVmMsU0FBUyxJQUFNdkMsZ0JBQWdCO2tEQUNoQzs7Ozs7Ozs7Ozs7OENBSUgsOERBQUNmLGlEQUFNQSxDQUFDeUMsR0FBRztvQ0FDVFUsWUFBWTt3Q0FBRUYsT0FBTztvQ0FBSztvQ0FDMUJHLFVBQVU7d0NBQUVILE9BQU87b0NBQUs7b0NBQ3hCVCxXQUFVOzhDQUVWLDRFQUFDckMseURBQU1BO3dDQUNMb0QsU0FBUTt3Q0FDUkYsTUFBSzt3Q0FDTGIsV0FBVTt3Q0FDVmMsU0FBUyxJQUFNdkMsZ0JBQWdCO2tEQUNoQzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT0wsOERBQUNmLGlEQUFNQSxDQUFDeUMsR0FBRzs0QkFDVEMsVUFBVWQ7NEJBQ1ZZLFdBQVU7c0NBRVQ7Z0NBQ0M7b0NBQUVnQixRQUFRO29DQUFNQyxPQUFPO2dDQUFRO2dDQUMvQjtvQ0FBRUQsUUFBUTtvQ0FBT0MsT0FBTztnQ0FBVztnQ0FDbkM7b0NBQUVELFFBQVE7b0NBQU9DLE9BQU87Z0NBQVU7NkJBQ25DLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDWCw4REFBQzVELGlEQUFNQSxDQUFDeUMsR0FBRztvQ0FFVEQsV0FBVTtvQ0FDVkksU0FBUzt3Q0FBRTlCLFNBQVM7d0NBQUdELEdBQUc7b0NBQUc7b0NBQzdCb0IsU0FBUzt3Q0FBRW5CLFNBQVM7d0NBQUdELEdBQUc7b0NBQUU7b0NBQzVCWSxZQUFZO3dDQUFFc0IsT0FBTyxJQUFJYSxRQUFRO3dDQUFLMUIsVUFBVTtvQ0FBSTtvQ0FDcERpQixZQUFZO3dDQUFFRixPQUFPO29DQUFJOztzREFFekIsOERBQUNqRCxpREFBTUEsQ0FBQ3lDLEdBQUc7NENBQ1RELFdBQVU7NENBQ1ZJLFNBQVM7Z0RBQUU5QixTQUFTO2dEQUFHbUMsT0FBTzs0Q0FBRTs0Q0FDaENoQixTQUFTO2dEQUFFbkIsU0FBUztnREFBR21DLE9BQU87NENBQUU7NENBQ2hDeEIsWUFBWTtnREFBRXNCLE9BQU8sTUFBTWEsUUFBUTtnREFBSzFCLFVBQVU7Z0RBQUtMLE1BQU07NENBQVM7c0RBRXJFOEIsS0FBS0gsTUFBTTs7Ozs7O3NEQUVkLDhEQUFDZjs0Q0FBSUQsV0FBVTtzREFBeUJtQixLQUFLRixLQUFLOzs7Ozs7O21DQWY3Q0UsS0FBS0YsS0FBSzs7Ozs7Ozs7OztzQ0FxQnJCLDhEQUFDekQsaURBQU1BLENBQUN5QyxHQUFHOzRCQUNUQyxVQUFVZDs0QkFDVlksV0FBVTtzQ0FFVDtnQ0FDQztvQ0FBRXFCLE1BQU16RCxnR0FBTUE7b0NBQUUwRCxNQUFNO2dDQUFxQjtnQ0FDM0M7b0NBQUVELE1BQU14RCxnR0FBUUE7b0NBQUV5RCxNQUFNO2dDQUF1QjtnQ0FDL0M7b0NBQUVELE1BQU12RCxnR0FBSUE7b0NBQUV3RCxNQUFNO2dDQUFnQzs2QkFDckQsQ0FBQ0osR0FBRyxDQUFDLENBQUNLLFFBQVFILHNCQUNiLDhEQUFDNUQsaURBQU1BLENBQUNnRSxDQUFDO29DQUVQRixNQUFNQyxPQUFPRCxJQUFJO29DQUNqQm5ELFFBQVFvRCxPQUFPRixJQUFJLEtBQUt2RCxnR0FBSUEsR0FBRyxXQUFXMkQ7b0NBQzFDQyxLQUFLSCxPQUFPRixJQUFJLEtBQUt2RCxnR0FBSUEsR0FBRyx3QkFBd0IyRDtvQ0FDcER6QixXQUFVO29DQUNWVyxZQUFZO3dDQUFFRixPQUFPO3dDQUFLa0IsUUFBUTtvQ0FBRTtvQ0FDcENmLFVBQVU7d0NBQUVILE9BQU87b0NBQUk7b0NBQ3ZCTCxTQUFTO3dDQUFFOUIsU0FBUzt3Q0FBR0QsR0FBRztvQ0FBRztvQ0FDN0JvQixTQUFTO3dDQUFFbkIsU0FBUzt3Q0FBR0QsR0FBRztvQ0FBRTtvQ0FDNUJZLFlBQVk7d0NBQUVzQixPQUFPLE1BQU1hLFFBQVE7d0NBQUsxQixVQUFVO29DQUFJOzhDQUV0RCw0RUFBQzZCLE9BQU9GLElBQUk7d0NBQUNSLE1BQU07Ozs7OzttQ0FYZE87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQW1CckIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3J0Zm9saW8vLi9zcmMvY29tcG9uZW50cy9zZWN0aW9ucy9IZXJvLnRzeD8wZTE0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBtb3Rpb24sIHVzZVNjcm9sbCwgdXNlVHJhbnNmb3JtIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBHaXRodWIsIExpbmtlZGluLCBNYWlsLCBFeHRlcm5hbExpbmsgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyB1c2VSZWYgfSBmcm9tICdyZWFjdCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSGVybygpIHtcbiAgY29uc3QgcmVmID0gdXNlUmVmKG51bGwpXG4gIGNvbnN0IHsgc2Nyb2xsWVByb2dyZXNzIH0gPSB1c2VTY3JvbGwoe1xuICAgIHRhcmdldDogcmVmLFxuICAgIG9mZnNldDogW1wic3RhcnQgc3RhcnRcIiwgXCJlbmQgc3RhcnRcIl1cbiAgfSlcblxuICBjb25zdCB5ID0gdXNlVHJhbnNmb3JtKHNjcm9sbFlQcm9ncmVzcywgWzAsIDFdLCBbXCIwJVwiLCBcIjUwJVwiXSlcbiAgY29uc3Qgb3BhY2l0eSA9IHVzZVRyYW5zZm9ybShzY3JvbGxZUHJvZ3Jlc3MsIFswLCAxXSwgWzEsIDBdKVxuXG4gIGNvbnN0IHNjcm9sbFRvU2VjdGlvbiA9IChzZWN0aW9uSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGVsZW1lbnQgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChzZWN0aW9uSWQpXG4gICAgZWxlbWVudD8uc2Nyb2xsSW50b1ZpZXcoeyBiZWhhdmlvcjogJ3Ntb290aCcgfSlcbiAgfVxuXG4gIC8vIEFuaW1hdGlvbiB2YXJpYW50c1xuICBjb25zdCBjb250YWluZXJWYXJpYW50cyA9IHtcbiAgICBoaWRkZW46IHsgb3BhY2l0eTogMCB9LFxuICAgIHZpc2libGU6IHtcbiAgICAgIG9wYWNpdHk6IDEsXG4gICAgICB0cmFuc2l0aW9uOiB7XG4gICAgICAgIHN0YWdnZXJDaGlsZHJlbjogMC4yLFxuICAgICAgICBkZWxheUNoaWxkcmVuOiAwLjNcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBjb25zdCBpdGVtVmFyaWFudHMgPSB7XG4gICAgaGlkZGVuOiB7IHk6IDUwLCBvcGFjaXR5OiAwIH0sXG4gICAgdmlzaWJsZToge1xuICAgICAgeTogMCxcbiAgICAgIG9wYWNpdHk6IDEsXG4gICAgICB0cmFuc2l0aW9uOiB7XG4gICAgICAgIHR5cGU6IFwic3ByaW5nXCIsXG4gICAgICAgIHN0aWZmbmVzczogMTAwLFxuICAgICAgICBkYW1waW5nOiAxMlxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGZsb2F0aW5nVmFyaWFudHMgPSB7XG4gICAgYW5pbWF0ZToge1xuICAgICAgeTogWzAsIC0yMCwgMF0sXG4gICAgICB0cmFuc2l0aW9uOiB7XG4gICAgICAgIGR1cmF0aW9uOiA2LFxuICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICBlYXNlOiBcImVhc2VJbk91dFwiXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8bW90aW9uLnNlY3Rpb25cbiAgICAgIHJlZj17cmVmfVxuICAgICAgc3R5bGU9e3sgeSwgb3BhY2l0eSB9fVxuICAgICAgY2xhc3NOYW1lPVwicHQtMzIgcGItMjAgcHgtNiByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIlxuICAgID5cbiAgICAgIHsvKiBBbmltYXRlZCBiYWNrZ3JvdW5kIGVsZW1lbnRzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICB2YXJpYW50cz17ZmxvYXRpbmdWYXJpYW50c31cbiAgICAgICAgICBhbmltYXRlPVwiYW5pbWF0ZVwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIwIGxlZnQtMTAgdy0yIGgtMiBiZy1ibHVlLTUwMC8zMCByb3VuZGVkLWZ1bGwgcGFyYWxsYXgtc2xvd1wiXG4gICAgICAgIC8+XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgdmFyaWFudHM9e2Zsb2F0aW5nVmFyaWFudHN9XG4gICAgICAgICAgYW5pbWF0ZT1cImFuaW1hdGVcIlxuICAgICAgICAgIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiBcIjJzXCIgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNDAgcmlnaHQtMjAgdy0zIGgtMyBiZy1wdXJwbGUtNTAwLzIwIHJvdW5kZWQtZnVsbCBwYXJhbGxheC1mYXN0XCJcbiAgICAgICAgLz5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICB2YXJpYW50cz17ZmxvYXRpbmdWYXJpYW50c31cbiAgICAgICAgICBhbmltYXRlPVwiYW5pbWF0ZVwiXG4gICAgICAgICAgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6IFwiNHNcIiB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS00MCBsZWZ0LTEvNCB3LTEgaC0xIGJnLXdoaXRlLzIwIHJvdW5kZWQtZnVsbCBwYXJhbGxheC1zbG93XCJcbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIG1heC13LTV4bFwiPlxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIHZhcmlhbnRzPXtjb250YWluZXJWYXJpYW50c31cbiAgICAgICAgICBpbml0aWFsPVwiaGlkZGVuXCJcbiAgICAgICAgICBhbmltYXRlPVwidmlzaWJsZVwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS04XCJcbiAgICAgICAgPlxuICAgICAgICAgIHsvKiBHcmVldGluZyAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdiB2YXJpYW50cz17aXRlbVZhcmlhbnRzfSBjbGFzc05hbWU9XCJzcGFjZS15LTIgcmV2ZWFsLXRleHRcIj5cbiAgICAgICAgICAgIDxtb3Rpb24ucFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtbGdcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC41LCBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEhlbGxvLCBJJ21cbiAgICAgICAgICAgIDwvbW90aW9uLnA+XG4gICAgICAgICAgICA8bW90aW9uLmgxXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtNXhsIG1kOnRleHQtN3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIHRyYWNraW5nLXRpZ2h0XCJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC44IH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17e1xuICAgICAgICAgICAgICAgIGRlbGF5OiAwLjcsXG4gICAgICAgICAgICAgICAgZHVyYXRpb246IDEsXG4gICAgICAgICAgICAgICAgdHlwZTogXCJzcHJpbmdcIixcbiAgICAgICAgICAgICAgICBzdGlmZm5lc3M6IDEwMFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBZb3VyIE5hbWVcbiAgICAgICAgICAgIDwvbW90aW9uLmgxPlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgIHsvKiBUaXRsZSB3aXRoIHR5cGV3cml0ZXIgZWZmZWN0ICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2IHZhcmlhbnRzPXtpdGVtVmFyaWFudHN9PlxuICAgICAgICAgICAgPG1vdGlvbi5oMlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBtZDp0ZXh0LTR4bCBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwXCJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMS4yLCBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEZ1bGwgU3RhY2sgRGV2ZWxvcGVyXG4gICAgICAgICAgICA8L21vdGlvbi5oMj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogRGVzY3JpcHRpb24gKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIHZhcmlhbnRzPXtpdGVtVmFyaWFudHN9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy0yeGwgbXgtYXV0byByZXZlYWwtdGV4dFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPG1vdGlvbi5wXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTQwMCBsZWFkaW5nLXJlbGF4ZWRcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAxLjUsIGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgSSBjcmVhdGUgYmVhdXRpZnVsLCBmdW5jdGlvbmFsLCBhbmQgdXNlci1jZW50ZXJlZCBkaWdpdGFsIGV4cGVyaWVuY2VzLlxuICAgICAgICAgICAgICBQYXNzaW9uYXRlIGFib3V0IGNsZWFuIGNvZGUsIG1vZGVybiBkZXNpZ24sIGFuZCBpbm5vdmF0aXZlIHNvbHV0aW9ucy5cbiAgICAgICAgICAgIDwvbW90aW9uLnA+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgey8qIENUQSBCdXR0b25zIHdpdGggbWFnbmV0aWMgZWZmZWN0ICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICB2YXJpYW50cz17aXRlbVZhcmlhbnRzfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNCBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wNSB9fVxuICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYWduZXRpY1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlIHRleHQtYmxhY2sgaG92ZXI6YmctZ3JheS0yMDAgcHgtOCBweS0zIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzY3JvbGxUb1NlY3Rpb24oJ3Byb2plY3RzJyl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBWaWV3IE15IFdvcmtcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1hZ25ldGljXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktODAwIGhvdmVyOmJvcmRlci1ncmF5LTUwMCBweC04IHB5LTMgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNjcm9sbFRvU2VjdGlvbignY29udGFjdCcpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgR2V0IEluIFRvdWNoXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgIHsvKiBBbmltYXRlZCBTdGF0cyAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgdmFyaWFudHM9e2l0ZW1WYXJpYW50c31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTggbWF4LXctbWQgbXgtYXV0byBwdC04XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7W1xuICAgICAgICAgICAgICB7IG51bWJlcjogXCIzK1wiLCBsYWJlbDogXCJZZWFyc1wiIH0sXG4gICAgICAgICAgICAgIHsgbnVtYmVyOiBcIjE1K1wiLCBsYWJlbDogXCJQcm9qZWN0c1wiIH0sXG4gICAgICAgICAgICAgIHsgbnVtYmVyOiBcIjUwK1wiLCBsYWJlbDogXCJDbGllbnRzXCIgfVxuICAgICAgICAgICAgXS5tYXAoKHN0YXQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAga2V5PXtzdGF0LmxhYmVsfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHN0YWdnZXItaXRlbVwiXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDIgKyBpbmRleCAqIDAuMSwgZHVyYXRpb246IDAuNiB9fVxuICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMSB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtMnhsIG1kOnRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAgfX1cbiAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDIuMiArIGluZGV4ICogMC4xLCBkdXJhdGlvbjogMC41LCB0eXBlOiBcInNwcmluZ1wiIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge3N0YXQubnVtYmVyfVxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPntzdGF0LmxhYmVsfTwvZGl2PlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogU29jaWFsIExpbmtzIHdpdGggaG92ZXIgYW5pbWF0aW9ucyAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgdmFyaWFudHM9e2l0ZW1WYXJpYW50c31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgc3BhY2UteC02IHB0LThcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgIHsgaWNvbjogR2l0aHViLCBocmVmOiBcImh0dHBzOi8vZ2l0aHViLmNvbVwiIH0sXG4gICAgICAgICAgICAgIHsgaWNvbjogTGlua2VkaW4sIGhyZWY6IFwiaHR0cHM6Ly9saW5rZWRpbi5jb21cIiB9LFxuICAgICAgICAgICAgICB7IGljb246IE1haWwsIGhyZWY6IFwibWFpbHRvOnlvdXIuZW1haWxAZXhhbXBsZS5jb21cIiB9XG4gICAgICAgICAgICBdLm1hcCgoc29jaWFsLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8bW90aW9uLmFcbiAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgIGhyZWY9e3NvY2lhbC5ocmVmfVxuICAgICAgICAgICAgICAgIHRhcmdldD17c29jaWFsLmljb24gIT09IE1haWwgPyBcIl9ibGFua1wiIDogdW5kZWZpbmVkfVxuICAgICAgICAgICAgICAgIHJlbD17c29jaWFsLmljb24gIT09IE1haWwgPyBcIm5vb3BlbmVyIG5vcmVmZXJyZXJcIiA6IHVuZGVmaW5lZH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjIsIHJvdGF0ZTogNSB9fVxuICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjkgfX1cbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMi41ICsgaW5kZXggKiAwLjEsIGR1cmF0aW9uOiAwLjYgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzb2NpYWwuaWNvbiBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uYT5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9tb3Rpb24uc2VjdGlvbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1vdGlvbiIsInVzZVNjcm9sbCIsInVzZVRyYW5zZm9ybSIsIkJ1dHRvbiIsIkdpdGh1YiIsIkxpbmtlZGluIiwiTWFpbCIsInVzZVJlZiIsIkhlcm8iLCJyZWYiLCJzY3JvbGxZUHJvZ3Jlc3MiLCJ0YXJnZXQiLCJvZmZzZXQiLCJ5Iiwib3BhY2l0eSIsInNjcm9sbFRvU2VjdGlvbiIsInNlY3Rpb25JZCIsImVsZW1lbnQiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsImNvbnRhaW5lclZhcmlhbnRzIiwiaGlkZGVuIiwidmlzaWJsZSIsInRyYW5zaXRpb24iLCJzdGFnZ2VyQ2hpbGRyZW4iLCJkZWxheUNoaWxkcmVuIiwiaXRlbVZhcmlhbnRzIiwidHlwZSIsInN0aWZmbmVzcyIsImRhbXBpbmciLCJmbG9hdGluZ1ZhcmlhbnRzIiwiYW5pbWF0ZSIsImR1cmF0aW9uIiwicmVwZWF0IiwiSW5maW5pdHkiLCJlYXNlIiwic2VjdGlvbiIsInN0eWxlIiwiY2xhc3NOYW1lIiwiZGl2IiwidmFyaWFudHMiLCJhbmltYXRpb25EZWxheSIsImluaXRpYWwiLCJwIiwieCIsImRlbGF5IiwiaDEiLCJzY2FsZSIsImgyIiwid2hpbGVIb3ZlciIsIndoaWxlVGFwIiwic2l6ZSIsIm9uQ2xpY2siLCJ2YXJpYW50IiwibnVtYmVyIiwibGFiZWwiLCJtYXAiLCJzdGF0IiwiaW5kZXgiLCJpY29uIiwiaHJlZiIsInNvY2lhbCIsImEiLCJ1bmRlZmluZWQiLCJyZWwiLCJyb3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Projects.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Projects.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Projects)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst projects = [\n    {\n        id: \"_01.\",\n        title: \"MTI Electronics\",\n        description: \"E-commerce platform built with Next.js and Payload CMS\",\n        technologies: [\n            \"Next.js\",\n            \"Payload CMS\",\n            \"Tailwind CSS\"\n        ],\n        image: \"/api/placeholder/600/400\",\n        url: \"/projects/mti-electronics\"\n    },\n    {\n        id: \"_02.\",\n        title: \"Epikcart\",\n        description: \"Multi-language shopping cart application with Redux\",\n        technologies: [\n            \"React\",\n            \"Redux\",\n            \"React i18n\"\n        ],\n        image: \"/api/placeholder/600/400\",\n        url: \"/projects/epikcart\"\n    },\n    {\n        id: \"_03.\",\n        title: \"Resume Roaster\",\n        description: \"AI-powered resume analysis tool using GPT-4\",\n        technologies: [\n            \"GPT-4\",\n            \"Next.js\",\n            \"PostgreSQL\"\n        ],\n        image: \"/api/placeholder/600/400\",\n        url: \"/projects/resume-roaster\"\n    },\n    {\n        id: \"_04.\",\n        title: \"Real Estate\",\n        description: \"Property management platform with advanced search\",\n        technologies: [\n            \"React.js\",\n            \"Redux\",\n            \"Tailwind CSS\"\n        ],\n        image: \"/api/placeholder/600/400\",\n        url: \"/projects/property-pro\"\n    },\n    {\n        id: \"_05.\",\n        title: \"Consulting Finance\",\n        description: \"Financial consulting website with modern design\",\n        technologies: [\n            \"HTML\",\n            \"CSS & SCSS\",\n            \"Javascript\"\n        ],\n        image: \"/api/placeholder/600/400\",\n        url: \"/projects/crenotive\"\n    },\n    {\n        id: \"_06.\",\n        title: \"devLinks\",\n        description: \"Developer link sharing platform with drag & drop\",\n        technologies: [\n            \"Next.js\",\n            \"Formik\",\n            \"Drag & Drop\"\n        ],\n        image: \"/api/placeholder/600/400\",\n        url: \"/projects/devLinks\"\n    }\n];\nfunction Projects() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)({\n        target: ref,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        100,\n        -100\n    ]);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.15,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const projectVariants = {\n        hidden: {\n            opacity: 0,\n            y: 60,\n            scale: 0.9\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            scale: 1,\n            transition: {\n                type: \"spring\",\n                stiffness: 100,\n                damping: 15,\n                duration: 0.8\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        className: \"py-20 px-6 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                style: {\n                    y\n                },\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 right-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-7xl\",\n                ref: ref,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: isInView ? {\n                            opacity: 1,\n                            y: 0\n                        } : {\n                            opacity: 0,\n                            y: 50\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16 reveal-text\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                scale: 1\n                            } : {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            transition: {\n                                duration: 1,\n                                delay: 0.2\n                            },\n                            children: \"SELECTED PROJECTS\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: projectVariants,\n                                className: \"group cursor-pointer project-card stagger-item\",\n                                onClick: ()=>window.open(project.url, \"_blank\"),\n                                whileHover: {\n                                    y: -10,\n                                    transition: {\n                                        duration: 0.3\n                                    }\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    className: \"bg-gray-900/50 border border-gray-800 rounded-xl overflow-hidden hover:border-gray-700 transition-all duration-500 hover:bg-gray-800/50 h-full\",\n                                    whileHover: {\n                                        boxShadow: \"0 20px 40px rgba(0,0,0,0.4)\",\n                                        borderColor: \"rgba(156, 163, 175, 0.3)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden h-48\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    className: \"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center\",\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    transition: {\n                                                        duration: 0.4\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-lg\",\n                                                        children: \"Project Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    className: \"absolute inset-0 bg-black/60 flex items-center justify-center\",\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    whileHover: {\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                                className: \"p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors backdrop-blur-sm\",\n                                                                whileHover: {\n                                                                    scale: 1.1,\n                                                                    rotate: 5\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.9\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    size: 20,\n                                                                    className: \"text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                                className: \"p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors backdrop-blur-sm\",\n                                                                whileHover: {\n                                                                    scale: 1.1,\n                                                                    rotate: -5\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.9\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    size: 20,\n                                                                    className: \"text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    className: \"text-gray-400 text-sm mb-2\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.5 + index * 0.1\n                                                    },\n                                                    children: project.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h3, {\n                                                    className: \"text-xl font-semibold text-white mb-3\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.6 + index * 0.1\n                                                    },\n                                                    children: project.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                                    className: \"text-gray-400 mb-4 text-sm leading-relaxed\",\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1\n                                                    } : {\n                                                        opacity: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.7 + index * 0.1\n                                                    },\n                                                    children: project.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.8 + index * 0.1\n                                                    },\n                                                    children: project.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                            className: \"px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs\",\n                                                            initial: {\n                                                                opacity: 0,\n                                                                scale: 0.8\n                                                            },\n                                                            animate: isInView ? {\n                                                                opacity: 1,\n                                                                scale: 1\n                                                            } : {\n                                                                opacity: 0,\n                                                                scale: 0.8\n                                                            },\n                                                            transition: {\n                                                                delay: 0.9 + index * 0.1 + techIndex * 0.05\n                                                            },\n                                                            whileHover: {\n                                                                scale: 1.05,\n                                                                backgroundColor: \"rgba(75, 85, 99, 0.8)\"\n                                                            },\n                                                            children: tech\n                                                        }, tech, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, project.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Projects.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Skills.tsx":
/*!********************************************!*\
  !*** ./src/components/sections/Skills.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Skills)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst skillCategories = [\n    {\n        title: \"frontend\",\n        skills: [\n            {\n                name: \"Javascript\",\n                icon: \"\\uD83D\\uDFE8\"\n            },\n            {\n                name: \"Typescript\",\n                icon: \"\\uD83D\\uDD37\"\n            },\n            {\n                name: \"React\",\n                icon: \"⚛️\"\n            },\n            {\n                name: \"Next.js\",\n                icon: \"▲\"\n            },\n            {\n                name: \"Redux\",\n                icon: \"\\uD83D\\uDFE3\"\n            },\n            {\n                name: \"Tailwind CSS\",\n                icon: \"\\uD83C\\uDFA8\"\n            },\n            {\n                name: \"GSAP\",\n                icon: \"\\uD83D\\uDFE2\"\n            },\n            {\n                name: \"Framer Motion\",\n                icon: \"\\uD83C\\uDFAD\"\n            },\n            {\n                name: \"SASS\",\n                icon: \"\\uD83C\\uDF38\"\n            },\n            {\n                name: \"Bootstrap\",\n                icon: \"\\uD83C\\uDD71️\"\n            }\n        ]\n    },\n    {\n        title: \"backend\",\n        skills: [\n            {\n                name: \"Node.js\",\n                icon: \"\\uD83D\\uDFE2\"\n            },\n            {\n                name: \"Nest.js\",\n                icon: \"\\uD83D\\uDD34\"\n            },\n            {\n                name: \"Express.js\",\n                icon: \"⚡\"\n            }\n        ]\n    },\n    {\n        title: \"database\",\n        skills: [\n            {\n                name: \"MySQL\",\n                icon: \"\\uD83D\\uDC2C\"\n            },\n            {\n                name: \"PostgreSQL\",\n                icon: \"\\uD83D\\uDC18\"\n            },\n            {\n                name: \"MongoDB\",\n                icon: \"\\uD83C\\uDF43\"\n            },\n            {\n                name: \"Prisma\",\n                icon: \"\\uD83D\\uDD3A\"\n            }\n        ]\n    },\n    {\n        title: \"tools\",\n        skills: [\n            {\n                name: \"Git\",\n                icon: \"\\uD83D\\uDCDA\"\n            },\n            {\n                name: \"Docker\",\n                icon: \"\\uD83D\\uDC33\"\n            },\n            {\n                name: \"AWS\",\n                icon: \"☁️\"\n            }\n        ]\n    }\n];\nfunction Skills() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.2\n            }\n        }\n    };\n    const categoryVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.8,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const skillVariants = {\n        hidden: {\n            opacity: 0,\n            scale: 0.8,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            scale: 1,\n            y: 0,\n            transition: {\n                type: \"spring\",\n                stiffness: 100,\n                damping: 12\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        className: \"py-20 px-6 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 right-10 w-32 h-32 bg-blue-500/5 rounded-full blur-xl parallax-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-10 w-48 h-48 bg-purple-500/5 rounded-full blur-xl parallax-fast\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-6xl\",\n                ref: ref,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: isInView ? {\n                            opacity: 1,\n                            y: 0\n                        } : {\n                            opacity: 0,\n                            y: 50\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16 reveal-text\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                scale: 1\n                            } : {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            transition: {\n                                duration: 1,\n                                delay: 0.2\n                            },\n                            children: \"My Stack\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        className: \"space-y-12\",\n                        children: skillCategories.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                variants: categoryVariants,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h3, {\n                                        className: \"text-xl font-semibold text-white lowercase reveal-text\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -30\n                                        },\n                                        animate: isInView ? {\n                                            opacity: 1,\n                                            x: 0\n                                        } : {\n                                            opacity: 0,\n                                            x: -30\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: categoryIndex * 0.1\n                                        },\n                                        children: category.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\",\n                                        children: category.skills.map((skill, skillIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                variants: skillVariants,\n                                                initial: \"hidden\",\n                                                animate: isInView ? \"visible\" : \"hidden\",\n                                                transition: {\n                                                    delay: categoryIndex * 0.1 + skillIndex * 0.05\n                                                },\n                                                className: \"group stagger-item\",\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    rotate: [\n                                                        0,\n                                                        -1,\n                                                        1,\n                                                        0\n                                                    ],\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    className: \"bg-gray-900/50 border border-gray-800 rounded-xl p-6 hover:border-gray-700 transition-all duration-300 hover:bg-gray-800/50 cursor-pointer relative overflow-hidden\",\n                                                    whileHover: {\n                                                        boxShadow: \"0 10px 30px rgba(0,0,0,0.3)\",\n                                                        borderColor: \"rgba(156, 163, 175, 0.5)\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0\",\n                                                            whileHover: {\n                                                                opacity: 1\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center space-y-3 relative z-10\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                                    className: \"text-3xl\",\n                                                                    whileHover: {\n                                                                        scale: 1.2,\n                                                                        rotate: 360,\n                                                                        transition: {\n                                                                            duration: 0.6\n                                                                        }\n                                                                    },\n                                                                    children: skill.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-sm font-medium text-center\",\n                                                                    children: skill.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, skill.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, category.title, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Skills.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = \"default\", size = \"default\", ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background\", {\n            \"bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700\": variant === \"default\",\n            \"border border-input hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\"\n        }, {\n            \"h-10 py-2 px-4\": size === \"default\",\n            \"h-9 px-3 rounded-md\": size === \"sm\",\n            \"h-11 px-8 rounded-md\": size === \"lg\"\n        }, className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 13,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   fadeInUp: () => (/* binding */ fadeInUp),\n/* harmony export */   staggerContainer: () => (/* binding */ staggerContainer)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nconst fadeInUp = {\n    initial: {\n        opacity: 0,\n        y: 60\n    },\n    animate: {\n        opacity: 1,\n        y: 0\n    },\n    transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n    }\n};\nconst staggerContainer = {\n    animate: {\n        transition: {\n            staggerChildren: 0.1\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRU8sTUFBTUMsV0FBVztJQUN0QkMsU0FBUztRQUFFQyxTQUFTO1FBQUdDLEdBQUc7SUFBRztJQUM3QkMsU0FBUztRQUFFRixTQUFTO1FBQUdDLEdBQUc7SUFBRTtJQUM1QkUsWUFBWTtRQUFFQyxVQUFVO1FBQUtDLE1BQU07SUFBVTtBQUMvQyxFQUFDO0FBRU0sTUFBTUMsbUJBQW1CO0lBQzlCSixTQUFTO1FBQ1BDLFlBQVk7WUFDVkksaUJBQWlCO1FBQ25CO0lBQ0Y7QUFDRixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9ydGZvbGlvLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuXG5leHBvcnQgY29uc3QgZmFkZUluVXAgPSB7XG4gIGluaXRpYWw6IHsgb3BhY2l0eTogMCwgeTogNjAgfSxcbiAgYW5pbWF0ZTogeyBvcGFjaXR5OiAxLCB5OiAwIH0sXG4gIHRyYW5zaXRpb246IHsgZHVyYXRpb246IDAuNiwgZWFzZTogXCJlYXNlT3V0XCIgfVxufVxuXG5leHBvcnQgY29uc3Qgc3RhZ2dlckNvbnRhaW5lciA9IHtcbiAgYW5pbWF0ZToge1xuICAgIHRyYW5zaXRpb246IHtcbiAgICAgIHN0YWdnZXJDaGlsZHJlbjogMC4xXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyIsImZhZGVJblVwIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImVhc2UiLCJzdGFnZ2VyQ29udGFpbmVyIiwic3RhZ2dlckNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b6f78d104f6e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9ydGZvbGlvLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9iYjc4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjZmNzhkMTA0ZjZlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_100_200_300_400_500_600_700_800_900_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"100\\\",\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_100_200_300_400_500_600_700_800_900_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_100_200_300_400_500_600_700_800_900_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Portfolio - Full Stack Developer\",\n    description: \"A creative Full Stack Developer with expertise in building high-performance, scalable, and responsive web solutions across the entire technology stack.\",\n    keywords: [\n        \"Full Stack Developer\",\n        \"React\",\n        \"Next.js\",\n        \"TypeScript\",\n        \"Node.js\",\n        \"Web Development\"\n    ],\n    authors: [\n        {\n            name: \"Your Name\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_100_200_300_400_500_600_700_800_900_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-[#0a0a0a] text-white\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\cookies\portfolio\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/gsap","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();