/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q2Fya2l0JTVDRGVza3RvcCU1Q2Nvb2tpZXMlNUNwb3J0Zm9saW8lNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2Fya2l0JTVDRGVza3RvcCU1Q2Nvb2tpZXMlNUNwb3J0Zm9saW8maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLGdKQUFzRztBQUM3SDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsb0pBQXdHO0FBQ2pJLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3J0Zm9saW8vPzgzYTEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcYXJraXRcXFxcRGVza3RvcFxcXFxjb29raWVzXFxcXHBvcnRmb2xpb1xcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXGFya2l0XFxcXERlc2t0b3BcXFxcY29va2llc1xcXFxwb3J0Zm9saW9cXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFya2l0XFxcXERlc2t0b3BcXFxcY29va2llc1xcXFxwb3J0Zm9saW9cXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXGFya2l0XFxcXERlc2t0b3BcXFxcY29va2llc1xcXFxwb3J0Zm9saW9cXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxhcmtpdFxcXFxEZXNrdG9wXFxcXGNvb2tpZXNcXFxccG9ydGZvbGlvXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYXJraXQlNUNEZXNrdG9wJTVDY29va2llcyU1Q3BvcnRmb2xpbyU1Q3NyYyU1Q2FwcCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BvcnRmb2xpby8/ZmU0OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFya2l0XFxcXERlc2t0b3BcXFxcY29va2llc1xcXFxwb3J0Zm9saW9cXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/Hero */ \"(ssr)/./src/components/sections/Hero.tsx\");\n/* harmony import */ var _components_sections_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/About */ \"(ssr)/./src/components/sections/About.tsx\");\n/* harmony import */ var _components_sections_Skills__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/Skills */ \"(ssr)/./src/components/sections/Skills.tsx\");\n/* harmony import */ var _components_sections_Experience__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/Experience */ \"(ssr)/./src/components/sections/Experience.tsx\");\n/* harmony import */ var _components_sections_Projects__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sections/Projects */ \"(ssr)/./src/components/sections/Projects.tsx\");\n/* harmony import */ var _components_sections_Contact__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/sections/Contact */ \"(ssr)/./src/components/sections/Contact.tsx\");\n/* harmony import */ var _components_ScrollAnimations__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ScrollAnimations */ \"(ssr)/./src/components/ScrollAnimations.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Home() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Smooth scrolling for anchor links\n        const handleSmoothScroll = (e)=>{\n            const target = e.target;\n            if (target.hash) {\n                e.preventDefault();\n                const element = document.querySelector(target.hash);\n                element?.scrollIntoView({\n                    behavior: \"smooth\"\n                });\n            }\n        };\n        document.addEventListener(\"click\", handleSmoothScroll);\n        return ()=>document.removeEventListener(\"click\", handleSmoothScroll);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollAnimations__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white font-medium\",\n                                children: \"Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#about\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#projects\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Work\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#contact\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_About__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Skills__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Experience__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Projects__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Contact__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFaUM7QUFDWTtBQUNFO0FBQ0U7QUFDUTtBQUNKO0FBQ0Y7QUFDUztBQUU3QyxTQUFTUTtJQUN0QlIsZ0RBQVNBLENBQUM7UUFDUixvQ0FBb0M7UUFDcEMsTUFBTVMscUJBQXFCLENBQUNDO1lBQzFCLE1BQU1DLFNBQVNELEVBQUVDLE1BQU07WUFDdkIsSUFBSUEsT0FBT0MsSUFBSSxFQUFFO2dCQUNmRixFQUFFRyxjQUFjO2dCQUNoQixNQUFNQyxVQUFVQyxTQUFTQyxhQUFhLENBQUNMLE9BQU9DLElBQUk7Z0JBQ2xERSxTQUFTRyxlQUFlO29CQUFFQyxVQUFVO2dCQUFTO1lBQy9DO1FBQ0Y7UUFFQUgsU0FBU0ksZ0JBQWdCLENBQUMsU0FBU1Y7UUFDbkMsT0FBTyxJQUFNTSxTQUFTSyxtQkFBbUIsQ0FBQyxTQUFTWDtJQUNyRCxHQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ1k7UUFBS0MsV0FBVTs7MEJBQ2QsOERBQUNmLG9FQUFnQkE7Ozs7OzBCQUdqQiw4REFBQ2dCO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDRTtvQkFBSUYsV0FBVTs4QkFDYiw0RUFBQ0U7d0JBQUlGLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBSUYsV0FBVTswQ0FBeUI7Ozs7OzswQ0FHeEMsOERBQUNFO2dDQUFJRixXQUFVOztrREFDYiw4REFBQ0c7d0NBQUVDLE1BQUs7d0NBQUlKLFdBQVU7a0RBQW1EOzs7Ozs7a0RBR3pFLDhEQUFDRzt3Q0FBRUMsTUFBSzt3Q0FBU0osV0FBVTtrREFBbUQ7Ozs7OztrREFHOUUsOERBQUNHO3dDQUFFQyxNQUFLO3dDQUFZSixXQUFVO2tEQUFtRDs7Ozs7O2tEQUdqRiw4REFBQ0c7d0NBQUVDLE1BQUs7d0NBQVdKLFdBQVU7a0RBQW1EOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVN4Riw4REFBQ3JCLGlFQUFJQTs7Ozs7MEJBQ0wsOERBQUNDLGtFQUFLQTs7Ozs7MEJBQ04sOERBQUNDLG1FQUFNQTs7Ozs7MEJBQ1AsOERBQUNDLHVFQUFVQTs7Ozs7MEJBQ1gsOERBQUNDLHFFQUFRQTs7Ozs7MEJBQ1QsOERBQUNDLG9FQUFPQTs7Ozs7Ozs7Ozs7QUFHZCIsInNvdXJjZXMiOlsid2VicGFjazovL3BvcnRmb2xpby8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgSGVybyBmcm9tICdAL2NvbXBvbmVudHMvc2VjdGlvbnMvSGVybydcbmltcG9ydCBBYm91dCBmcm9tICdAL2NvbXBvbmVudHMvc2VjdGlvbnMvQWJvdXQnXG5pbXBvcnQgU2tpbGxzIGZyb20gJ0AvY29tcG9uZW50cy9zZWN0aW9ucy9Ta2lsbHMnXG5pbXBvcnQgRXhwZXJpZW5jZSBmcm9tICdAL2NvbXBvbmVudHMvc2VjdGlvbnMvRXhwZXJpZW5jZSdcbmltcG9ydCBQcm9qZWN0cyBmcm9tICdAL2NvbXBvbmVudHMvc2VjdGlvbnMvUHJvamVjdHMnXG5pbXBvcnQgQ29udGFjdCBmcm9tICdAL2NvbXBvbmVudHMvc2VjdGlvbnMvQ29udGFjdCdcbmltcG9ydCBTY3JvbGxBbmltYXRpb25zIGZyb20gJ0AvY29tcG9uZW50cy9TY3JvbGxBbmltYXRpb25zJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFNtb290aCBzY3JvbGxpbmcgZm9yIGFuY2hvciBsaW5rc1xuICAgIGNvbnN0IGhhbmRsZVNtb290aFNjcm9sbCA9IChlOiBFdmVudCkgPT4ge1xuICAgICAgY29uc3QgdGFyZ2V0ID0gZS50YXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnRcbiAgICAgIGlmICh0YXJnZXQuaGFzaCkge1xuICAgICAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICAgICAgY29uc3QgZWxlbWVudCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IodGFyZ2V0Lmhhc2gpXG4gICAgICAgIGVsZW1lbnQ/LnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6ICdzbW9vdGgnIH0pXG4gICAgICB9XG4gICAgfVxuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVTbW9vdGhTY3JvbGwpXG4gICAgcmV0dXJuICgpID0+IGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgaGFuZGxlU21vb3RoU2Nyb2xsKVxuICB9LCBbXSlcblxuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICA8U2Nyb2xsQW5pbWF0aW9ucyAvPlxuXG4gICAgICB7LyogU2ltcGxlIE5hdmlnYXRpb24gKi99XG4gICAgICA8bmF2IGNsYXNzTmFtZT1cImZpeGVkIHRvcC0wIGxlZnQtMCByaWdodC0wIHotNTAgYmctYmxhY2svOTAgYmFja2Ryb3AtYmx1ci1zbVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTZ4bCBteC1hdXRvIHB4LTYgcHktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgUG9ydGZvbGlvXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggc3BhY2UteC04XCI+XG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgSG9tZVxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIjYWJvdXRcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICBBYm91dFxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIjcHJvamVjdHNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICBXb3JrXG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPGEgaHJlZj1cIiNjb250YWN0XCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgQ29udGFjdFxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L25hdj5cblxuICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgIDxIZXJvIC8+XG4gICAgICA8QWJvdXQgLz5cbiAgICAgIDxTa2lsbHMgLz5cbiAgICAgIDxFeHBlcmllbmNlIC8+XG4gICAgICA8UHJvamVjdHMgLz5cbiAgICAgIDxDb250YWN0IC8+XG4gICAgPC9tYWluPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiSGVybyIsIkFib3V0IiwiU2tpbGxzIiwiRXhwZXJpZW5jZSIsIlByb2plY3RzIiwiQ29udGFjdCIsIlNjcm9sbEFuaW1hdGlvbnMiLCJIb21lIiwiaGFuZGxlU21vb3RoU2Nyb2xsIiwiZSIsInRhcmdldCIsImhhc2giLCJwcmV2ZW50RGVmYXVsdCIsImVsZW1lbnQiLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3IiLCJzY3JvbGxJbnRvVmlldyIsImJlaGF2aW9yIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJtYWluIiwiY2xhc3NOYW1lIiwibmF2IiwiZGl2IiwiYSIsImhyZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ScrollAnimations.tsx":
/*!*********************************************!*\
  !*** ./src/components/ScrollAnimations.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollAnimations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! gsap */ \"(ssr)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(ssr)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ScrollAnimations() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Register ScrollTrigger plugin\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__.ScrollTrigger);\n        // Smooth scrolling\n        const initLenis = async ()=>{\n            try {\n                const Lenis = (await __webpack_require__.e(/*! import() */ \"vendor-chunks/lenis\").then(__webpack_require__.bind(__webpack_require__, /*! lenis */ \"(ssr)/./node_modules/lenis/dist/lenis.mjs\"))).default;\n                const lenis = new Lenis({\n                    duration: 1.2,\n                    easing: (t)=>Math.min(1, 1.001 - Math.pow(2, -10 * t)),\n                    smooth: true\n                });\n                function raf(time) {\n                    lenis.raf(time);\n                    requestAnimationFrame(raf);\n                }\n                requestAnimationFrame(raf);\n            } catch (error) {\n                console.log(\"Lenis not available, using default scroll\");\n            }\n        };\n        // Initialize smooth scrolling\n        initLenis();\n        // Parallax background elements\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(\".parallax-slow\", {\n            yPercent: -50,\n            ease: \"none\",\n            scrollTrigger: {\n                trigger: \"body\",\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: true\n            }\n        });\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(\".parallax-fast\", {\n            yPercent: -100,\n            ease: \"none\",\n            scrollTrigger: {\n                trigger: \"body\",\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: true\n            }\n        });\n        // Text reveal animations\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.utils.toArray(\".reveal-text\").forEach((element)=>{\n            gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.fromTo(element, {\n                y: 100,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 1,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: element,\n                    start: \"top 80%\",\n                    end: \"bottom 20%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n        });\n        // Stagger animations for cards\n        gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.utils.toArray(\".stagger-item\").forEach((element, index)=>{\n            gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.fromTo(element, {\n                y: 60,\n                opacity: 0,\n                scale: 0.9\n            }, {\n                y: 0,\n                opacity: 1,\n                scale: 1,\n                duration: 0.8,\n                delay: index * 0.1,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: element,\n                    start: \"top 85%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n        });\n        // Horizontal scroll for projects\n        const projectsContainer = document.querySelector(\".horizontal-scroll\");\n        if (projectsContainer) {\n            const projects = gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.utils.toArray(\".project-card\");\n            gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(projects, {\n                xPercent: -100 * (projects.length - 1),\n                ease: \"none\",\n                scrollTrigger: {\n                    trigger: projectsContainer,\n                    pin: true,\n                    scrub: 1,\n                    snap: 1 / (projects.length - 1),\n                    end: ()=>\"+=\" + projectsContainer.offsetWidth\n                }\n            });\n        }\n        // Magnetic effect for buttons\n        const magneticElements = document.querySelectorAll(\".magnetic\");\n        magneticElements.forEach((element)=>{\n            const handleMouseMove = (e)=>{\n                const rect = element.getBoundingClientRect();\n                const x = e.clientX - rect.left - rect.width / 2;\n                const y = e.clientY - rect.top - rect.height / 2;\n                gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(element, {\n                    x: x * 0.3,\n                    y: y * 0.3,\n                    duration: 0.3,\n                    ease: \"power2.out\"\n                });\n            };\n            const handleMouseLeave = ()=>{\n                gsap__WEBPACK_IMPORTED_MODULE_1__.gsap.to(element, {\n                    x: 0,\n                    y: 0,\n                    duration: 0.5,\n                    ease: \"elastic.out(1, 0.3)\"\n                });\n            };\n            element.addEventListener(\"mousemove\", handleMouseMove);\n            element.addEventListener(\"mouseleave\", handleMouseLeave);\n        });\n        // Cleanup\n        return ()=>{\n            gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__.ScrollTrigger.getAll().forEach((trigger)=>trigger.kill());\n        };\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TY3JvbGxBbmltYXRpb25zLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs2REFFaUM7QUFDTjtBQUN1QjtBQUVuQyxTQUFTRztJQUN0QkgsZ0RBQVNBLENBQUM7UUFDUixnQ0FBZ0M7UUFDaENDLHNDQUFJQSxDQUFDRyxjQUFjLENBQUNGLDZEQUFhQTtRQUVqQyxtQkFBbUI7UUFDbkIsTUFBTUcsWUFBWTtZQUNoQixJQUFJO2dCQUNGLE1BQU1DLFFBQVEsQ0FBQyxNQUFNLDBLQUFjLEVBQUdDLE9BQU87Z0JBQzdDLE1BQU1DLFFBQVEsSUFBSUYsTUFBTTtvQkFDdEJHLFVBQVU7b0JBQ1ZDLFFBQVEsQ0FBQ0MsSUFBTUMsS0FBS0MsR0FBRyxDQUFDLEdBQUcsUUFBUUQsS0FBS0UsR0FBRyxDQUFDLEdBQUcsQ0FBQyxLQUFLSDtvQkFDckRJLFFBQVE7Z0JBQ1Y7Z0JBRUEsU0FBU0MsSUFBSUMsSUFBWTtvQkFDdkJULE1BQU1RLEdBQUcsQ0FBQ0M7b0JBQ1ZDLHNCQUFzQkY7Z0JBQ3hCO2dCQUNBRSxzQkFBc0JGO1lBQ3hCLEVBQUUsT0FBT0csT0FBTztnQkFDZEMsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLDhCQUE4QjtRQUM5QmhCO1FBRUEsK0JBQStCO1FBQy9CSixzQ0FBSUEsQ0FBQ3FCLEVBQUUsQ0FBQyxrQkFBa0I7WUFDeEJDLFVBQVUsQ0FBQztZQUNYQyxNQUFNO1lBQ05DLGVBQWU7Z0JBQ2JDLFNBQVM7Z0JBQ1RDLE9BQU87Z0JBQ1BDLEtBQUs7Z0JBQ0xDLE9BQU87WUFDVDtRQUNGO1FBRUE1QixzQ0FBSUEsQ0FBQ3FCLEVBQUUsQ0FBQyxrQkFBa0I7WUFDeEJDLFVBQVUsQ0FBQztZQUNYQyxNQUFNO1lBQ05DLGVBQWU7Z0JBQ2JDLFNBQVM7Z0JBQ1RDLE9BQU87Z0JBQ1BDLEtBQUs7Z0JBQ0xDLE9BQU87WUFDVDtRQUNGO1FBRUEseUJBQXlCO1FBQ3pCNUIsc0NBQUlBLENBQUM2QixLQUFLLENBQUNDLE9BQU8sQ0FBQyxnQkFBZ0JDLE9BQU8sQ0FBQyxDQUFDQztZQUMxQ2hDLHNDQUFJQSxDQUFDaUMsTUFBTSxDQUNURCxTQUNBO2dCQUNFRSxHQUFHO2dCQUNIQyxTQUFTO1lBQ1gsR0FDQTtnQkFDRUQsR0FBRztnQkFDSEMsU0FBUztnQkFDVDNCLFVBQVU7Z0JBQ1ZlLE1BQU07Z0JBQ05DLGVBQWU7b0JBQ2JDLFNBQVNPO29CQUNUTixPQUFPO29CQUNQQyxLQUFLO29CQUNMUyxlQUFlO2dCQUNqQjtZQUNGO1FBRUo7UUFFQSwrQkFBK0I7UUFDL0JwQyxzQ0FBSUEsQ0FBQzZCLEtBQUssQ0FBQ0MsT0FBTyxDQUFDLGlCQUFpQkMsT0FBTyxDQUFDLENBQUNDLFNBQWNLO1lBQ3pEckMsc0NBQUlBLENBQUNpQyxNQUFNLENBQ1RELFNBQ0E7Z0JBQ0VFLEdBQUc7Z0JBQ0hDLFNBQVM7Z0JBQ1RHLE9BQU87WUFDVCxHQUNBO2dCQUNFSixHQUFHO2dCQUNIQyxTQUFTO2dCQUNURyxPQUFPO2dCQUNQOUIsVUFBVTtnQkFDVitCLE9BQU9GLFFBQVE7Z0JBQ2ZkLE1BQU07Z0JBQ05DLGVBQWU7b0JBQ2JDLFNBQVNPO29CQUNUTixPQUFPO29CQUNQVSxlQUFlO2dCQUNqQjtZQUNGO1FBRUo7UUFFQSxpQ0FBaUM7UUFDakMsTUFBTUksb0JBQW9CQyxTQUFTQyxhQUFhLENBQUM7UUFDakQsSUFBSUYsbUJBQW1CO1lBQ3JCLE1BQU1HLFdBQVczQyxzQ0FBSUEsQ0FBQzZCLEtBQUssQ0FBQ0MsT0FBTyxDQUFDO1lBRXBDOUIsc0NBQUlBLENBQUNxQixFQUFFLENBQUNzQixVQUFVO2dCQUNoQkMsVUFBVSxDQUFDLE1BQU9ELENBQUFBLFNBQVNFLE1BQU0sR0FBRztnQkFDcEN0QixNQUFNO2dCQUNOQyxlQUFlO29CQUNiQyxTQUFTZTtvQkFDVE0sS0FBSztvQkFDTGxCLE9BQU87b0JBQ1BtQixNQUFNLElBQUtKLENBQUFBLFNBQVNFLE1BQU0sR0FBRztvQkFDN0JsQixLQUFLLElBQU0sT0FBTyxrQkFBbUNxQixXQUFXO2dCQUNsRTtZQUNGO1FBQ0Y7UUFFQSw4QkFBOEI7UUFDOUIsTUFBTUMsbUJBQW1CUixTQUFTUyxnQkFBZ0IsQ0FBQztRQUNuREQsaUJBQWlCbEIsT0FBTyxDQUFDLENBQUNDO1lBQ3hCLE1BQU1tQixrQkFBa0IsQ0FBQ0M7Z0JBQ3ZCLE1BQU1DLE9BQU9yQixRQUFRc0IscUJBQXFCO2dCQUMxQyxNQUFNQyxJQUFJSCxFQUFFSSxPQUFPLEdBQUdILEtBQUtJLElBQUksR0FBR0osS0FBS0ssS0FBSyxHQUFHO2dCQUMvQyxNQUFNeEIsSUFBSWtCLEVBQUVPLE9BQU8sR0FBR04sS0FBS08sR0FBRyxHQUFHUCxLQUFLUSxNQUFNLEdBQUc7Z0JBRS9DN0Qsc0NBQUlBLENBQUNxQixFQUFFLENBQUNXLFNBQVM7b0JBQ2Z1QixHQUFHQSxJQUFJO29CQUNQckIsR0FBR0EsSUFBSTtvQkFDUDFCLFVBQVU7b0JBQ1ZlLE1BQU07Z0JBQ1I7WUFDRjtZQUVBLE1BQU11QyxtQkFBbUI7Z0JBQ3ZCOUQsc0NBQUlBLENBQUNxQixFQUFFLENBQUNXLFNBQVM7b0JBQ2Z1QixHQUFHO29CQUNIckIsR0FBRztvQkFDSDFCLFVBQVU7b0JBQ1ZlLE1BQU07Z0JBQ1I7WUFDRjtZQUVBUyxRQUFRK0IsZ0JBQWdCLENBQUMsYUFBYVo7WUFDdENuQixRQUFRK0IsZ0JBQWdCLENBQUMsY0FBY0Q7UUFDekM7UUFFQSxVQUFVO1FBQ1YsT0FBTztZQUNMN0QsNkRBQWFBLENBQUMrRCxNQUFNLEdBQUdqQyxPQUFPLENBQUNOLENBQUFBLFVBQVdBLFFBQVF3QyxJQUFJO1FBQ3hEO0lBQ0YsR0FBRyxFQUFFO0lBRUwsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9ydGZvbGlvLy4vc3JjL2NvbXBvbmVudHMvU2Nyb2xsQW5pbWF0aW9ucy50c3g/MWM0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBnc2FwIH0gZnJvbSAnZ3NhcCdcbmltcG9ydCB7IFNjcm9sbFRyaWdnZXIgfSBmcm9tICdnc2FwL1Njcm9sbFRyaWdnZXInXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNjcm9sbEFuaW1hdGlvbnMoKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gUmVnaXN0ZXIgU2Nyb2xsVHJpZ2dlciBwbHVnaW5cbiAgICBnc2FwLnJlZ2lzdGVyUGx1Z2luKFNjcm9sbFRyaWdnZXIpXG5cbiAgICAvLyBTbW9vdGggc2Nyb2xsaW5nXG4gICAgY29uc3QgaW5pdExlbmlzID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgTGVuaXMgPSAoYXdhaXQgaW1wb3J0KCdsZW5pcycpKS5kZWZhdWx0XG4gICAgICAgIGNvbnN0IGxlbmlzID0gbmV3IExlbmlzKHtcbiAgICAgICAgICBkdXJhdGlvbjogMS4yLFxuICAgICAgICAgIGVhc2luZzogKHQpID0+IE1hdGgubWluKDEsIDEuMDAxIC0gTWF0aC5wb3coMiwgLTEwICogdCkpLFxuICAgICAgICAgIHNtb290aDogdHJ1ZSxcbiAgICAgICAgfSlcblxuICAgICAgICBmdW5jdGlvbiByYWYodGltZTogbnVtYmVyKSB7XG4gICAgICAgICAgbGVuaXMucmFmKHRpbWUpXG4gICAgICAgICAgcmVxdWVzdEFuaW1hdGlvbkZyYW1lKHJhZilcbiAgICAgICAgfVxuICAgICAgICByZXF1ZXN0QW5pbWF0aW9uRnJhbWUocmFmKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0xlbmlzIG5vdCBhdmFpbGFibGUsIHVzaW5nIGRlZmF1bHQgc2Nyb2xsJylcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBJbml0aWFsaXplIHNtb290aCBzY3JvbGxpbmdcbiAgICBpbml0TGVuaXMoKVxuXG4gICAgLy8gUGFyYWxsYXggYmFja2dyb3VuZCBlbGVtZW50c1xuICAgIGdzYXAudG8oJy5wYXJhbGxheC1zbG93Jywge1xuICAgICAgeVBlcmNlbnQ6IC01MCxcbiAgICAgIGVhc2U6ICdub25lJyxcbiAgICAgIHNjcm9sbFRyaWdnZXI6IHtcbiAgICAgICAgdHJpZ2dlcjogJ2JvZHknLFxuICAgICAgICBzdGFydDogJ3RvcCBib3R0b20nLFxuICAgICAgICBlbmQ6ICdib3R0b20gdG9wJyxcbiAgICAgICAgc2NydWI6IHRydWUsXG4gICAgICB9LFxuICAgIH0pXG5cbiAgICBnc2FwLnRvKCcucGFyYWxsYXgtZmFzdCcsIHtcbiAgICAgIHlQZXJjZW50OiAtMTAwLFxuICAgICAgZWFzZTogJ25vbmUnLFxuICAgICAgc2Nyb2xsVHJpZ2dlcjoge1xuICAgICAgICB0cmlnZ2VyOiAnYm9keScsXG4gICAgICAgIHN0YXJ0OiAndG9wIGJvdHRvbScsXG4gICAgICAgIGVuZDogJ2JvdHRvbSB0b3AnLFxuICAgICAgICBzY3J1YjogdHJ1ZSxcbiAgICAgIH0sXG4gICAgfSlcblxuICAgIC8vIFRleHQgcmV2ZWFsIGFuaW1hdGlvbnNcbiAgICBnc2FwLnV0aWxzLnRvQXJyYXkoJy5yZXZlYWwtdGV4dCcpLmZvckVhY2goKGVsZW1lbnQ6IGFueSkgPT4ge1xuICAgICAgZ3NhcC5mcm9tVG8oXG4gICAgICAgIGVsZW1lbnQsXG4gICAgICAgIHtcbiAgICAgICAgICB5OiAxMDAsXG4gICAgICAgICAgb3BhY2l0eTogMCxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIHk6IDAsXG4gICAgICAgICAgb3BhY2l0eTogMSxcbiAgICAgICAgICBkdXJhdGlvbjogMSxcbiAgICAgICAgICBlYXNlOiAncG93ZXIzLm91dCcsXG4gICAgICAgICAgc2Nyb2xsVHJpZ2dlcjoge1xuICAgICAgICAgICAgdHJpZ2dlcjogZWxlbWVudCxcbiAgICAgICAgICAgIHN0YXJ0OiAndG9wIDgwJScsXG4gICAgICAgICAgICBlbmQ6ICdib3R0b20gMjAlJyxcbiAgICAgICAgICAgIHRvZ2dsZUFjdGlvbnM6ICdwbGF5IG5vbmUgbm9uZSByZXZlcnNlJyxcbiAgICAgICAgICB9LFxuICAgICAgICB9XG4gICAgICApXG4gICAgfSlcblxuICAgIC8vIFN0YWdnZXIgYW5pbWF0aW9ucyBmb3IgY2FyZHNcbiAgICBnc2FwLnV0aWxzLnRvQXJyYXkoJy5zdGFnZ2VyLWl0ZW0nKS5mb3JFYWNoKChlbGVtZW50OiBhbnksIGluZGV4KSA9PiB7XG4gICAgICBnc2FwLmZyb21UbyhcbiAgICAgICAgZWxlbWVudCxcbiAgICAgICAge1xuICAgICAgICAgIHk6IDYwLFxuICAgICAgICAgIG9wYWNpdHk6IDAsXG4gICAgICAgICAgc2NhbGU6IDAuOSxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIHk6IDAsXG4gICAgICAgICAgb3BhY2l0eTogMSxcbiAgICAgICAgICBzY2FsZTogMSxcbiAgICAgICAgICBkdXJhdGlvbjogMC44LFxuICAgICAgICAgIGRlbGF5OiBpbmRleCAqIDAuMSxcbiAgICAgICAgICBlYXNlOiAncG93ZXIzLm91dCcsXG4gICAgICAgICAgc2Nyb2xsVHJpZ2dlcjoge1xuICAgICAgICAgICAgdHJpZ2dlcjogZWxlbWVudCxcbiAgICAgICAgICAgIHN0YXJ0OiAndG9wIDg1JScsXG4gICAgICAgICAgICB0b2dnbGVBY3Rpb25zOiAncGxheSBub25lIG5vbmUgcmV2ZXJzZScsXG4gICAgICAgICAgfSxcbiAgICAgICAgfVxuICAgICAgKVxuICAgIH0pXG5cbiAgICAvLyBIb3Jpem9udGFsIHNjcm9sbCBmb3IgcHJvamVjdHNcbiAgICBjb25zdCBwcm9qZWN0c0NvbnRhaW5lciA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5ob3Jpem9udGFsLXNjcm9sbCcpXG4gICAgaWYgKHByb2plY3RzQ29udGFpbmVyKSB7XG4gICAgICBjb25zdCBwcm9qZWN0cyA9IGdzYXAudXRpbHMudG9BcnJheSgnLnByb2plY3QtY2FyZCcpXG4gICAgICBcbiAgICAgIGdzYXAudG8ocHJvamVjdHMsIHtcbiAgICAgICAgeFBlcmNlbnQ6IC0xMDAgKiAocHJvamVjdHMubGVuZ3RoIC0gMSksXG4gICAgICAgIGVhc2U6ICdub25lJyxcbiAgICAgICAgc2Nyb2xsVHJpZ2dlcjoge1xuICAgICAgICAgIHRyaWdnZXI6IHByb2plY3RzQ29udGFpbmVyLFxuICAgICAgICAgIHBpbjogdHJ1ZSxcbiAgICAgICAgICBzY3J1YjogMSxcbiAgICAgICAgICBzbmFwOiAxIC8gKHByb2plY3RzLmxlbmd0aCAtIDEpLFxuICAgICAgICAgIGVuZDogKCkgPT4gJys9JyArIChwcm9qZWN0c0NvbnRhaW5lciBhcyBIVE1MRWxlbWVudCkub2Zmc2V0V2lkdGgsXG4gICAgICAgIH0sXG4gICAgICB9KVxuICAgIH1cblxuICAgIC8vIE1hZ25ldGljIGVmZmVjdCBmb3IgYnV0dG9uc1xuICAgIGNvbnN0IG1hZ25ldGljRWxlbWVudHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcubWFnbmV0aWMnKVxuICAgIG1hZ25ldGljRWxlbWVudHMuZm9yRWFjaCgoZWxlbWVudCkgPT4ge1xuICAgICAgY29uc3QgaGFuZGxlTW91c2VNb3ZlID0gKGU6IE1vdXNlRXZlbnQpID0+IHtcbiAgICAgICAgY29uc3QgcmVjdCA9IGVsZW1lbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KClcbiAgICAgICAgY29uc3QgeCA9IGUuY2xpZW50WCAtIHJlY3QubGVmdCAtIHJlY3Qud2lkdGggLyAyXG4gICAgICAgIGNvbnN0IHkgPSBlLmNsaWVudFkgLSByZWN0LnRvcCAtIHJlY3QuaGVpZ2h0IC8gMlxuICAgICAgICBcbiAgICAgICAgZ3NhcC50byhlbGVtZW50LCB7XG4gICAgICAgICAgeDogeCAqIDAuMyxcbiAgICAgICAgICB5OiB5ICogMC4zLFxuICAgICAgICAgIGR1cmF0aW9uOiAwLjMsXG4gICAgICAgICAgZWFzZTogJ3Bvd2VyMi5vdXQnLFxuICAgICAgICB9KVxuICAgICAgfVxuXG4gICAgICBjb25zdCBoYW5kbGVNb3VzZUxlYXZlID0gKCkgPT4ge1xuICAgICAgICBnc2FwLnRvKGVsZW1lbnQsIHtcbiAgICAgICAgICB4OiAwLFxuICAgICAgICAgIHk6IDAsXG4gICAgICAgICAgZHVyYXRpb246IDAuNSxcbiAgICAgICAgICBlYXNlOiAnZWxhc3RpYy5vdXQoMSwgMC4zKScsXG4gICAgICAgIH0pXG4gICAgICB9XG5cbiAgICAgIGVsZW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKVxuICAgICAgZWxlbWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWxlYXZlJywgaGFuZGxlTW91c2VMZWF2ZSlcbiAgICB9KVxuXG4gICAgLy8gQ2xlYW51cFxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBTY3JvbGxUcmlnZ2VyLmdldEFsbCgpLmZvckVhY2godHJpZ2dlciA9PiB0cmlnZ2VyLmtpbGwoKSlcbiAgICB9XG4gIH0sIFtdKVxuXG4gIHJldHVybiBudWxsXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiZ3NhcCIsIlNjcm9sbFRyaWdnZXIiLCJTY3JvbGxBbmltYXRpb25zIiwicmVnaXN0ZXJQbHVnaW4iLCJpbml0TGVuaXMiLCJMZW5pcyIsImRlZmF1bHQiLCJsZW5pcyIsImR1cmF0aW9uIiwiZWFzaW5nIiwidCIsIk1hdGgiLCJtaW4iLCJwb3ciLCJzbW9vdGgiLCJyYWYiLCJ0aW1lIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwiZXJyb3IiLCJjb25zb2xlIiwibG9nIiwidG8iLCJ5UGVyY2VudCIsImVhc2UiLCJzY3JvbGxUcmlnZ2VyIiwidHJpZ2dlciIsInN0YXJ0IiwiZW5kIiwic2NydWIiLCJ1dGlscyIsInRvQXJyYXkiLCJmb3JFYWNoIiwiZWxlbWVudCIsImZyb21UbyIsInkiLCJvcGFjaXR5IiwidG9nZ2xlQWN0aW9ucyIsImluZGV4Iiwic2NhbGUiLCJkZWxheSIsInByb2plY3RzQ29udGFpbmVyIiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yIiwicHJvamVjdHMiLCJ4UGVyY2VudCIsImxlbmd0aCIsInBpbiIsInNuYXAiLCJvZmZzZXRXaWR0aCIsIm1hZ25ldGljRWxlbWVudHMiLCJxdWVyeVNlbGVjdG9yQWxsIiwiaGFuZGxlTW91c2VNb3ZlIiwiZSIsInJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJ4IiwiY2xpZW50WCIsImxlZnQiLCJ3aWR0aCIsImNsaWVudFkiLCJ0b3AiLCJoZWlnaHQiLCJoYW5kbGVNb3VzZUxlYXZlIiwiYWRkRXZlbnRMaXN0ZW5lciIsImdldEFsbCIsImtpbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ScrollAnimations.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/About.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/About.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction About() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        className: \"py-20 px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                viewport: {\n                    once: true\n                },\n                className: \"text-center mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                        children: \"About Me\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl mx-auto space-y-6 text-gray-400 text-lg leading-relaxed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Hi, I'm \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: \"Your Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 23\n                                    }, this),\n                                    \", a full stack developer passionate about creating digital experiences that matter.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"I specialize in building modern web applications using React, Node.js, and other cutting-edge technologies. My focus is on writing clean, maintainable code and delivering solutions that solve real problems.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"When I'm not coding, you can find me exploring new technologies, contributing to open source projects, or sharing knowledge with the developer community.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/About.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Contact.tsx":
/*!*********************************************!*\
  !*** ./src/components/sections/Contact.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Contact() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"py-20 px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-8\",\n                            children: \"Have a project in mind?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"mailto:<EMAIL>\",\n                                className: \"inline-flex items-center text-2xl md:text-3xl text-white hover:text-blue-400 transition-colors duration-300 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"mr-4 group-hover:scale-110 transition-transform duration-300\",\n                                        size: 32\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"<EMAIL>\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"flex justify-center space-x-8 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://github.com\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-4 bg-gray-800 hover:bg-gray-700 rounded-full transition-colors duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 24,\n                                        className: \"text-white group-hover:scale-110 transition-transform duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://linkedin.com\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-4 bg-gray-800 hover:bg-gray-700 rounded-full transition-colors duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24,\n                                        className: \"text-white group-hover:scale-110 transition-transform duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center border-t border-gray-800 pt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2 text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://github.com/Tajmirul/portfolio-2.0\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"hover:text-white transition-colors duration-300 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Design & built by Your Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-white mb-2\",\n                                    children: \"YOUR NAME\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                    children: \"<EMAIL>\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Contact.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Contact.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Experience.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/Experience.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Experience)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst experiences = [\n    {\n        company: \"Strativ AB\",\n        position: \"Software Engineer (Frontend)\",\n        period: \"Dec 2024 - Present\",\n        description: \"Leading frontend development initiatives and building scalable web applications.\"\n    },\n    {\n        company: \"Epikcoders\",\n        position: \"Frontend Developer\",\n        period: \"Oct 2023 - Nov 2024\",\n        description: \"Developed responsive web applications using React and modern frontend technologies.\"\n    },\n    {\n        company: \"Anchorblock Technology\",\n        position: \"FRONTEND ENGINEER\",\n        period: \"Oct 2022 - Sep 2023\",\n        description: \"Built user interfaces and implemented frontend solutions for various client projects.\"\n    },\n    {\n        company: \"Branex IT\",\n        position: \"Frontend Developer (Part-time)\",\n        period: \"Jan 2022 - Oct 2022\",\n        description: \"Collaborated on frontend development projects while maintaining high code quality standards.\"\n    }\n];\nfunction Experience() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"experience\",\n        className: \"py-20 px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                        children: \"My Experience\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: experiences.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"relative\",\n                            children: [\n                                index !== experiences.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-6 top-16 w-px h-16 bg-gray-700\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-3 h-3 bg-blue-500 rounded-full mt-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 bg-gray-900/50 border border-gray-800 rounded-xl p-6 hover:border-gray-700 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-white mb-1\",\n                                                                    children: exp.company\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                                    lineNumber: 71,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-400 font-medium\",\n                                                                    children: exp.position\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                                    lineNumber: 74,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm mt-2 md:mt-0\",\n                                                            children: exp.period\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 leading-relaxed\",\n                                                    children: exp.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Experience.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Experience.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Hero() {\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        element?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"min-h-screen flex items-center justify-center px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-lg\",\n                            children: \"Hi, I'm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-white\",\n                            children: \"Your Name\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl md:text-2xl text-gray-300 font-light\",\n                            children: \"Full Stack Developer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    className: \"text-gray-400 text-lg leading-relaxed max-w-2xl mx-auto mt-8 mb-12\",\n                    children: \"I build digital experiences that are functional, beautiful, and user-centered. Passionate about clean code and thoughtful design.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>scrollToSection(\"projects\"),\n                            className: \"px-8 py-3 bg-white text-black rounded-lg hover:bg-gray-200 transition-colors duration-300\",\n                            children: \"View Work\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>scrollToSection(\"contact\"),\n                            className: \"px-8 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors duration-300\",\n                            children: \"Contact\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.9\n                    },\n                    className: \"flex justify-center space-x-6 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://github.com\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://linkedin.com\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"mailto:<EMAIL>\",\n                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 1.2\n                    },\n                    className: \"flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mb-2\",\n                            children: \"Scroll down\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            animate: {\n                                y: [\n                                    0,\n                                    8,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity\n                            },\n                            className: \"cursor-pointer\",\n                            onClick: ()=>scrollToSection(\"about\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Projects.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Projects.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Projects)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst projects = [\n    {\n        title: \"E-commerce Platform\",\n        description: \"Modern e-commerce solution with Next.js and Stripe integration\",\n        technologies: [\n            \"Next.js\",\n            \"TypeScript\",\n            \"Stripe\",\n            \"Tailwind CSS\"\n        ],\n        github: \"https://github.com\",\n        demo: \"https://demo.com\"\n    },\n    {\n        title: \"Task Management App\",\n        description: \"Collaborative task management with real-time updates\",\n        technologies: [\n            \"React\",\n            \"Node.js\",\n            \"Socket.io\",\n            \"MongoDB\"\n        ],\n        github: \"https://github.com\",\n        demo: \"https://demo.com\"\n    },\n    {\n        title: \"Weather Dashboard\",\n        description: \"Beautiful weather app with location-based forecasts\",\n        technologies: [\n            \"React\",\n            \"OpenWeather API\",\n            \"Chart.js\"\n        ],\n        github: \"https://github.com\",\n        demo: \"https://demo.com\"\n    },\n    {\n        title: \"Portfolio Website\",\n        description: \"Responsive portfolio with modern animations\",\n        technologies: [\n            \"Next.js\",\n            \"Framer Motion\",\n            \"Tailwind CSS\"\n        ],\n        github: \"https://github.com\",\n        demo: \"https://demo.com\"\n    }\n];\nfunction Projects() {\n    const ref = useRef(null);\n    const isInView = useInView(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const { scrollYProgress } = useScroll({\n        target: ref,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y = useTransform(scrollYProgress, [\n        0,\n        1\n    ], [\n        100,\n        -100\n    ]);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.15,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const projectVariants = {\n        hidden: {\n            opacity: 0,\n            y: 60,\n            scale: 0.9\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            scale: 1,\n            transition: {\n                type: \"spring\",\n                stiffness: 100,\n                damping: 15,\n                duration: 0.8\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        className: \"py-20 px-6 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                style: {\n                    y\n                },\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 right-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-7xl\",\n                ref: ref,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: isInView ? {\n                            opacity: 1,\n                            y: 0\n                        } : {\n                            opacity: 0,\n                            y: 50\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16 reveal-text\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h2, {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                scale: 1\n                            } : {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            transition: {\n                                duration: 1,\n                                delay: 0.2\n                            },\n                            children: \"SELECTED PROJECTS\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                variants: projectVariants,\n                                className: \"group cursor-pointer project-card stagger-item\",\n                                onClick: ()=>window.open(project.url, \"_blank\"),\n                                whileHover: {\n                                    y: -10,\n                                    transition: {\n                                        duration: 0.3\n                                    }\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    className: \"bg-gray-900/50 border border-gray-800 rounded-xl overflow-hidden hover:border-gray-700 transition-all duration-500 hover:bg-gray-800/50 h-full\",\n                                    whileHover: {\n                                        boxShadow: \"0 20px 40px rgba(0,0,0,0.4)\",\n                                        borderColor: \"rgba(156, 163, 175, 0.3)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden h-48\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center\",\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    transition: {\n                                                        duration: 0.4\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-lg\",\n                                                        children: \"Project Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"absolute inset-0 bg-black/60 flex items-center justify-center\",\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    whileHover: {\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n                                                                className: \"p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors backdrop-blur-sm\",\n                                                                whileHover: {\n                                                                    scale: 1.1,\n                                                                    rotate: 5\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.9\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    size: 20,\n                                                                    className: \"text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n                                                                className: \"p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors backdrop-blur-sm\",\n                                                                whileHover: {\n                                                                    scale: 1.1,\n                                                                    rotate: -5\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.9\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    size: 20,\n                                                                    className: \"text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"text-gray-400 text-sm mb-2\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.5 + index * 0.1\n                                                    },\n                                                    children: project.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h3, {\n                                                    className: \"text-xl font-semibold text-white mb-3\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.6 + index * 0.1\n                                                    },\n                                                    children: project.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                                                    className: \"text-gray-400 mb-4 text-sm leading-relaxed\",\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1\n                                                    } : {\n                                                        opacity: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.7 + index * 0.1\n                                                    },\n                                                    children: project.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.8 + index * 0.1\n                                                    },\n                                                    children: project.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.span, {\n                                                            className: \"px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs\",\n                                                            initial: {\n                                                                opacity: 0,\n                                                                scale: 0.8\n                                                            },\n                                                            animate: isInView ? {\n                                                                opacity: 1,\n                                                                scale: 1\n                                                            } : {\n                                                                opacity: 0,\n                                                                scale: 0.8\n                                                            },\n                                                            transition: {\n                                                                delay: 0.9 + index * 0.1 + techIndex * 0.05\n                                                            },\n                                                            whileHover: {\n                                                                scale: 1.05,\n                                                                backgroundColor: \"rgba(75, 85, 99, 0.8)\"\n                                                            },\n                                                            children: tech\n                                                        }, tech, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            }, project.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Projects.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Skills.tsx":
/*!********************************************!*\
  !*** ./src/components/sections/Skills.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Skills)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst skills = [\n    \"JavaScript\",\n    \"TypeScript\",\n    \"React\",\n    \"Next.js\",\n    \"Node.js\",\n    \"Express\",\n    \"PostgreSQL\",\n    \"MongoDB\",\n    \"Tailwind CSS\",\n    \"Git\",\n    \"Docker\",\n    \"AWS\",\n    \"Python\",\n    \"GraphQL\",\n    \"Redux\"\n];\nfunction Skills() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        className: \"py-20 px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"Skills\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-lg\",\n                            children: \"Technologies I work with\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"flex flex-wrap justify-center gap-4\",\n                    children: skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.4,\n                                delay: index * 0.05\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"px-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-gray-300 hover:bg-gray-700/50 hover:border-gray-600 transition-colors duration-300\",\n                            children: skill\n                        }, skill, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Skills.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b6f78d104f6e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9ydGZvbGlvLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9iYjc4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjZmNzhkMTA0ZjZlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_100_200_300_400_500_600_700_800_900_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"100\\\",\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_100_200_300_400_500_600_700_800_900_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_100_200_300_400_500_600_700_800_900_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Portfolio - Full Stack Developer\",\n    description: \"A creative Full Stack Developer with expertise in building high-performance, scalable, and responsive web solutions across the entire technology stack.\",\n    keywords: [\n        \"Full Stack Developer\",\n        \"React\",\n        \"Next.js\",\n        \"TypeScript\",\n        \"Node.js\",\n        \"Web Development\"\n    ],\n    authors: [\n        {\n            name: \"Your Name\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_100_200_300_400_500_600_700_800_900_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-[#0a0a0a] text-white\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\cookies\portfolio\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/gsap"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Ccookies%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();