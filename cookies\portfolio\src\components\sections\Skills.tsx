'use client'

import { motion } from 'framer-motion'

const skillCategories = [
  {
    title: "Languages & Frameworks",
    skills: [
      { name: "JavaScript", color: "bg-yellow-500" },
      { name: "TypeScript", color: "bg-blue-500" },
      { name: "React.js", color: "bg-cyan-500" },
      { name: "Next.js", color: "bg-gray-700" },
      { name: "TailwindCSS", color: "bg-teal-500" },
      { name: "<PERSON>tra<PERSON>", color: "bg-purple-600" },
      { name: "C++", color: "bg-blue-600" },
      { name: "CSS", color: "bg-blue-400" },
      { name: "C", color: "bg-gray-600" }
    ]
  },
  {
    title: "Backend & Databases",
    skills: [
      { name: "Node.js", color: "bg-green-600" },
      { name: "PostgreSQL", color: "bg-blue-700" },
      { name: "<PERSON><PERSON>a", color: "bg-gray-800" },
      { name: "Firebase", color: "bg-orange-500" },
      { name: "Artificial Intelligence", color: "bg-red-500" },
      { name: "Nginx", color: "bg-green-700" },
      { name: "Express", color: "bg-gray-700" }
    ]
  }
]

export default function Skills() {
  return (
    <section id="skills" className="py-20 px-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Tools that I have used
          </h2>
        </motion.div>

        <div className="space-y-12">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: categoryIndex * 0.2 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h3 className="text-xl text-gray-400 font-medium">{category.title}</h3>
              <div className="flex flex-wrap gap-3">
                {category.skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: index * 0.05 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-2 px-4 py-2 bg-gray-800/50 border border-gray-700 rounded-full text-gray-300 hover:bg-gray-700/50 hover:border-gray-600 transition-all duration-300"
                  >
                    <div className={`w-3 h-3 rounded-full ${skill.color}`}></div>
                    <span className="text-sm font-medium">{skill.name}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
