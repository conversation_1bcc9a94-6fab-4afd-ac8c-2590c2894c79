'use client'

import { motion, useInView, useScroll, useTransform } from 'framer-motion'
import { ExternalLink, Github } from 'lucide-react'
import { useRef } from 'react'

const projects = [
  {
    id: "_01.",
    title: "MTI Electronics",
    description: "E-commerce platform built with Next.js and Payload CMS",
    technologies: ["Next.js", "Payload CMS", "Tailwind CSS"],
    image: "/api/placeholder/600/400",
    url: "/projects/mti-electronics"
  },
  {
    id: "_02.",
    title: "Epikcart",
    description: "Multi-language shopping cart application with Redux",
    technologies: ["React", "Redux", "React i18n"],
    image: "/api/placeholder/600/400",
    url: "/projects/epikcart"
  },
  {
    id: "_03.",
    title: "Resume Roaster",
    description: "AI-powered resume analysis tool using GPT-4",
    technologies: ["GPT-4", "Next.js", "PostgreSQL"],
    image: "/api/placeholder/600/400",
    url: "/projects/resume-roaster"
  },
  {
    id: "_04.",
    title: "Real Estate",
    description: "Property management platform with advanced search",
    technologies: ["React.js", "Redux", "Tailwind CSS"],
    image: "/api/placeholder/600/400",
    url: "/projects/property-pro"
  },
  {
    id: "_05.",
    title: "Consulting Finance",
    description: "Financial consulting website with modern design",
    technologies: ["HTML", "CSS & SCSS", "Javascript"],
    image: "/api/placeholder/600/400",
    url: "/projects/crenotive"
  },
  {
    id: "_06.",
    title: "devLinks",
    description: "Developer link sharing platform with drag & drop",
    technologies: ["Next.js", "Formik", "Drag & Drop"],
    image: "/api/placeholder/600/400",
    url: "/projects/devLinks"
  }
]

export default function Projects() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  })

  const y = useTransform(scrollYProgress, [0, 1], [100, -100])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3
      }
    }
  }

  const projectVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15,
        duration: 0.8
      }
    }
  }

  return (
    <section id="projects" className="py-20 px-6 relative overflow-hidden">
      {/* Background elements */}
      <motion.div
        style={{ y }}
        className="absolute inset-0 pointer-events-none"
      >
        <div className="absolute top-40 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl" />
        <div className="absolute bottom-40 right-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl" />
      </motion.div>

      <div className="container mx-auto max-w-7xl" ref={ref}>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16 reveal-text"
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
            transition={{ duration: 1, delay: 0.2 }}
          >
            SELECTED PROJECTS
          </motion.h2>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              variants={projectVariants}
              className="group cursor-pointer project-card stagger-item"
              onClick={() => window.open(project.url, '_blank')}
              whileHover={{
                y: -10,
                transition: { duration: 0.3 }
              }}
              whileTap={{ scale: 0.98 }}
            >
              <motion.div
                className="bg-gray-900/50 border border-gray-800 rounded-xl overflow-hidden hover:border-gray-700 transition-all duration-500 hover:bg-gray-800/50 h-full"
                whileHover={{
                  boxShadow: "0 20px 40px rgba(0,0,0,0.4)",
                  borderColor: "rgba(156, 163, 175, 0.3)"
                }}
              >
                {/* Project Image */}
                <div className="relative overflow-hidden h-48">
                  <motion.div
                    className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.4 }}
                  >
                    <span className="text-gray-500 text-lg">Project Image</span>
                  </motion.div>

                  {/* Hover overlay */}
                  <motion.div
                    className="absolute inset-0 bg-black/60 flex items-center justify-center"
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="flex space-x-4">
                      <motion.button
                        className="p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors backdrop-blur-sm"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <ExternalLink size={20} className="text-white" />
                      </motion.button>
                      <motion.button
                        className="p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors backdrop-blur-sm"
                        whileHover={{ scale: 1.1, rotate: -5 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Github size={20} className="text-white" />
                      </motion.button>
                    </div>
                  </motion.div>
                </div>

                {/* Project Info */}
                <div className="p-6">
                  <motion.div
                    className="text-gray-400 text-sm mb-2"
                    initial={{ opacity: 0, x: -20 }}
                    animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                  >
                    {project.id}
                  </motion.div>

                  <motion.h3
                    className="text-xl font-semibold text-white mb-3"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                  >
                    {project.title}
                  </motion.h3>

                  <motion.p
                    className="text-gray-400 mb-4 text-sm leading-relaxed"
                    initial={{ opacity: 0 }}
                    animate={isInView ? { opacity: 1 } : { opacity: 0 }}
                    transition={{ delay: 0.7 + index * 0.1 }}
                  >
                    {project.description}
                  </motion.p>

                  <motion.div
                    className="flex flex-wrap gap-2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                  >
                    {project.technologies.map((tech, techIndex) => (
                      <motion.span
                        key={tech}
                        className="px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                        transition={{ delay: 0.9 + index * 0.1 + techIndex * 0.05 }}
                        whileHover={{ scale: 1.05, backgroundColor: "rgba(75, 85, 99, 0.8)" }}
                      >
                        {tech}
                      </motion.span>
                    ))}
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
