# Modern Portfolio Website

A clean, modern, and responsive portfolio website built with Next.js, TypeScript, and Tailwind CSS.

## Features

- 🎨 Modern and clean design
- 📱 Fully responsive
- ⚡ Fast and optimized
- 🎭 Smooth animations with Framer Motion
- 🌙 Dark theme
- 📊 GitHub status integration
- 🎯 SEO optimized

## Tech Stack

- **Framework:** Next.js 14
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **Animations:** Framer Motion
- **Icons:** Lucide React
- **Deployment:** Vercel (recommended)

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Customization

### Personal Information
Update the following files with your information:

- `src/components/sections/Hero.tsx` - Your name, title, and description
- `src/components/sections/Skills.tsx` - Your skills and technologies
- `src/components/sections/Projects.tsx` - Your projects
- `src/components/sections/Footer.tsx` - Contact information
- `src/components/GitHubStatus.tsx` - Your GitHub username

### GitHub Integration
To enable the GitHub status widget:

1. Replace `'your-username'` in `src/components/GitHubStatus.tsx` with your actual GitHub username
2. The widget will automatically fetch your GitHub data and show your online status

### Styling
- Colors and themes can be customized in `tailwind.config.ts`
- Global styles are in `src/app/globals.css`

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with one click

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Performance

This portfolio is optimized for performance:
- Server-side rendering with Next.js
- Optimized images and fonts
- Minimal bundle size
- Fast loading times

## License

MIT License - feel free to use this template for your own portfolio!

## Support

If you have any questions or need help customizing the portfolio, feel free to open an issue or reach out!
