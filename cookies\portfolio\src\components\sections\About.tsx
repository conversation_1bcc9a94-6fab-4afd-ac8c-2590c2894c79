'use client'

import { motion } from 'framer-motion'

export default function About() {
  return (
    <section id="about" className="py-20 px-6">
      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
        {/* Left Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Hi, Amrit here
            </h2>
            <p className="text-gray-400 text-lg mb-6">
              20 year old something guy
            </p>
          </div>

          <div className="space-y-6">
            <div>
              <h3 className="text-white text-xl font-semibold mb-4">About</h3>
              <div className="space-y-4 text-gray-400">
                <p>tldr; learnt by hacking around on the internet.</p>
                <p>I like technology and deep science, they make a dent in the universe.</p>
                <p>I deeply study art, history, football and great books.</p>
              </div>
            </div>

            <div>
              <h3 className="text-white text-xl font-semibold mb-4">Cool places I worked at</h3>
              <div className="space-y-4">
                {/* Zero */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-white rounded border-2 border-gray-600 flex items-center justify-center">
                      <span className="text-black font-bold text-sm">Z</span>
                    </div>
                    <div>
                      <div className="text-white font-medium">Zero</div>
                      <div className="text-gray-400 text-sm">ICT | Software Engineer</div>
                    </div>
                  </div>
                  <div className="text-gray-400 text-sm">June 2025 - Present</div>
                </div>

                {/* Google Summer of Code */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-yellow-500 rounded flex items-center justify-center">
                      <span className="text-white font-bold text-sm">G</span>
                    </div>
                    <div>
                      <div className="text-white font-medium">Google Summer of Code</div>
                      <div className="text-gray-400 text-sm">Contributor under Deepmind.</div>
                    </div>
                  </div>
                  <div className="text-gray-400 text-sm">May 2025 - July 2025</div>
                </div>

                {/* Cal.com */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-700 rounded flex items-center justify-center">
                      <span className="text-white font-bold text-sm">C</span>
                    </div>
                    <div>
                      <div className="text-white font-medium">Cal.com</div>
                      <div className="text-gray-400 text-sm">Software Engineer Intern</div>
                    </div>
                  </div>
                  <div className="text-gray-400 text-sm">February 2025 - May 2025</div>
                </div>

                {/* Superteam */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-600 rounded flex items-center justify-center">
                      <span className="text-white font-bold text-sm">S</span>
                    </div>
                    <div>
                      <div className="text-white font-medium">Superteam, Solo</div>
                      <div className="text-gray-400 text-sm">Member | Grant In</div>
                    </div>
                  </div>
                  <div className="text-gray-400 text-sm">November 2024 - Present</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Right Content - Profile Image */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex justify-center lg:justify-end"
        >
          <div className="relative">
            <div className="w-80 h-96 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl border border-cyan-500/30 overflow-hidden">
              {/* Placeholder for profile image */}
              <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="w-16 h-16 bg-gray-600 rounded-full mx-auto mb-4"></div>
                  <p>Profile Image</p>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-2 -right-2 w-4 h-4 bg-cyan-500 rounded-full"></div>
            <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
