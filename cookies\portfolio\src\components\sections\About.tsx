'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'

export default function About() {
  return (
    <section id="about" className="py-20 px-6">
      <div className="container mx-auto max-w-6xl">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
              This is me.
            </h2>
            
            <div className="space-y-6 text-gray-400 text-lg leading-relaxed">
              <p>
                Hi, I'm <span className="text-white font-semibold">Your Name</span>.
              </p>

              <p>
                I'm a full stack web developer dedicated to turning ideas into creative solutions.
                I specialize in creating seamless and intuitive user experiences while building
                robust backend systems that power them.
              </p>

              <p>
                My approach focuses on creating scalable, high-performing solutions tailored to
                both user needs and business objectives. From frontend interfaces to backend APIs
                and databases, I strive to deliver complete solutions that not only engage users
                but also drive tangible results.
              </p>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative w-full h-[500px] rounded-2xl overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900">
              {/* Placeholder for profile image */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-32 h-32 rounded-full bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center">
                  <span className="text-4xl font-bold text-white">YN</span>
                </div>
              </div>
              
              {/* Overlay gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
