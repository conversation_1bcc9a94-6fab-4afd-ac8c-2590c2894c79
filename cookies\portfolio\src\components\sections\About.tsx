'use client'

import { motion } from 'framer-motion'

export default function About() {
  return (
    <section id="about" className="py-20 px-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            About Me
          </h2>
          <div className="max-w-2xl mx-auto space-y-6 text-gray-400 text-lg leading-relaxed">
            <p>
              Hi, I'm <span className="text-white">Your Name</span>, a full stack developer
              passionate about creating digital experiences that matter.
            </p>
            <p>
              I specialize in building modern web applications using React, Node.js, and other
              cutting-edge technologies. My focus is on writing clean, maintainable code and
              delivering solutions that solve real problems.
            </p>
            <p>
              When I'm not coding, you can find me exploring new technologies, contributing to
              open source projects, or sharing knowledge with the developer community.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
