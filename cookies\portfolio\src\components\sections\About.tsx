'use client'

import { motion, useInView, useScroll, useTransform } from 'framer-motion'
import { useRef } from 'react'

export default function About() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  })

  const x = useTransform(scrollYProgress, [0, 1], [-100, 100])
  const rotate = useTransform(scrollYProgress, [0, 1], [0, 360])

  return (
    <section id="about" className="py-20 px-6 relative overflow-hidden">
      {/* Background decoration */}
      <motion.div
        style={{ x, rotate }}
        className="absolute top-20 right-10 w-20 h-20 border border-gray-800 rounded-full opacity-20"
      />

      <div className="container mx-auto max-w-6xl" ref={ref}>
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="reveal-text"
          >
            <motion.h2
              className="text-5xl md:text-6xl font-bold text-white mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              This is me.
            </motion.h2>

            <div className="space-y-6 text-gray-400 text-lg leading-relaxed">
              {[
                "Hi, I'm Your Name.",
                "I'm a full stack web developer dedicated to turning ideas into creative solutions. I specialize in creating seamless and intuitive user experiences while building robust backend systems that power them.",
                "My approach focuses on creating scalable, high-performing solutions tailored to both user needs and business objectives. From frontend interfaces to backend APIs and databases, I strive to deliver complete solutions that not only engage users but also drive tangible results."
              ].map((text, index) => (
                <motion.p
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.2 }}
                  className={index === 0 ? "text-white font-semibold" : ""}
                >
                  {index === 0 ? (
                    <>
                      Hi, I'm <span className="text-white font-semibold">Your Name</span>.
                    </>
                  ) : (
                    text
                  )}
                </motion.p>
              ))}
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 50, scale: 0.9 }}
            animate={isInView ? { opacity: 1, x: 0, scale: 1 } : { opacity: 0, x: 50, scale: 0.9 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="relative"
          >
            <motion.div
              className="relative w-full h-[500px] rounded-2xl overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              {/* Placeholder for profile image */}
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={isInView ? { scale: 1 } : { scale: 0 }}
                transition={{ duration: 0.8, delay: 0.6, type: "spring", stiffness: 100 }}
              >
                <motion.div
                  className="w-32 h-32 rounded-full bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center"
                  whileHover={{
                    scale: 1.1,
                    rotate: 360,
                    transition: { duration: 0.6 }
                  }}
                >
                  <span className="text-4xl font-bold text-white">YN</span>
                </motion.div>
              </motion.div>

              {/* Overlay gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

              {/* Floating elements */}
              <motion.div
                className="absolute top-10 left-10 w-4 h-4 bg-blue-500/30 rounded-full"
                animate={{
                  y: [0, -20, 0],
                  opacity: [0.3, 0.8, 0.3]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <motion.div
                className="absolute bottom-20 right-10 w-3 h-3 bg-purple-500/40 rounded-full"
                animate={{
                  y: [0, -15, 0],
                  opacity: [0.4, 0.9, 0.4]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
