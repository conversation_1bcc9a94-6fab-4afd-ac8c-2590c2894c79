{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-icons": "^1.3.0", "@studio-freight/lenis": "^1.0.42", "@tabler/icons-react": "^3.34.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "gsap": "^3.13.0", "lenis": "^1.3.8", "lucide-react": "^0.294.0", "mini-svg-data-uri": "^1.4.4", "next": "14.0.4", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}