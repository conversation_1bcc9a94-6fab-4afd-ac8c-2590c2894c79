'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Github, Activity } from 'lucide-react'

interface GitHubData {
  username: string
  avatar_url: string
  public_repos: number
  followers: number
  following: number
  lastCommitDate?: string
  isOnline: boolean
}

export default function GitHubStatus() {
  const [githubData, setGithubData] = useState<GitHubData | null>(null)
  const [loading, setLoading] = useState(true)

  // Replace with your GitHub username
  const GITHUB_USERNAME = 'your-username'

  useEffect(() => {
    const fetchGitHubData = async () => {
      try {
        // Fetch basic user data
        const userResponse = await fetch(`https://api.github.com/users/${GITHUB_USERNAME}`)
        const userData = await userResponse.json()

        // Fetch recent activity to determine if user is "online"
        const eventsResponse = await fetch(`https://api.github.com/users/${GITHUB_USERNAME}/events`)
        const eventsData = await eventsResponse.json()

        // Check if there's recent activity (within last 24 hours)
        const now = new Date()
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        
        let isOnline = false
        let lastCommitDate = null

        if (eventsData && eventsData.length > 0) {
          const recentEvent = eventsData[0]
          const eventDate = new Date(recentEvent.created_at)
          lastCommitDate = eventDate.toISOString()
          isOnline = eventDate > oneDayAgo
        }

        setGithubData({
          username: userData.login,
          avatar_url: userData.avatar_url,
          public_repos: userData.public_repos,
          followers: userData.followers,
          following: userData.following,
          lastCommitDate,
          isOnline
        })
      } catch (error) {
        console.error('Error fetching GitHub data:', error)
        // Fallback data for demo purposes
        setGithubData({
          username: GITHUB_USERNAME,
          avatar_url: '/api/placeholder/40/40',
          public_repos: 25,
          followers: 150,
          following: 75,
          isOnline: true
        })
      } finally {
        setLoading(false)
      }
    }

    fetchGitHubData()
    
    // Refresh data every 5 minutes
    const interval = setInterval(fetchGitHubData, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <div className="glass-effect rounded-lg p-4 animate-pulse">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-600 rounded-full"></div>
            <div className="space-y-2">
              <div className="w-20 h-3 bg-gray-600 rounded"></div>
              <div className="w-16 h-2 bg-gray-600 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!githubData) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 100 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 1 }}
      className="fixed bottom-6 right-6 z-50"
    >
      <div className="glass-effect rounded-lg p-4 shadow-lg hover:shadow-xl transition-shadow">
        <div className="flex items-center space-x-3">
          {/* Avatar */}
          <div className="relative">
            <img
              src={githubData.avatar_url}
              alt={githubData.username}
              className="w-10 h-10 rounded-full border-2 border-white/20"
              onError={(e) => {
                e.currentTarget.src = `https://github.com/${githubData.username}.png`
              }}
            />
            {/* Online Status Indicator */}
            <div className="absolute -bottom-1 -right-1">
              <div className={`w-4 h-4 rounded-full border-2 border-white ${
                githubData.isOnline ? 'bg-green-500' : 'bg-gray-500'
              }`}>
                {githubData.isOnline && (
                  <motion.div
                    className="w-full h-full bg-green-400 rounded-full"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                )}
              </div>
            </div>
          </div>

          {/* GitHub Info */}
          <div className="text-white">
            <div className="flex items-center space-x-2">
              <Github size={16} />
              <span className="font-medium text-sm">{githubData.username}</span>
              {githubData.isOnline && (
                <motion.div
                  animate={{ opacity: [1, 0.5, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="flex items-center space-x-1"
                >
                  <Activity size={12} className="text-green-400" />
                  <span className="text-xs text-green-400">Active</span>
                </motion.div>
              )}
            </div>
            <div className="text-xs text-gray-300 mt-1">
              {githubData.public_repos} repos • {githubData.followers} followers
            </div>
          </div>
        </div>

        {/* Hover Details */}
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          whileHover={{ opacity: 1, height: 'auto' }}
          className="overflow-hidden"
        >
          <div className="mt-3 pt-3 border-t border-white/20 text-xs text-gray-300">
            <div className="flex justify-between">
              <span>Following: {githubData.following}</span>
              <span className={githubData.isOnline ? 'text-green-400' : 'text-gray-400'}>
                {githubData.isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
            {githubData.lastCommitDate && (
              <div className="mt-1 text-xs">
                Last activity: {new Date(githubData.lastCommitDate).toLocaleDateString()}
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </motion.div>
  )
}
