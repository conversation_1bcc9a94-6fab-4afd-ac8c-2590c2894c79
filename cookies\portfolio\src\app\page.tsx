'use client'

import { useEffect } from 'react'
import Hero from '@/components/sections/Hero'
import About from '@/components/sections/About'
import Skills from '@/components/sections/Skills'
import Experience from '@/components/sections/Experience'
import Projects from '@/components/sections/Projects'
import Contact from '@/components/sections/Contact'

export default function Home() {
  useEffect(() => {
    // Smooth scrolling for anchor links
    const handleSmoothScroll = (e: Event) => {
      const target = e.target as HTMLAnchorElement
      if (target.hash) {
        e.preventDefault()
        const element = document.querySelector(target.hash)
        element?.scrollIntoView({ behavior: 'smooth' })
      }
    }

    document.addEventListener('click', handleSmoothScroll)
    return () => document.removeEventListener('click', handleSmoothScroll)
  }, [])

  return (
    <main className="relative">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-md border-b border-gray-800">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="text-white font-bold text-xl">
              <span className="gradient-text">PORTFOLIO</span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#" className="text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide">
                Home
              </a>
              <a href="#about" className="text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide">
                About Me
              </a>
              <a href="#experience" className="text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide">
                Experience
              </a>
              <a href="#projects" className="text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide">
                Projects
              </a>
            </div>
            <div className="hidden md:block">
              <a
                href="mailto:<EMAIL>"
                className="text-gray-300 hover:text-white transition-colors text-sm uppercase tracking-wide"
              >
                GET IN TOUCH
              </a>
            </div>
            <div className="md:hidden">
              <button className="text-white">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <Hero />
      <About />
      <Skills />
      <Experience />
      <Projects />
      <Contact />
    </main>
  )
}
