'use client'

import { useEffect } from 'react'
import Hero from '@/components/sections/Hero'
import Skills from '@/components/sections/Skills'
import Projects from '@/components/sections/Projects'
import Footer from '@/components/sections/Footer'
import GitHubStatus from '@/components/GitHubStatus'

export default function Home() {
  useEffect(() => {
    // Smooth scrolling for anchor links
    const handleSmoothScroll = (e: Event) => {
      const target = e.target as HTMLAnchorElement
      if (target.hash) {
        e.preventDefault()
        const element = document.querySelector(target.hash)
        element?.scrollIntoView({ behavior: 'smooth' })
      }
    }

    document.addEventListener('click', handleSmoothScroll)
    return () => document.removeEventListener('click', handleSmoothScroll)
  }, [])

  return (
    <main className="relative">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="text-white font-bold text-xl">
              <span className="gradient-text">Portfolio</span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#" className="text-white hover:text-purple-300 transition-colors">
                Home
              </a>
              <a href="#skills" className="text-white hover:text-purple-300 transition-colors">
                Skills
              </a>
              <a href="#projects" className="text-white hover:text-purple-300 transition-colors">
                Projects
              </a>
              <a href="#contact" className="text-white hover:text-purple-300 transition-colors">
                Contact
              </a>
            </div>
            <div className="md:hidden">
              <button className="text-white">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <Hero />
      <Skills />
      <Projects />
      <Footer />

      {/* GitHub Status Widget */}
      <GitHubStatus />

      {/* Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl" />
      </div>
    </main>
  )
}
