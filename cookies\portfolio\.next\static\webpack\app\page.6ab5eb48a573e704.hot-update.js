"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/Skills.tsx":
/*!********************************************!*\
  !*** ./src/components/sections/Skills.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Skills; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst skillCategories = [\n    {\n        title: \"Languages & Frameworks\",\n        skills: [\n            {\n                name: \"JavaScript\",\n                color: \"bg-yellow-500\"\n            },\n            {\n                name: \"TypeScript\",\n                color: \"bg-blue-500\"\n            },\n            {\n                name: \"React.js\",\n                color: \"bg-cyan-500\"\n            },\n            {\n                name: \"Next.js\",\n                color: \"bg-gray-700\"\n            },\n            {\n                name: \"TailwindCSS\",\n                color: \"bg-teal-500\"\n            },\n            {\n                name: \"Bootstrap\",\n                color: \"bg-purple-600\"\n            },\n            {\n                name: \"C++\",\n                color: \"bg-blue-600\"\n            },\n            {\n                name: \"CSS\",\n                color: \"bg-blue-400\"\n            },\n            {\n                name: \"C\",\n                color: \"bg-gray-600\"\n            }\n        ]\n    },\n    {\n        title: \"Backend & Databases\",\n        skills: [\n            {\n                name: \"Node.js\",\n                color: \"bg-green-600\"\n            },\n            {\n                name: \"PostgreSQL\",\n                color: \"bg-blue-700\"\n            },\n            {\n                name: \"Prisma\",\n                color: \"bg-gray-800\"\n            },\n            {\n                name: \"Firebase\",\n                color: \"bg-orange-500\"\n            },\n            {\n                name: \"Artificial Intelligence\",\n                color: \"bg-red-500\"\n            },\n            {\n                name: \"Nginx\",\n                color: \"bg-green-700\"\n            },\n            {\n                name: \"Express\",\n                color: \"bg-gray-700\"\n            }\n        ]\n    }\n];\nfunction Skills() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        className: \"py-20 px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mb-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                        children: \"Tools that I have used\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-12\",\n                    children: skillCategories.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: categoryIndex * 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl text-gray-400 font-medium\",\n                                    children: category.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-3\",\n                                    children: category.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                delay: index * 0.05\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"flex items-center space-x-2 px-4 py-2 bg-gray-800/50 border border-gray-700 rounded-full text-gray-300 hover:bg-gray-700/50 hover:border-gray-600 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full \".concat(skill.color)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: skill.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, skill.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, category.title, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_c = Skills;\nvar _c;\n$RefreshReg$(_c, \"Skills\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Skills.tsx\n"));

/***/ })

});