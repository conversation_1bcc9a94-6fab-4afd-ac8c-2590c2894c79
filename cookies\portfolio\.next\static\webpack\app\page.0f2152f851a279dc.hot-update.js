"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/Skills.tsx":
/*!********************************************!*\
  !*** ./src/components/sections/Skills.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Skills; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst skills = [\n    \"JavaScript\",\n    \"TypeScript\",\n    \"React\",\n    \"Next.js\",\n    \"Node.js\",\n    \"Express\",\n    \"PostgreSQL\",\n    \"MongoDB\",\n    \"Tailwind CSS\",\n    \"Git\",\n    \"Docker\",\n    \"AWS\",\n    \"Python\",\n    \"GraphQL\",\n    \"Redux\"\n];\nfunction Skills() {\n    _s();\n    const ref = useRef(null);\n    const isInView = useInView(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.2\n            }\n        }\n    };\n    const categoryVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.8,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const skillVariants = {\n        hidden: {\n            opacity: 0,\n            scale: 0.8,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            scale: 1,\n            y: 0,\n            transition: {\n                type: \"spring\",\n                stiffness: 100,\n                damping: 12\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        className: \"py-20 px-6 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 right-10 w-32 h-32 bg-blue-500/5 rounded-full blur-xl parallax-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-10 w-48 h-48 bg-purple-500/5 rounded-full blur-xl parallax-fast\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-6xl\",\n                ref: ref,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: isInView ? {\n                            opacity: 1,\n                            y: 0\n                        } : {\n                            opacity: 0,\n                            y: 50\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16 reveal-text\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h2, {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                scale: 1\n                            } : {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            transition: {\n                                duration: 1,\n                                delay: 0.2\n                            },\n                            children: \"My Stack\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        className: \"space-y-12\",\n                        children: skillCategories.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                variants: categoryVariants,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h3, {\n                                        className: \"text-xl font-semibold text-white lowercase reveal-text\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -30\n                                        },\n                                        animate: isInView ? {\n                                            opacity: 1,\n                                            x: 0\n                                        } : {\n                                            opacity: 0,\n                                            x: -30\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: categoryIndex * 0.1\n                                        },\n                                        children: category.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\",\n                                        children: category.skills.map((skill, skillIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                variants: skillVariants,\n                                                initial: \"hidden\",\n                                                animate: isInView ? \"visible\" : \"hidden\",\n                                                transition: {\n                                                    delay: categoryIndex * 0.1 + skillIndex * 0.05\n                                                },\n                                                className: \"group stagger-item\",\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    rotate: [\n                                                        0,\n                                                        -1,\n                                                        1,\n                                                        0\n                                                    ],\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"bg-gray-900/50 border border-gray-800 rounded-xl p-6 hover:border-gray-700 transition-all duration-300 hover:bg-gray-800/50 cursor-pointer relative overflow-hidden\",\n                                                    whileHover: {\n                                                        boxShadow: \"0 10px 30px rgba(0,0,0,0.3)\",\n                                                        borderColor: \"rgba(156, 163, 175, 0.5)\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0\",\n                                                            whileHover: {\n                                                                opacity: 1\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center space-y-3 relative z-10\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                                    className: \"text-3xl\",\n                                                                    whileHover: {\n                                                                        scale: 1.2,\n                                                                        rotate: 360,\n                                                                        transition: {\n                                                                            duration: 0.6\n                                                                        }\n                                                                    },\n                                                                    children: skill.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-sm font-medium text-center\",\n                                                                    children: skill.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, skill.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, category.title, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Skills.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(Skills, \"DljcBprJKYjULUac3YKdUV9OwZQ=\", true);\n_c = Skills;\nvar _c;\n$RefreshReg$(_c, \"Skills\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Skills.tsx\n"));

/***/ })

});