{"c": ["app/page", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs"]}