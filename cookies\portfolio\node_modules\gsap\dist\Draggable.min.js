/*!
 * Draggable 3.13.0
 * https://gsap.com
 * 
 * @license Copyright 2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license.
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function w(e,t){if(e.parentNode&&(h||M(e))){var n=P(e),o=n?n.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",r=n?t?"rect":"g":"div",i=2!==t?0:100,a=3===t?100:0,l="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",s=h.createElementNS?h.createElementNS(o.replace(/^https/,"http"),r):h.createElement(r);return t&&(n?(x=x||w(e),s.setAttribute("width",.01),s.setAttribute("height",.01),s.setAttribute("transform","translate("+i+","+a+")"),x.appendChild(s)):(g||((g=w(e)).style.cssText=l),s.style.cssText=l+"width:0.1px;height:0.1px;top:"+a+"px;left:"+i+"px",g.appendChild(s))),s}throw"Need document and parent."}function A(e,t,n,o,r,i,a){return e.a=t,e.b=n,e.c=o,e.d=r,e.e=i,e.f=a,e}var h,f,i,a,g,x,m,v,y,t,b="transform",T=b+"Origin",M=function _setDoc(e){var t=e.ownerDocument||e;!(b in e.style)&&"msTransform"in e.style&&(T=(b="msTransform")+"Origin");for(;t.parentNode&&(t=t.parentNode););if(f=window,m=new ge,t){i=(h=t).documentElement,a=t.body,(v=h.createElementNS("http://www.w3.org/2000/svg","g")).style.transform="none";var n=t.createElement("div"),o=t.createElement("div"),r=t&&(t.body||t.firstElementChild);r&&r.appendChild&&(r.appendChild(n),n.appendChild(o),n.setAttribute("style","position:static;transform:translate3d(0,0,1px)"),y=o.offsetParent!==n,r.removeChild(n))}return t},E=function _forceNonZeroScale(e){for(var t,n;e&&e!==a;)(n=e._gsap)&&n.uncache&&n.get(e,"x"),n&&!n.scaleX&&!n.scaleY&&n.renderTransform&&(n.scaleX=n.scaleY=1e-4,n.renderTransform(1,n),t?t.push(n):t=[n]),e=e.parentNode;return t},D=[],S=[],L=function _getDocScrollTop(){return f.pageYOffset||h.scrollTop||i.scrollTop||a.scrollTop||0},C=function _getDocScrollLeft(){return f.pageXOffset||h.scrollLeft||i.scrollLeft||a.scrollLeft||0},P=function _svgOwner(e){return e.ownerSVGElement||("svg"===(e.tagName+"").toLowerCase()?e:null)},N=function _isFixed(e){return"fixed"===f.getComputedStyle(e).position||((e=e.parentNode)&&1===e.nodeType?_isFixed(e):void 0)},k=function _placeSiblings(e,t){var n,o,r,i,a,l,s=P(e),c=e===s,d=s?D:S,p=e.parentNode,u=p&&!s&&p.shadowRoot&&p.shadowRoot.appendChild?p.shadowRoot:p;if(e===f)return e;if(d.length||d.push(w(e,1),w(e,2),w(e,3)),n=s?x:g,s)c?(i=-(r=function _getCTM(e){var t,n=e.getCTM();return n||(t=e.style[b],e.style[b]="none",e.appendChild(v),n=v.getCTM(),e.removeChild(v),t?e.style[b]=t:e.style.removeProperty(b.replace(/([A-Z])/g,"-$1").toLowerCase())),n||m.clone()}(e)).e/r.a,a=-r.f/r.d,o=m):e.getBBox?(r=e.getBBox(),i=(o=(o=e.transform?e.transform.baseVal:{}).numberOfItems?1<o.numberOfItems?function _consolidate(e){for(var t=new ge,n=0;n<e.numberOfItems;n++)t.multiply(e.getItem(n).matrix);return t}(o):o.getItem(0).matrix:m).a*r.x+o.c*r.y,a=o.b*r.x+o.d*r.y):(o=new ge,i=a=0),t&&"g"===e.tagName.toLowerCase()&&(i=a=0),(c?s:p).appendChild(n),n.setAttribute("transform","matrix("+o.a+","+o.b+","+o.c+","+o.d+","+(o.e+i)+","+(o.f+a)+")");else{if(i=a=0,y)for(o=e.offsetParent,r=e;(r=r&&r.parentNode)&&r!==o&&r.parentNode;)4<(f.getComputedStyle(r)[b]+"").length&&(i=r.offsetLeft,a=r.offsetTop,r=0);if("absolute"!==(l=f.getComputedStyle(e)).position&&"fixed"!==l.position)for(o=e.offsetParent;p&&p!==o;)i+=p.scrollLeft||0,a+=p.scrollTop||0,p=p.parentNode;(r=n.style).top=e.offsetTop-a+"px",r.left=e.offsetLeft-i+"px",r[b]=l[b],r[T]=l[T],r.position="fixed"===l.position?"fixed":"absolute",u.appendChild(n)}return n},ge=((t=Matrix2D.prototype).inverse=function inverse(){var e=this.a,t=this.b,n=this.c,o=this.d,r=this.e,i=this.f,a=e*o-t*n||1e-10;return A(this,o/a,-t/a,-n/a,e/a,(n*i-o*r)/a,-(e*i-t*r)/a)},t.multiply=function multiply(e){var t=this.a,n=this.b,o=this.c,r=this.d,i=this.e,a=this.f,l=e.a,s=e.c,c=e.b,d=e.d,p=e.e,u=e.f;return A(this,l*t+c*o,l*n+c*r,s*t+d*o,s*n+d*r,i+p*t+u*o,a+p*n+u*r)},t.clone=function clone(){return new Matrix2D(this.a,this.b,this.c,this.d,this.e,this.f)},t.equals=function equals(e){var t=this.a,n=this.b,o=this.c,r=this.d,i=this.e,a=this.f;return t===e.a&&n===e.b&&o===e.c&&r===e.d&&i===e.e&&a===e.f},t.apply=function apply(e,t){void 0===t&&(t={});var n=e.x,o=e.y,r=this.a,i=this.b,a=this.c,l=this.d,s=this.e,c=this.f;return t.x=n*r+o*a+s||0,t.y=n*i+o*l+c||0,t},Matrix2D);function Matrix2D(e,t,n,o,r,i){void 0===e&&(e=1),void 0===t&&(t=0),void 0===n&&(n=0),void 0===o&&(o=1),void 0===r&&(r=0),void 0===i&&(i=0),A(this,e,t,n,o,r,i)}function getGlobalMatrix(e,t,n,o){if(!e||!e.parentNode||(h||M(e)).documentElement===e)return new ge;var r=E(e),i=P(e)?D:S,a=k(e,n),l=i[0].getBoundingClientRect(),s=i[1].getBoundingClientRect(),c=i[2].getBoundingClientRect(),d=a.parentNode,p=!o&&N(e),u=new ge((s.left-l.left)/100,(s.top-l.top)/100,(c.left-l.left)/100,(c.top-l.top)/100,l.left+(p?0:C()),l.top+(p?0:L()));if(d.removeChild(a),r)for(l=r.length;l--;)(s=r[l]).scaleX=s.scaleY=0,s.renderTransform(1,s);return t?u.inverse():u}function X(){return"undefined"!=typeof window}function Y(){return xe||X()&&(xe=window.gsap)&&xe.registerPlugin&&xe}function Z(e){return"function"==typeof e}function $(e){return"object"==typeof e}function _(e){return void 0===e}function aa(){return!1}function da(e){return Math.round(1e4*e)/1e4}function fa(e,t){var n=ve.createElementNS?ve.createElementNS((t||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):ve.createElement(e);return n.style?n:ve.createElement(e)}function ra(e,t){var n,o={};for(n in e)o[n]=t?e[n]*t:e[n];return o}function ta(e,t){for(var n,o=e.length;o--;)t?e[o].style.touchAction=t:e[o].style.removeProperty("touch-action"),(n=e[o].children)&&n.length&&ta(n,t)}function ua(){return Oe.forEach(function(e){return e()})}function wa(){return!Oe.length&&xe.ticker.remove(ua)}function xa(e){for(var t=Oe.length;t--;)Oe[t]===e&&Oe.splice(t,1);xe.to(wa,{overwrite:!0,delay:15,duration:0,onComplete:wa,data:"_draggable"})}function za(e,t,n,o){if(e.addEventListener){var r=Ee[t];o=o||(d?{passive:!1}:null),e.addEventListener(r||t,n,o),r&&t!==r&&e.addEventListener(t,n,o)}}function Aa(e,t,n,o){if(e.removeEventListener){var r=Ee[t];e.removeEventListener(r||t,n,o),r&&t!==r&&e.removeEventListener(t,n,o)}}function Ba(e){e.preventDefault&&e.preventDefault(),e.preventManipulation&&e.preventManipulation()}function Da(e){De=e.touches&&Pe<e.touches.length,Aa(e.target,"touchend",Da)}function Ea(e){De=e.touches&&Pe<e.touches.length,za(e.target,"touchend",Da)}function Fa(e){return me.pageYOffset||e.scrollTop||e.documentElement.scrollTop||e.body.scrollTop||0}function Ga(e){return me.pageXOffset||e.scrollLeft||e.documentElement.scrollLeft||e.body.scrollLeft||0}function Ha(e,t){za(e,"scroll",t),Qe(e.parentNode)||Ha(e.parentNode,t)}function Ia(e,t){Aa(e,"scroll",t),Qe(e.parentNode)||Ia(e.parentNode,t)}function Ka(e,t){var n="x"===t?"Width":"Height",o="scroll"+n,r="client"+n;return Math.max(0,Qe(e)?Math.max(ye[o],l[o])-(me["inner"+n]||ye[r]||l[r]):e[o]-e[r])}function La(e,t){var n=Ka(e,"x"),o=Ka(e,"y");Qe(e)?e=We:La(e.parentNode,t),e._gsMaxScrollX=n,e._gsMaxScrollY=o,t||(e._gsScrollX=e.scrollLeft||0,e._gsScrollY=e.scrollTop||0)}function Ma(e,t,n){var o=e.style;o&&(_(o[t])&&(t=c(t,e)||t),null==n?o.removeProperty&&o.removeProperty(t.replace(/([A-Z])/g,"-$1").toLowerCase()):o[t]=n)}function Na(e){return me.getComputedStyle(e instanceof Element?e:e.host||(e.parentNode||{}).host||e)}function Pa(e){if(e===me)return p.left=p.top=0,p.width=p.right=ye.clientWidth||e.innerWidth||l.clientWidth||0,p.height=p.bottom=(e.innerHeight||0)-20<ye.clientHeight?ye.clientHeight:e.innerHeight||l.clientHeight||0,p;var t=e.ownerDocument||ve,n=_(e.pageX)?e.nodeType||_(e.left)||_(e.top)?Te(e)[0].getBoundingClientRect():e:{left:e.pageX-Ga(t),top:e.pageY-Fa(t),right:e.pageX-Ga(t)+1,bottom:e.pageY-Fa(t)+1};return _(n.right)&&!_(n.width)?(n.right=n.left+n.width,n.bottom=n.top+n.height):_(n.width)&&(n={width:n.right-n.left,height:n.bottom-n.top,right:n.right,left:n.left,bottom:n.bottom,top:n.top}),n}function Qa(e,t,n){var o,r=e.vars,i=r[n],a=e._listeners[t];return Z(i)&&(o=i.apply(r.callbackScope||e,r[n+"Params"]||[e.pointerEvent])),a&&!1===e.dispatchEvent(t)&&(o=!1),o}function Ra(e,t){var n,o,r,i=Te(e)[0];return i.nodeType||i===me?u(i,t):_(e.left)?{left:o=e.min||e.minX||e.minRotation||0,top:n=e.min||e.minY||0,width:(e.max||e.maxX||e.maxRotation||0)-o,height:(e.max||e.maxY||0)-n}:(r={x:0,y:0},{left:e.left-r.x,top:e.top-r.y,width:e.width,height:e.height})}function Ua(r,i,e,t,a,n){var o,l,s,c={};if(i)if(1!==a&&i instanceof Array){if(c.end=o=[],s=i.length,$(i[0]))for(l=0;l<s;l++)o[l]=ra(i[l],a);else for(l=0;l<s;l++)o[l]=i[l]*a;e+=1.1,t-=1.1}else Z(i)?c.end=function(e){var t,n,o=i.call(r,e);if(1!==a)if($(o)){for(n in t={},o)t[n]=o[n]*a;o=t}else o*=a;return o}:c.end=i;return!e&&0!==e||(c.max=e),!t&&0!==t||(c.min=t),n&&(c.velocity=0),c}function Va(e){var t;return!(!e||!e.getAttribute||e===l)&&(!("true"!==(t=e.getAttribute("data-clickable"))&&("false"===t||!o.test(e.nodeName+"")&&"true"!==e.getAttribute("contentEditable")))||Va(e.parentNode))}function Wa(e,t){for(var n,o=e.length;o--;)(n=e[o]).ondragstart=n.onselectstart=t?null:aa,xe.set(n,{lazy:!0,userSelect:t?"text":"none"})}function $a(i,r){i=xe.utils.toArray(i)[0],r=r||{};var a,l,s,e,c,d,p=document.createElement("div"),u=p.style,t=i.firstChild,h=0,f=0,g=i.scrollTop,x=i.scrollLeft,m=i.scrollWidth,v=i.scrollHeight,y=0,w=0,b=0;B&&!1!==r.force3D?(c="translate3d(",d="px,0px)"):O&&(c="translate(",d="px)"),this.scrollTop=function(e,t){if(!arguments.length)return-this.top();this.top(-e,t)},this.scrollLeft=function(e,t){if(!arguments.length)return-this.left();this.left(-e,t)},this.left=function(e,t){if(!arguments.length)return-(i.scrollLeft+f);var n=i.scrollLeft-x,o=f;if((2<n||n<-2)&&!t)return x=i.scrollLeft,xe.killTweensOf(this,{left:1,scrollLeft:1}),this.left(-x),void(r.onKill&&r.onKill());(e=-e)<0?(f=e-.5|0,e=0):w<e?(f=e-w|0,e=w):f=0,(f||o)&&(this._skip||(u[O]=c+-f+"px,"+-h+d),0<=f+y&&(u.paddingRight=f+y+"px")),i.scrollLeft=0|e,x=i.scrollLeft},this.top=function(e,t){if(!arguments.length)return-(i.scrollTop+h);var n=i.scrollTop-g,o=h;if((2<n||n<-2)&&!t)return g=i.scrollTop,xe.killTweensOf(this,{top:1,scrollTop:1}),this.top(-g),void(r.onKill&&r.onKill());(e=-e)<0?(h=e-.5|0,e=0):b<e?(h=e-b|0,e=b):h=0,(h||o)&&(this._skip||(u[O]=c+-f+"px,"+-h+d)),i.scrollTop=0|e,g=i.scrollTop},this.maxScrollTop=function(){return b},this.maxScrollLeft=function(){return w},this.disable=function(){for(t=p.firstChild;t;)e=t.nextSibling,i.appendChild(t),t=e;i===p.parentNode&&i.removeChild(p)},this.enable=function(){if((t=i.firstChild)!==p){for(;t;)e=t.nextSibling,p.appendChild(t),t=e;i.appendChild(p),this.calibrate()}},this.calibrate=function(e){var t,n,o,r=i.clientWidth===a;g=i.scrollTop,x=i.scrollLeft,r&&i.clientHeight===l&&p.offsetHeight===s&&m===i.scrollWidth&&v===i.scrollHeight&&!e||((h||f)&&(n=this.left(),o=this.top(),this.left(-i.scrollLeft),this.top(-i.scrollTop)),t=Na(i),r&&!e||(u.display="block",u.width="auto",u.paddingRight="0px",(y=Math.max(0,i.scrollWidth-i.clientWidth))&&(y+=parseFloat(t.paddingLeft)+(R?parseFloat(t.paddingRight):0))),u.display="inline-block",u.position="relative",u.overflow="visible",u.verticalAlign="top",u.boxSizing="content-box",u.width="100%",u.paddingRight=y+"px",R&&(u.paddingBottom=t.paddingBottom),a=i.clientWidth,l=i.clientHeight,m=i.scrollWidth,v=i.scrollHeight,w=i.scrollWidth-a,b=i.scrollHeight-l,s=p.offsetHeight,u.display="block",(n||o)&&(this.left(n),this.top(o)))},this.content=p,this.element=i,this._skip=!1,this.enable()}function _a(e){if(X()&&document.body){var t=window&&window.navigator;me=window,ve=document,ye=ve.documentElement,l=ve.body,s=fa("div"),Ae=!!window.PointerEvent,(we=fa("div")).style.cssText="visibility:hidden;height:1px;top:-1px;pointer-events:none;position:relative;clear:both;cursor:grab",Xe="grab"===we.style.cursor?"grab":"move",Se=t&&-1!==t.userAgent.toLowerCase().indexOf("android"),Me="ontouchstart"in ye&&"orientation"in me||t&&(0<t.MaxTouchPoints||0<t.msMaxTouchPoints),o=fa("div"),r=fa("div"),i=r.style,a=l,i.display="inline-block",i.position="relative",o.style.cssText="width:90px;height:40px;padding:10px;overflow:auto;visibility:hidden",o.appendChild(r),a.appendChild(o),n=r.offsetHeight+18>o.scrollHeight,a.removeChild(o),R=n,Ee=function(e){for(var t=e.split(","),n=(("onpointerdown"in s?"pointerdown,pointermove,pointerup,pointercancel":"onmspointerdown"in s?"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel":e).split(",")),o={},r=4;-1<--r;)o[t[r]]=n[r],o[n[r]]=t[r];try{ye.addEventListener("test",null,Object.defineProperty({},"passive",{get:function get(){d=1}}))}catch(e){}return o}("touchstart,touchmove,touchend,touchcancel"),za(ve,"touchcancel",aa),za(me,"touchmove",aa),l&&l.addEventListener("touchstart",aa),za(ve,"contextmenu",function(){for(var e in Ie)Ie[e].isPressed&&Ie[e].endDrag()}),xe=be=Y()}var n,o,r,i,a;xe?(_e=xe.plugins.inertia,Le=xe.core.context||function(){},c=xe.utils.checkPrefix,O=c(O),Ye=c(Ye),Te=xe.utils.toArray,Ce=xe.core.getStyleSaver,B=!!c("perspective")):e&&console.warn("Please gsap.registerPlugin(Draggable)")}var xe,me,ve,ye,l,s,we,be,c,Te,d,Me,Ee,De,Se,_e,Xe,Ae,Le,Ce,B,R,n,Pe=0,O="transform",Ye="transformOrigin",Ne=Array.isArray,ke=180/Math.PI,Be=1e20,r=new ge,Re=Date.now||function(){return(new Date).getTime()},Oe=[],Ie={},Fe=0,o=/^(?:a|input|textarea|button|select)$/i,ze=0,He={},We={},Qe=function _isRoot(e){return!(e&&e!==ye&&9!==e.nodeType&&e!==ve.body&&e!==me&&e.nodeType&&e.parentNode)},p={},Ge={},u=function _getElementBounds(e,t){t=Te(t)[0];var n,o,r,i,a,l,s,c,d,p,u,h,f,g=e.getBBox&&e.ownerSVGElement,x=e.ownerDocument||ve;if(e===me)r=Fa(x),o=(n=Ga(x))+(x.documentElement.clientWidth||e.innerWidth||x.body.clientWidth||0),i=r+((e.innerHeight||0)-20<x.documentElement.clientHeight?x.documentElement.clientHeight:e.innerHeight||x.body.clientHeight||0);else{if(t===me||_(t))return e.getBoundingClientRect();n=r=0,g?(u=(p=e.getBBox()).width,h=p.height):(e.viewBox&&(p=e.viewBox.baseVal)&&(n=p.x||0,r=p.y||0,u=p.width,h=p.height),u||(p="border-box"===(f=Na(e)).boxSizing,u=(parseFloat(f.width)||e.clientWidth||0)+(p?0:parseFloat(f.borderLeftWidth)+parseFloat(f.borderRightWidth)),h=(parseFloat(f.height)||e.clientHeight||0)+(p?0:parseFloat(f.borderTopWidth)+parseFloat(f.borderBottomWidth)))),o=u,i=h}return e===t?{left:n,top:r,width:o-n,height:i-r}:(l=(a=getGlobalMatrix(t,!0).multiply(getGlobalMatrix(e))).apply({x:n,y:r}),s=a.apply({x:o,y:r}),c=a.apply({x:o,y:i}),d=a.apply({x:n,y:i}),{left:n=Math.min(l.x,s.x,c.x,d.x),top:r=Math.min(l.y,s.y,c.y,d.y),width:Math.max(l.x,s.x,c.x,d.x)-n,height:Math.max(l.y,s.y,c.y,d.y)-r})},I=((n=EventDispatcher.prototype).addEventListener=function addEventListener(e,t){var n=this._listeners[e]||(this._listeners[e]=[]);~n.indexOf(t)||n.push(t)},n.removeEventListener=function removeEventListener(e,t){var n=this._listeners[e],o=n&&n.indexOf(t);0<=o&&n.splice(o,1)},n.dispatchEvent=function dispatchEvent(t){var n,o=this;return(this._listeners[t]||[]).forEach(function(e){return!1===e.call(o,{type:t,target:o.target})&&(n=!1)}),n},EventDispatcher);function EventDispatcher(e){this._listeners={},this.target=e||this}var Ve,F=(function _inheritsLoose(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t}(Draggable,Ve=I),Draggable.register=function register(e){xe=e,_a()},Draggable.create=function create(e,t){return be||_a(!0),Te(e).map(function(e){return new Draggable(e,t)})},Draggable.get=function get(e){return Ie[(Te(e)[0]||{})._gsDragID]},Draggable.timeSinceDrag=function timeSinceDrag(){return(Re()-ze)/1e3},Draggable.hitTest=function hitTest(e,t,n){if(e===t)return!1;var o,r,i,a=Pa(e),l=Pa(t),s=a.top,c=a.left,d=a.right,p=a.bottom,u=a.width,h=a.height,f=l.left>d||l.right<c||l.top>p||l.bottom<s;return f||!n?!f:(i=-1!==(n+"").indexOf("%"),n=parseFloat(n)||0,(o={left:Math.max(c,l.left),top:Math.max(s,l.top)}).width=Math.min(d,l.right)-o.left,o.height=Math.min(p,l.bottom)-o.top,!(o.width<0||o.height<0)&&(i?u*h*(n*=.01)<=(r=o.width*o.height)||r>=l.width*l.height*n:o.width>n&&o.height>n))},Draggable);function Draggable(h,p){var e;e=Ve.call(this)||this,be||_a(1),h=Te(h)[0],e.styles=Ce&&Ce(h,"transform,left,top"),_e=_e||xe.plugins.inertia,e.vars=p=ra(p||{}),e.target=h,e.x=e.y=e.rotation=0,e.dragResistance=parseFloat(p.dragResistance)||0,e.edgeResistance=isNaN(p.edgeResistance)?1:parseFloat(p.edgeResistance)||0,e.lockAxis=p.lockAxis,e.autoScroll=p.autoScroll||0,e.lockedAxis=null,e.allowEventDefault=!!p.allowEventDefault,xe.getProperty(h,"x");function Sg(e,t){return parseFloat(se.get(h,e,t))}function zh(e){return Ba(e),e.stopImmediatePropagation&&e.stopImmediatePropagation(),!1}function Ah(e){if(J.autoScroll&&J.isDragging&&(te||C)){var t,n,o,r,i,a,l,s,c=h,d=15*J.autoScroll;for(te=!1,We.scrollTop=null!=me.pageYOffset?me.pageYOffset:null!=de.documentElement.scrollTop?de.documentElement.scrollTop:de.body.scrollTop,We.scrollLeft=null!=me.pageXOffset?me.pageXOffset:null!=de.documentElement.scrollLeft?de.documentElement.scrollLeft:de.body.scrollLeft,r=J.pointerX-We.scrollLeft,i=J.pointerY-We.scrollTop;c&&!n;)t=(n=Qe(c.parentNode))?We:c.parentNode,o=n?{bottom:Math.max(ye.clientHeight,me.innerHeight||0),right:Math.max(ye.clientWidth,me.innerWidth||0),left:0,top:0}:t.getBoundingClientRect(),a=l=0,U&&((s=t._gsMaxScrollY-t.scrollTop)<0?l=s:i>o.bottom-re&&s?(te=!0,l=Math.min(s,d*(1-Math.max(0,o.bottom-i)/re)|0)):i<o.top+ne&&t.scrollTop&&(te=!0,l=-Math.min(t.scrollTop,d*(1-Math.max(0,i-o.top)/ne)|0)),l&&(t.scrollTop+=l)),K&&((s=t._gsMaxScrollX-t.scrollLeft)<0?a=s:r>o.right-oe&&s?(te=!0,a=Math.min(s,d*(1-Math.max(0,o.right-r)/oe)|0)):r<o.left+ie&&t.scrollLeft&&(te=!0,a=-Math.min(t.scrollLeft,d*(1-Math.max(0,r-o.left)/ie)|0)),a&&(t.scrollLeft+=a)),n&&(a||l)&&(me.scrollTo(t.scrollLeft,t.scrollTop),he(J.pointerX+a,J.pointerY+l)),c=t}if(C){var p=J.x,u=J.y;Q?(J.deltaX=p-parseFloat(se.rotation),J.rotation=p,se.rotation=p+"deg",se.renderTransform(1,se)):f?(U&&(J.deltaY=u-f.top(),f.top(u)),K&&(J.deltaX=p-f.left(),f.left(p))):W?(U&&(J.deltaY=u-parseFloat(se.y),se.y=u+"px"),K&&(J.deltaX=p-parseFloat(se.x),se.x=p+"px"),se.renderTransform(1,se)):(U&&(J.deltaY=u-parseFloat(h.style.top||0),h.style.top=u+"px"),K&&(J.deltaX=p-parseFloat(h.style.left||0),h.style.left=p+"px")),!g||e||I||(!(I=!0)===Qa(J,"drag","onDrag")&&(K&&(J.x-=J.deltaX),U&&(J.y-=J.deltaY),Ah(!0)),I=!1)}C=!1}function Bh(e,t){var n,o,r=J.x,i=J.y;h._gsap||(se=xe.core.getCache(h)),se.uncache&&xe.getProperty(h,"x"),W?(J.x=parseFloat(se.x),J.y=parseFloat(se.y)):Q?J.x=J.rotation=parseFloat(se.rotation):f?(J.y=f.top(),J.x=f.left()):(J.y=parseFloat(h.style.top||(o=Na(h))&&o.top)||0,J.x=parseFloat(h.style.left||(o||{}).left)||0),(P||Y||N)&&!t&&(J.isDragging||J.isThrowing)&&(N&&(He.x=J.x,He.y=J.y,(n=N(He)).x!==J.x&&(J.x=n.x,C=!0),n.y!==J.y&&(J.y=n.y,C=!0)),P&&(n=P(J.x))!==J.x&&(J.x=n,Q&&(J.rotation=n),C=!0),Y&&((n=Y(J.y))!==J.y&&(J.y=n),C=!0)),C&&Ah(!0),e||(J.deltaX=J.x-r,J.deltaY=J.y-i,Qa(J,"throwupdate","onThrowUpdate"))}function Ch(a,l,s,n){return null==l&&(l=-Be),null==s&&(s=Be),Z(a)?function(e){var t=J.isPressed?1-J.edgeResistance:1;return a.call(J,(s<e?s+(e-s)*t:e<l?l+(e-l)*t:e)*n)*n}:Ne(a)?function(e){for(var t,n,o=a.length,r=0,i=Be;-1<--o;)(n=(t=a[o])-e)<0&&(n=-n),n<i&&l<=t&&t<=s&&(r=o,i=n);return a[r]}:isNaN(a)?function(e){return e}:function(){return a*n}}function Eh(){var e,t,n,o;E=!1,f?(f.calibrate(),J.minX=S=-f.maxScrollLeft(),J.minY=A=-f.maxScrollTop(),J.maxX=D=J.maxY=X=0,E=!0):p.bounds&&(e=Ra(p.bounds,h.parentNode),Q?(J.minX=S=e.left,J.maxX=D=e.left+e.width,J.minY=A=J.maxY=X=0):_(p.bounds.maxX)&&_(p.bounds.maxY)?(t=Ra(h,h.parentNode),J.minX=S=Math.round(Sg(G,"px")+e.left-t.left),J.minY=A=Math.round(Sg(V,"px")+e.top-t.top),J.maxX=D=Math.round(S+(e.width-t.width)),J.maxY=X=Math.round(A+(e.height-t.height))):(e=p.bounds,J.minX=S=e.minX,J.minY=A=e.minY,J.maxX=D=e.maxX,J.maxY=X=e.maxY),D<S&&(J.minX=D,J.maxX=D=S,S=J.minX),X<A&&(J.minY=X,J.maxY=X=A,A=J.minY),Q&&(J.minRotation=S,J.maxRotation=D),E=!0),p.liveSnap&&(n=!0===p.liveSnap?p.snap||{}:p.liveSnap,o=Ne(n)||Z(n),Q?(P=Ch(o?n:n.rotation,S,D,1),Y=null):n.points?N=function buildPointSnapFunc(s,l,c,d,p,u,h){return u=u&&u<Be?u*u:Be,Z(s)?function(e){var t,n,o,r=J.isPressed?1-J.edgeResistance:1,i=e.x,a=e.y;return e.x=i=c<i?c+(i-c)*r:i<l?l+(i-l)*r:i,e.y=a=p<a?p+(a-p)*r:a<d?d+(a-d)*r:a,(t=s.call(J,e))!==e&&(e.x=t.x,e.y=t.y),1!==h&&(e.x*=h,e.y*=h),u<Be&&(n=e.x-i,o=e.y-a,u<n*n+o*o&&(e.x=i,e.y=a)),e}:Ne(s)?function(e){for(var t,n,o,r,i=s.length,a=0,l=Be;-1<--i;)(r=(t=(o=s[i]).x-e.x)*t+(n=o.y-e.y)*n)<l&&(a=i,l=r);return l<=u?s[a]:e}:function(e){return e}}(o?n:n.points,S,D,A,X,n.radius,f?-1:1):(K&&(P=Ch(o?n:n.x||n.left||n.scrollLeft,S,D,f?-1:1)),U&&(Y=Ch(o?n:n.y||n.top||n.scrollTop,A,X,f?-1:1))))}function Fh(){J.isThrowing=!1,Qa(J,"throwcomplete","onThrowComplete")}function Gh(){J.isThrowing=!1}function Hh(e,t){var n,o,r,i;e&&_e?(!0===e&&(n=p.snap||p.liveSnap||{},o=Ne(n)||Z(n),e={resistance:(p.throwResistance||p.resistance||1e3)/(Q?10:1)},Q?e.rotation=Ua(J,o?n:n.rotation,D,S,1,t):(K&&(e[G]=Ua(J,o?n:n.points||n.x||n.left,D,S,f?-1:1,t||"x"===J.lockedAxis)),U&&(e[V]=Ua(J,o?n:n.points||n.y||n.top,X,A,f?-1:1,t||"y"===J.lockedAxis)),(n.points||Ne(n)&&$(n[0]))&&(e.linkedProps=G+","+V,e.radius=n.radius))),J.isThrowing=!0,i=isNaN(p.overshootTolerance)?1===p.edgeResistance?0:1-J.edgeResistance+.2:p.overshootTolerance,e.duration||(e.duration={max:Math.max(p.minDuration||0,"maxDuration"in p?p.maxDuration:2),min:isNaN(p.minDuration)?0===i||$(e)&&1e3<e.resistance?0:.5:p.minDuration,overshoot:i}),J.tween=r=xe.to(f||h,{inertia:e,data:"_draggable",inherit:!1,onComplete:Fh,onInterrupt:Gh,onUpdate:p.fastMode?Qa:Bh,onUpdateParams:p.fastMode?[J,"onthrowupdate","onThrowUpdate"]:n&&n.radius?[!1,!0]:[]}),p.fastMode||(f&&(f._skip=!0),r.render(1e9,!0,!0),Bh(!0,!0),J.endX=J.x,J.endY=J.y,Q&&(J.endRotation=J.x),r.play(0),Bh(!0,!0),f&&(f._skip=!1))):E&&J.applyBounds()}function Ih(e){var t,n=k;k=getGlobalMatrix(h.parentNode,!0),e&&J.isPressed&&!k.equals(n||new ge)&&(t=n.inverse().apply({x:w,y:b}),k.apply(t,t),w=t.x,b=t.y),k.equals(r)&&(k=null)}function Jh(){var e,t,n,o=1-J.edgeResistance,r=ce?Ga(de):0,i=ce?Fa(de):0;W&&(se.x=Sg(G,"px")+"px",se.y=Sg(V,"px")+"px",se.renderTransform()),Ih(!1),Ge.x=J.pointerX-r,Ge.y=J.pointerY-i,k&&k.apply(Ge,Ge),w=Ge.x,b=Ge.y,C&&(he(J.pointerX,J.pointerY),Ah(!0)),d=getGlobalMatrix(h),f?(Eh(),M=f.top(),T=f.left()):(pe()?(Bh(!0,!0),Eh()):J.applyBounds(),Q?(e=h.ownerSVGElement?[se.xOrigin-h.getBBox().x,se.yOrigin-h.getBBox().y]:(Na(h)[Ye]||"0 0").split(" "),L=J.rotationOrigin=getGlobalMatrix(h).apply({x:parseFloat(e[0])||0,y:parseFloat(e[1])||0}),Bh(!0,!0),t=J.pointerX-L.x-r,n=L.y-J.pointerY+i,T=J.x,M=J.y=Math.atan2(n,t)*ke):(M=Sg(V,"px"),T=Sg(G,"px"))),E&&o&&(D<T?T=D+(T-D)/o:T<S&&(T=S-(S-T)/o),Q||(X<M?M=X+(M-X)/o:M<A&&(M=A-(A-M)/o))),J.startX=T=da(T),J.startY=M=da(M)}function Lh(){!we.parentNode||pe()||J.isDragging||we.parentNode.removeChild(we)}function Mh(e,t){var n;if(!u||J.isPressed||!e||!("mousedown"!==e.type&&"pointerdown"!==e.type||t)&&Re()-le<30&&Ee[J.pointerEvent.type])z&&e&&u&&Ba(e);else{if(B=pe(),H=!1,J.pointerEvent=e,Ee[e.type]?(y=~e.type.indexOf("touch")?e.currentTarget||e.target:de,za(y,"touchend",fe),za(y,"touchmove",ue),za(y,"touchcancel",fe),za(de,"touchstart",Ea)):(y=null,za(de,"mousemove",ue)),O=null,Ae&&y||(za(de,"mouseup",fe),e&&e.target&&za(e.target,"mouseup",fe)),v=ae.call(J,e.target)&&!1===p.dragClickables&&!t)return za(e.target,"change",fe),Qa(J,"pressInit","onPressInit"),Qa(J,"press","onPress"),Wa(q,!0),void(z=!1);if(R=!(!y||K==U||!1===J.vars.allowNativeTouchScrolling||J.vars.allowContextMenu&&e&&(e.ctrlKey||2<e.which))&&(K?"y":"x"),(z=!R&&!J.allowEventDefault)&&(Ba(e),za(me,"touchforcechange",Ba)),e.changedTouches?(e=x=e.changedTouches[0],m=e.identifier):e.pointerId?m=e.pointerId:x=m=null,Pe++,function _addToRenderQueue(e){Oe.push(e),1===Oe.length&&xe.ticker.add(ua)}(Ah),b=J.pointerY=e.pageY,w=J.pointerX=e.pageX,Qa(J,"pressInit","onPressInit"),(R||J.autoScroll)&&La(h.parentNode),!h.parentNode||!J.autoScroll||f||Q||!h.parentNode._gsMaxScrollX||we.parentNode||h.getBBox||(we.style.width=h.parentNode.scrollWidth+"px",h.parentNode.appendChild(we)),Jh(),J.tween&&J.tween.kill(),J.isThrowing=!1,xe.killTweensOf(f||h,o,!0),f&&xe.killTweensOf(h,{scrollTo:1},!0),J.tween=J.lockedAxis=null,!p.zIndexBoost&&(Q||f||!1===p.zIndexBoost)||(h.style.zIndex=Draggable.zIndex++),J.isPressed=!0,g=!(!p.onDrag&&!J._listeners.drag),s=!(!p.onMove&&!J._listeners.move),!1!==p.cursor||p.activeCursor)for(n=q.length;-1<--n;)xe.set(q[n],{cursor:p.activeCursor||p.cursor||("grab"===Xe?"grabbing":Xe)});Qa(J,"press","onPress")}}function Qh(e){if(e&&J.isDragging&&!f){var t=e.target||h.parentNode,n=t.scrollLeft-t._gsScrollX,o=t.scrollTop-t._gsScrollY;(n||o)&&(k?(w-=n*k.a+o*k.c,b-=o*k.d+n*k.b):(w-=n,b-=o),t._gsScrollX+=n,t._gsScrollY+=o,he(J.pointerX,J.pointerY))}}function Rh(e){var t=Re(),n=t-le<100,o=t-ee<50,r=n&&F===le,i=J.pointerEvent&&J.pointerEvent.defaultPrevented,a=n&&c===le,l=e.isTrusted||null==e.isTrusted&&n&&r;if((r||o&&!1!==J.vars.suppressClickOnDrag)&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),n&&(!J.pointerEvent||!J.pointerEvent.defaultPrevented)&&(!r||l&&!a))return l&&r&&(c=le),void(F=le);(J.isPressed||o||n)&&(l&&e.detail&&n&&!i||Ba(e)),n||o||H||(e&&e.target&&(J.pointerEvent=e),Qa(J,"click","onClick"))}function Sh(e){return k?{x:e.x*k.a+e.y*k.c+k.e,y:e.x*k.b+e.y*k.d+k.f}:{x:e.x,y:e.y}}var u,f,w,b,T,M,E,g,s,D,S,X,A,x,m,L,C,t,P,Y,N,v,y,k,B,R,O,I,F,c,z,d,H,n=(p.type||"x,y").toLowerCase(),W=~n.indexOf("x")||~n.indexOf("y"),Q=-1!==n.indexOf("rotation"),G=Q?"rotation":W?"x":"left",V=W?"y":"top",K=!(!~n.indexOf("x")&&!~n.indexOf("left")&&"scroll"!==n),U=!(!~n.indexOf("y")&&!~n.indexOf("top")&&"scroll"!==n),j=p.minimumMovement||2,J=_assertThisInitialized(e),q=Te(p.trigger||p.handle||h),o={},ee=0,te=!1,ne=p.autoScrollMarginTop||40,oe=p.autoScrollMarginRight||40,re=p.autoScrollMarginBottom||40,ie=p.autoScrollMarginLeft||40,ae=p.clickableTest||Va,le=0,se=h._gsap||xe.core.getCache(h),ce=function _isFixed(e){return"fixed"===Na(e).position||((e=e.parentNode)&&1===e.nodeType?_isFixed(e):void 0)}(h),de=h.ownerDocument||ve,pe=function isTweening(){return J.tween&&J.tween.isActive()},ue=function onMove(e){var t,n,o,r,i,a,l=e;if(u&&!De&&J.isPressed&&e){if(t=(J.pointerEvent=e).changedTouches){if((e=t[0])!==x&&e.identifier!==m){for(r=t.length;-1<--r&&(e=t[r]).identifier!==m&&e.target!==h;);if(r<0)return}}else if(e.pointerId&&m&&e.pointerId!==m)return;y&&R&&!O&&(Ge.x=e.pageX-(ce?Ga(de):0),Ge.y=e.pageY-(ce?Fa(de):0),k&&k.apply(Ge,Ge),n=Ge.x,o=Ge.y,((i=Math.abs(n-w))!==(a=Math.abs(o-b))&&(j<i||j<a)||Se&&R===O)&&(O=a<i&&K?"x":"y",R&&O!==R&&za(me,"touchforcechange",Ba),!1!==J.vars.lockAxisOnTouchScroll&&K&&U&&(J.lockedAxis="x"===O?"y":"x",Z(J.vars.onLockAxis)&&J.vars.onLockAxis.call(J,l)),Se&&R===O))?fe(l):(z=J.allowEventDefault||R&&(!O||R===O)||!1===l.cancelable?z&&!1:(Ba(l),!0),J.autoScroll&&(te=!0),he(e.pageX,e.pageY,s))}else z&&e&&u&&Ba(e)},he=function setPointerPosition(e,t,n){var o,r,i,a,l,s,c=1-J.dragResistance,d=1-J.edgeResistance,p=J.pointerX,u=J.pointerY,h=M,f=J.x,g=J.y,x=J.endX,m=J.endY,v=J.endRotation,y=C;J.pointerX=e,J.pointerY=t,ce&&(e-=Ga(de),t-=Fa(de)),Q?(a=Math.atan2(L.y-t,e-L.x)*ke,180<(l=J.y-a)?(M-=360,J.y=a):l<-180&&(M+=360,J.y=a),i=J.x!==T||Math.max(Math.abs(w-e),Math.abs(b-t))>j?(J.y=a,T+(M-a)*c):T):(k&&(s=e*k.a+t*k.c+k.e,t=e*k.b+t*k.d+k.f,e=s),(r=t-b)<j&&-j<r&&(r=0),(o=e-w)<j&&-j<o&&(o=0),(J.lockAxis||J.lockedAxis)&&(o||r)&&((s=J.lockedAxis)||(J.lockedAxis=s=K&&Math.abs(o)>Math.abs(r)?"y":U?"x":null,s&&Z(J.vars.onLockAxis)&&J.vars.onLockAxis.call(J,J.pointerEvent)),"y"===s?r=0:"x"===s&&(o=0)),i=da(T+o*c),a=da(M+r*c)),(P||Y||N)&&(J.x!==i||J.y!==a&&!Q)&&(N&&(He.x=i,He.y=a,s=N(He),i=da(s.x),a=da(s.y)),P&&(i=da(P(i))),Y&&(a=da(Y(a)))),E&&(D<i?i=D+Math.round((i-D)*d):i<S&&(i=S+Math.round((i-S)*d)),Q||(X<a?a=Math.round(X+(a-X)*d):a<A&&(a=Math.round(A+(a-A)*d)))),J.x===i&&(J.y===a||Q)||(Q?(J.endRotation=J.x=J.endX=i,C=!0):(U&&(J.y=J.endY=a,C=!0),K&&(J.x=J.endX=i,C=!0)),n&&!1===Qa(J,"move","onMove")?(J.pointerX=p,J.pointerY=u,M=h,J.x=f,J.y=g,J.endX=x,J.endY=m,J.endRotation=v,C=y):!J.isDragging&&J.isPressed&&(J.isDragging=H=!0,Qa(J,"dragstart","onDragStart")))},fe=function onRelease(e,t){if(u&&J.isPressed&&(!e||null==m||t||!(e.pointerId&&e.pointerId!==m&&e.target!==h||e.changedTouches&&!function _hasTouchID(e,t){for(var n=e.length;n--;)if(e[n].identifier===t)return!0}(e.changedTouches,m)))){J.isPressed=!1;var n,o,r,i,a,l=e,s=J.isDragging,c=J.vars.allowContextMenu&&e&&(e.ctrlKey||2<e.which),d=xe.delayedCall(.001,Lh);if(y?(Aa(y,"touchend",onRelease),Aa(y,"touchmove",ue),Aa(y,"touchcancel",onRelease),Aa(de,"touchstart",Ea)):Aa(de,"mousemove",ue),Aa(me,"touchforcechange",Ba),Ae&&y||(Aa(de,"mouseup",onRelease),e&&e.target&&Aa(e.target,"mouseup",onRelease)),C=!1,s&&(ee=ze=Re(),J.isDragging=!1),xa(Ah),v&&!c)return e&&(Aa(e.target,"change",onRelease),J.pointerEvent=l),Wa(q,!1),Qa(J,"release","onRelease"),Qa(J,"click","onClick"),void(v=!1);for(o=q.length;-1<--o;)Ma(q[o],"cursor",p.cursor||(!1!==p.cursor?Xe:null));if(Pe--,e){if((n=e.changedTouches)&&(e=n[0])!==x&&e.identifier!==m){for(o=n.length;-1<--o&&(e=n[o]).identifier!==m&&e.target!==h;);if(o<0&&!t)return}J.pointerEvent=l,J.pointerX=e.pageX,J.pointerY=e.pageY}return c&&l?(Ba(l),z=!0,Qa(J,"release","onRelease")):l&&!s?(z=!1,B&&(p.snap||p.bounds)&&Hh(p.inertia||p.throwProps),Qa(J,"release","onRelease"),Se&&"touchmove"===l.type||-1!==l.type.indexOf("cancel")||(Qa(J,"click","onClick"),Re()-le<300&&Qa(J,"doubleclick","onDoubleClick"),i=l.target||h,le=Re(),a=function syntheticClick(){le===F||!J.enabled()||J.isPressed||l.defaultPrevented||(i.click?i.click():de.createEvent&&((r=de.createEvent("MouseEvents")).initMouseEvent("click",!0,!0,me,1,J.pointerEvent.screenX,J.pointerEvent.screenY,J.pointerX,J.pointerY,!1,!1,!1,!1,0,null),i.dispatchEvent(r)))},Se||l.defaultPrevented||xe.delayedCall(.05,a))):(Hh(p.inertia||p.throwProps),J.allowEventDefault||!l||!1===p.dragClickables&&ae.call(J,l.target)||!s||R&&(!O||R!==O)||!1===l.cancelable?z=!1:(z=!0,Ba(l)),Qa(J,"release","onRelease")),pe()&&d.duration(J.tween.duration()),s&&Qa(J,"dragend","onDragEnd"),!0}z&&e&&u&&Ba(e)};return(t=Draggable.get(h))&&t.kill(),e.startDrag=function(e,t){var n,o,r,i;Mh(e||J.pointerEvent,!0),t&&!J.hitTest(e||J.pointerEvent)&&(n=Pa(e||J.pointerEvent),o=Pa(h),r=Sh({x:n.left+n.width/2,y:n.top+n.height/2}),i=Sh({x:o.left+o.width/2,y:o.top+o.height/2}),w-=r.x-i.x,b-=r.y-i.y),J.isDragging||(J.isDragging=H=!0,Qa(J,"dragstart","onDragStart"))},e.drag=ue,e.endDrag=function(e){return fe(e||J.pointerEvent,!0)},e.timeSinceDrag=function(){return J.isDragging?0:(Re()-ee)/1e3},e.timeSinceClick=function(){return(Re()-le)/1e3},e.hitTest=function(e,t){return Draggable.hitTest(J.target,e,t)},e.getDirection=function(e,t){var n,o,r,i,a,l,s="velocity"===e&&_e?e:$(e)&&!Q?"element":"start";return"element"===s&&(a=Pa(J.target),l=Pa(e)),n="start"===s?J.x-T:"velocity"===s?_e.getVelocity(h,G):a.left+a.width/2-(l.left+l.width/2),Q?n<0?"counter-clockwise":"clockwise":(t=t||2,o="start"===s?J.y-M:"velocity"===s?_e.getVelocity(h,V):a.top+a.height/2-(l.top+l.height/2),i=(r=Math.abs(n/o))<1/t?"":n<0?"left":"right",r<t&&(""!==i&&(i+="-"),i+=o<0?"up":"down"),i)},e.applyBounds=function(e,t){var n,o,r,i,a,l;if(e&&p.bounds!==e)return p.bounds=e,J.update(!0,t);if(Bh(!0),Eh(),E&&!pe()){if(n=J.x,o=J.y,D<n?n=D:n<S&&(n=S),X<o?o=X:o<A&&(o=A),(J.x!==n||J.y!==o)&&(r=!0,J.x=J.endX=n,Q?J.endRotation=n:J.y=J.endY=o,Ah(C=!0),J.autoScroll&&!J.isDragging))for(La(h.parentNode),i=h,We.scrollTop=null!=me.pageYOffset?me.pageYOffset:null!=de.documentElement.scrollTop?de.documentElement.scrollTop:de.body.scrollTop,We.scrollLeft=null!=me.pageXOffset?me.pageXOffset:null!=de.documentElement.scrollLeft?de.documentElement.scrollLeft:de.body.scrollLeft;i&&!l;)a=(l=Qe(i.parentNode))?We:i.parentNode,U&&a.scrollTop>a._gsMaxScrollY&&(a.scrollTop=a._gsMaxScrollY),K&&a.scrollLeft>a._gsMaxScrollX&&(a.scrollLeft=a._gsMaxScrollX),i=a;J.isThrowing&&(r||J.endX>D||J.endX<S||J.endY>X||J.endY<A)&&Hh(p.inertia||p.throwProps,r)}return J},e.update=function(e,t,n){if(t&&J.isPressed){var o=getGlobalMatrix(h),r=d.apply({x:J.x-T,y:J.y-M}),i=getGlobalMatrix(h.parentNode,!0);i.apply({x:o.e-r.x,y:o.f-r.y},r),J.x-=r.x-i.e,J.y-=r.y-i.f,Ah(!0),Jh()}var a=J.x,l=J.y;return Ih(!t),e?J.applyBounds():(C&&n&&Ah(!0),Bh(!0)),t&&(he(J.pointerX,J.pointerY),C&&Ah(!0)),J.isPressed&&!t&&(K&&.01<Math.abs(a-J.x)||U&&.01<Math.abs(l-J.y)&&!Q)&&Jh(),J.autoScroll&&(La(h.parentNode,J.isDragging),te=J.isDragging,Ah(!0),Ia(h,Qh),Ha(h,Qh)),J},e.enable=function(e){var t,n,o,r={lazy:!0};if(!1!==p.cursor&&(r.cursor=p.cursor||Xe),xe.utils.checkPrefix("touchCallout")&&(r.touchCallout="none"),"soft"!==e){for(ta(q,K==U?"none":p.allowNativeTouchScrolling&&h.scrollHeight===h.clientHeight==(h.scrollWidth===h.clientHeight)||p.allowEventDefault?"manipulation":K?"pan-y":"pan-x"),n=q.length;-1<--n;)o=q[n],Ae||za(o,"mousedown",Mh),za(o,"touchstart",Mh),za(o,"click",Rh,!0),xe.set(o,r),o.getBBox&&o.ownerSVGElement&&K!=U&&xe.set(o.ownerSVGElement,{touchAction:p.allowNativeTouchScrolling||p.allowEventDefault?"manipulation":K?"pan-y":"pan-x"}),p.allowContextMenu||za(o,"contextmenu",zh);Wa(q,!1)}return Ha(h,Qh),u=!0,_e&&"soft"!==e&&_e.track(f||h,W?"x,y":Q?"rotation":"top,left"),h._gsDragID=t=h._gsDragID||"d"+Fe++,Ie[t]=J,f&&(f.enable(),f.element._gsDragID=t),(p.bounds||Q)&&Jh(),p.bounds&&J.applyBounds(),J},e.disable=function(e){for(var t,n=J.isDragging,o=q.length;-1<--o;)Ma(q[o],"cursor",null);if("soft"!==e){for(ta(q,null),o=q.length;-1<--o;)t=q[o],Ma(t,"touchCallout",null),Aa(t,"mousedown",Mh),Aa(t,"touchstart",Mh),Aa(t,"click",Rh,!0),Aa(t,"contextmenu",zh);Wa(q,!0),y&&(Aa(y,"touchcancel",fe),Aa(y,"touchend",fe),Aa(y,"touchmove",ue)),Aa(de,"mouseup",fe),Aa(de,"mousemove",ue)}return Ia(h,Qh),u=!1,_e&&"soft"!==e&&(_e.untrack(f||h,W?"x,y":Q?"rotation":"top,left"),J.tween&&J.tween.kill()),f&&f.disable(),xa(Ah),J.isDragging=J.isPressed=v=!1,n&&Qa(J,"dragend","onDragEnd"),J},e.enabled=function(e,t){return arguments.length?e?J.enable(t):J.disable(t):u},e.kill=function(){return J.isThrowing=!1,J.tween&&J.tween.kill(),J.disable(),xe.set(q,{clearProps:"userSelect"}),delete Ie[h._gsDragID],J},e.revert=function(){this.kill(),this.styles&&this.styles.revert()},~n.indexOf("scroll")&&(f=e.scrollProxy=new $a(h,function _extend(e,t){for(var n in t)n in e||(e[n]=t[n]);return e}({onKill:function onKill(){J.isPressed&&fe(null)}},p)),h.style.overflowY=U&&!Me?"auto":"hidden",h.style.overflowX=K&&!Me?"auto":"hidden",h=f.content),Q?o.rotation=1:(K&&(o[G]=1),U&&(o[V]=1)),se.force3D=!("force3D"in p)||p.force3D,Le(_assertThisInitialized(e)),e.enable(),e}!function _setDefaults(e,t){for(var n in t)n in e||(e[n]=t[n])}(F.prototype,{pointerX:0,pointerY:0,startX:0,startY:0,deltaX:0,deltaY:0,isDragging:!1,isPressed:!1}),F.zIndex=1e3,F.version="3.13.0",Y()&&xe.registerPlugin(F),e.Draggable=F,e.default=F;if (typeof(window)==="undefined"||window!==e){Object.defineProperty(e,"__esModule",{value:!0})} else {delete e.default}});

