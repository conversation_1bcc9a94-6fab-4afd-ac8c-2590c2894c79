"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lenis";
exports.ids = ["vendor-chunks/lenis"];
exports.modules = {

/***/ "(ssr)/./node_modules/lenis/dist/lenis.mjs":
/*!*******************************************!*\
  !*** ./node_modules/lenis/dist/lenis.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Lenis)\n/* harmony export */ });\n// package.json\nvar version = \"1.3.8\";\n// packages/core/src/maths.ts\nfunction clamp(min, input, max) {\n    return Math.max(min, Math.min(input, max));\n}\nfunction lerp(x, y, t) {\n    return (1 - t) * x + t * y;\n}\nfunction damp(x, y, lambda, deltaTime) {\n    return lerp(x, y, 1 - Math.exp(-lambda * deltaTime));\n}\nfunction modulo(n, d) {\n    return (n % d + d) % d;\n}\n// packages/core/src/animate.ts\nvar Animate = class {\n    /**\n   * Advance the animation by the given delta time\n   *\n   * @param deltaTime - The time in seconds to advance the animation\n   */ advance(deltaTime) {\n        if (!this.isRunning) return;\n        let completed = false;\n        if (this.duration && this.easing) {\n            this.currentTime += deltaTime;\n            const linearProgress = clamp(0, this.currentTime / this.duration, 1);\n            completed = linearProgress >= 1;\n            const easedProgress = completed ? 1 : this.easing(linearProgress);\n            this.value = this.from + (this.to - this.from) * easedProgress;\n        } else if (this.lerp) {\n            this.value = damp(this.value, this.to, this.lerp * 60, deltaTime);\n            if (Math.round(this.value) === this.to) {\n                this.value = this.to;\n                completed = true;\n            }\n        } else {\n            this.value = this.to;\n            completed = true;\n        }\n        if (completed) {\n            this.stop();\n        }\n        this.onUpdate?.(this.value, completed);\n    }\n    /** Stop the animation */ stop() {\n        this.isRunning = false;\n    }\n    /**\n   * Set up the animation from a starting value to an ending value\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\n   *\n   * @param from - The starting value\n   * @param to - The ending value\n   * @param options - Options for the animation\n   */ fromTo(from, to, { lerp: lerp2, duration, easing, onStart, onUpdate }) {\n        this.from = this.value = from;\n        this.to = to;\n        this.lerp = lerp2;\n        this.duration = duration;\n        this.easing = easing;\n        this.currentTime = 0;\n        this.isRunning = true;\n        onStart?.();\n        this.onUpdate = onUpdate;\n    }\n    constructor(){\n        this.isRunning = false;\n        this.value = 0;\n        this.from = 0;\n        this.to = 0;\n        this.currentTime = 0;\n    }\n};\n// packages/core/src/debounce.ts\nfunction debounce(callback, delay) {\n    let timer;\n    return function(...args) {\n        let context = this;\n        clearTimeout(timer);\n        timer = setTimeout(()=>{\n            timer = void 0;\n            callback.apply(context, args);\n        }, delay);\n    };\n}\n// packages/core/src/dimensions.ts\nvar Dimensions = class {\n    constructor(wrapper, content, { autoResize = true, debounce: debounceValue = 250 } = {}){\n        this.width = 0;\n        this.height = 0;\n        this.scrollHeight = 0;\n        this.scrollWidth = 0;\n        this.resize = ()=>{\n            this.onWrapperResize();\n            this.onContentResize();\n        };\n        this.onWrapperResize = ()=>{\n            if (this.wrapper instanceof Window) {\n                this.width = window.innerWidth;\n                this.height = window.innerHeight;\n            } else {\n                this.width = this.wrapper.clientWidth;\n                this.height = this.wrapper.clientHeight;\n            }\n        };\n        this.onContentResize = ()=>{\n            if (this.wrapper instanceof Window) {\n                this.scrollHeight = this.content.scrollHeight;\n                this.scrollWidth = this.content.scrollWidth;\n            } else {\n                this.scrollHeight = this.wrapper.scrollHeight;\n                this.scrollWidth = this.wrapper.scrollWidth;\n            }\n        };\n        this.wrapper = wrapper;\n        this.content = content;\n        if (autoResize) {\n            this.debouncedResize = debounce(this.resize, debounceValue);\n            if (this.wrapper instanceof Window) {\n                window.addEventListener(\"resize\", this.debouncedResize, false);\n            } else {\n                this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize);\n                this.wrapperResizeObserver.observe(this.wrapper);\n            }\n            this.contentResizeObserver = new ResizeObserver(this.debouncedResize);\n            this.contentResizeObserver.observe(this.content);\n        }\n        this.resize();\n    }\n    destroy() {\n        this.wrapperResizeObserver?.disconnect();\n        this.contentResizeObserver?.disconnect();\n        if (this.wrapper === window && this.debouncedResize) {\n            window.removeEventListener(\"resize\", this.debouncedResize, false);\n        }\n    }\n    get limit() {\n        return {\n            x: this.scrollWidth - this.width,\n            y: this.scrollHeight - this.height\n        };\n    }\n};\n// packages/core/src/emitter.ts\nvar Emitter = class {\n    /**\n   * Emit an event with the given data\n   * @param event Event name\n   * @param args Data to pass to the event handlers\n   */ emit(event, ...args) {\n        let callbacks = this.events[event] || [];\n        for(let i = 0, length = callbacks.length; i < length; i++){\n            callbacks[i]?.(...args);\n        }\n    }\n    /**\n   * Add a callback to the event\n   * @param event Event name\n   * @param cb Callback function\n   * @returns Unsubscribe function\n   */ on(event, cb) {\n        this.events[event]?.push(cb) || (this.events[event] = [\n            cb\n        ]);\n        return ()=>{\n            this.events[event] = this.events[event]?.filter((i)=>cb !== i);\n        };\n    }\n    /**\n   * Remove a callback from the event\n   * @param event Event name\n   * @param callback Callback function\n   */ off(event, callback) {\n        this.events[event] = this.events[event]?.filter((i)=>callback !== i);\n    }\n    /**\n   * Remove all event listeners and clean up\n   */ destroy() {\n        this.events = {};\n    }\n    constructor(){\n        this.events = {};\n    }\n};\n// packages/core/src/virtual-scroll.ts\nvar LINE_HEIGHT = 100 / 6;\nvar listenerOptions = {\n    passive: false\n};\nvar VirtualScroll = class {\n    constructor(element, options = {\n        wheelMultiplier: 1,\n        touchMultiplier: 1\n    }){\n        this.touchStart = {\n            x: 0,\n            y: 0\n        };\n        this.lastDelta = {\n            x: 0,\n            y: 0\n        };\n        this.window = {\n            width: 0,\n            height: 0\n        };\n        this.emitter = new Emitter();\n        /**\n   * Event handler for 'touchstart' event\n   *\n   * @param event Touch event\n   */ this.onTouchStart = (event)=>{\n            const { clientX, clientY } = event.targetTouches ? event.targetTouches[0] : event;\n            this.touchStart.x = clientX;\n            this.touchStart.y = clientY;\n            this.lastDelta = {\n                x: 0,\n                y: 0\n            };\n            this.emitter.emit(\"scroll\", {\n                deltaX: 0,\n                deltaY: 0,\n                event\n            });\n        };\n        /** Event handler for 'touchmove' event */ this.onTouchMove = (event)=>{\n            const { clientX, clientY } = event.targetTouches ? event.targetTouches[0] : event;\n            const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier;\n            const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier;\n            this.touchStart.x = clientX;\n            this.touchStart.y = clientY;\n            this.lastDelta = {\n                x: deltaX,\n                y: deltaY\n            };\n            this.emitter.emit(\"scroll\", {\n                deltaX,\n                deltaY,\n                event\n            });\n        };\n        this.onTouchEnd = (event)=>{\n            this.emitter.emit(\"scroll\", {\n                deltaX: this.lastDelta.x,\n                deltaY: this.lastDelta.y,\n                event\n            });\n        };\n        /** Event handler for 'wheel' event */ this.onWheel = (event)=>{\n            let { deltaX, deltaY, deltaMode } = event;\n            const multiplierX = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1;\n            const multiplierY = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1;\n            deltaX *= multiplierX;\n            deltaY *= multiplierY;\n            deltaX *= this.options.wheelMultiplier;\n            deltaY *= this.options.wheelMultiplier;\n            this.emitter.emit(\"scroll\", {\n                deltaX,\n                deltaY,\n                event\n            });\n        };\n        this.onWindowResize = ()=>{\n            this.window = {\n                width: window.innerWidth,\n                height: window.innerHeight\n            };\n        };\n        this.element = element;\n        this.options = options;\n        window.addEventListener(\"resize\", this.onWindowResize, false);\n        this.onWindowResize();\n        this.element.addEventListener(\"wheel\", this.onWheel, listenerOptions);\n        this.element.addEventListener(\"touchstart\", this.onTouchStart, listenerOptions);\n        this.element.addEventListener(\"touchmove\", this.onTouchMove, listenerOptions);\n        this.element.addEventListener(\"touchend\", this.onTouchEnd, listenerOptions);\n    }\n    /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */ on(event, callback) {\n        return this.emitter.on(event, callback);\n    }\n    /** Remove all event listeners and clean up */ destroy() {\n        this.emitter.destroy();\n        window.removeEventListener(\"resize\", this.onWindowResize, false);\n        this.element.removeEventListener(\"wheel\", this.onWheel, listenerOptions);\n        this.element.removeEventListener(\"touchstart\", this.onTouchStart, listenerOptions);\n        this.element.removeEventListener(\"touchmove\", this.onTouchMove, listenerOptions);\n        this.element.removeEventListener(\"touchend\", this.onTouchEnd, listenerOptions);\n    }\n};\n// packages/core/src/lenis.ts\nvar defaultEasing = (t)=>Math.min(1, 1.001 - Math.pow(2, -10 * t));\nvar Lenis = class {\n    constructor({ wrapper = window, content = document.documentElement, eventsTarget = wrapper, smoothWheel = true, syncTouch = false, syncTouchLerp = 0.075, touchInertiaExponent = 1.7, duration, // in seconds\n    easing, lerp: lerp2 = 0.1, infinite = false, orientation = \"vertical\", // vertical, horizontal\n    gestureOrientation = \"vertical\", // vertical, horizontal, both\n    touchMultiplier = 1, wheelMultiplier = 1, autoResize = true, prevent, virtualScroll, overscroll = true, autoRaf = false, anchors = false, autoToggle = false, // https://caniuse.com/?search=transition-behavior\n    allowNestedScroll = false, __experimental__naiveDimensions = false } = {}){\n        this._isScrolling = false;\n        // true when scroll is animating\n        this._isStopped = false;\n        // true if user should not be able to scroll - enable/disable programmatically\n        this._isLocked = false;\n        // same as isStopped but enabled/disabled when scroll reaches target\n        this._preventNextNativeScrollEvent = false;\n        this._resetVelocityTimeout = null;\n        this.__rafID = null;\n        /**\n   * The time in ms since the lenis instance was created\n   */ this.time = 0;\n        /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */ this.userData = {};\n        /**\n   * The last velocity of the scroll\n   */ this.lastVelocity = 0;\n        /**\n   * The current velocity of the scroll\n   */ this.velocity = 0;\n        /**\n   * The direction of the scroll\n   */ this.direction = 0;\n        // These are instanciated here as they don't need information from the options\n        this.animate = new Animate();\n        this.emitter = new Emitter();\n        this.onScrollEnd = (e)=>{\n            if (!(e instanceof CustomEvent)) {\n                if (this.isScrolling === \"smooth\" || this.isScrolling === false) {\n                    e.stopPropagation();\n                }\n            }\n        };\n        this.dispatchScrollendEvent = ()=>{\n            this.options.wrapper.dispatchEvent(new CustomEvent(\"scrollend\", {\n                bubbles: this.options.wrapper === window,\n                // cancelable: false,\n                detail: {\n                    lenisScrollEnd: true\n                }\n            }));\n        };\n        this.onTransitionEnd = (event)=>{\n            if (event.propertyName.includes(\"overflow\")) {\n                const property = this.isHorizontal ? \"overflow-x\" : \"overflow-y\";\n                const overflow = getComputedStyle(this.rootElement)[property];\n                if ([\n                    \"hidden\",\n                    \"clip\"\n                ].includes(overflow)) {\n                    this.internalStop();\n                } else {\n                    this.internalStart();\n                }\n            }\n        };\n        this.onClick = (event)=>{\n            const path = event.composedPath();\n            const anchor = path.find((node)=>node instanceof HTMLAnchorElement && (node.getAttribute(\"href\")?.startsWith(\"#\") || node.getAttribute(\"href\")?.startsWith(\"/#\") || node.getAttribute(\"href\")?.startsWith(\"./#\")));\n            if (anchor) {\n                const id = anchor.getAttribute(\"href\");\n                if (id) {\n                    const options = typeof this.options.anchors === \"object\" && this.options.anchors ? this.options.anchors : void 0;\n                    let target = `#${id.split(\"#\")[1]}`;\n                    if ([\n                        \"#\",\n                        \"/#\",\n                        \"./#\",\n                        \"#top\",\n                        \"/#top\",\n                        \"./#top\"\n                    ].includes(id)) {\n                        target = 0;\n                    }\n                    this.scrollTo(target, options);\n                }\n            }\n        };\n        this.onPointerDown = (event)=>{\n            if (event.button === 1) {\n                this.reset();\n            }\n        };\n        this.onVirtualScroll = (data)=>{\n            if (typeof this.options.virtualScroll === \"function\" && this.options.virtualScroll(data) === false) return;\n            const { deltaX, deltaY, event } = data;\n            this.emitter.emit(\"virtual-scroll\", {\n                deltaX,\n                deltaY,\n                event\n            });\n            if (event.ctrlKey) return;\n            if (event.lenisStopPropagation) return;\n            const isTouch = event.type.includes(\"touch\");\n            const isWheel = event.type.includes(\"wheel\");\n            this.isTouching = event.type === \"touchstart\" || event.type === \"touchmove\";\n            const isClickOrTap = deltaX === 0 && deltaY === 0;\n            const isTapToStop = this.options.syncTouch && isTouch && event.type === \"touchstart\" && isClickOrTap && !this.isStopped && !this.isLocked;\n            if (isTapToStop) {\n                this.reset();\n                return;\n            }\n            const isUnknownGesture = this.options.gestureOrientation === \"vertical\" && deltaY === 0 || this.options.gestureOrientation === \"horizontal\" && deltaX === 0;\n            if (isClickOrTap || isUnknownGesture) {\n                return;\n            }\n            let composedPath = event.composedPath();\n            composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement));\n            const prevent = this.options.prevent;\n            if (!!composedPath.find((node)=>node instanceof HTMLElement && (typeof prevent === \"function\" && prevent?.(node) || node.hasAttribute?.(\"data-lenis-prevent\") || isTouch && node.hasAttribute?.(\"data-lenis-prevent-touch\") || isWheel && node.hasAttribute?.(\"data-lenis-prevent-wheel\") || this.options.allowNestedScroll && this.checkNestedScroll(node, {\n                    deltaX,\n                    deltaY\n                })))) return;\n            if (this.isStopped || this.isLocked) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n                return;\n            }\n            const isSmooth = this.options.syncTouch && isTouch || this.options.smoothWheel && isWheel;\n            if (!isSmooth) {\n                this.isScrolling = \"native\";\n                this.animate.stop();\n                event.lenisStopPropagation = true;\n                return;\n            }\n            let delta = deltaY;\n            if (this.options.gestureOrientation === \"both\") {\n                delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX;\n            } else if (this.options.gestureOrientation === \"horizontal\") {\n                delta = deltaX;\n            }\n            if (!this.options.overscroll || this.options.infinite || this.options.wrapper !== window && (this.animatedScroll > 0 && this.animatedScroll < this.limit || this.animatedScroll === 0 && deltaY > 0 || this.animatedScroll === this.limit && deltaY < 0)) {\n                event.lenisStopPropagation = true;\n            }\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            const isSyncTouch = isTouch && this.options.syncTouch;\n            const isTouchEnd = isTouch && event.type === \"touchend\";\n            const hasTouchInertia = isTouchEnd;\n            if (hasTouchInertia) {\n                delta = Math.sign(this.velocity) * Math.pow(Math.abs(this.velocity), this.options.touchInertiaExponent);\n            }\n            this.scrollTo(this.targetScroll + delta, {\n                programmatic: false,\n                ...isSyncTouch ? {\n                    lerp: hasTouchInertia ? this.options.syncTouchLerp : 1\n                } : {\n                    lerp: this.options.lerp,\n                    duration: this.options.duration,\n                    easing: this.options.easing\n                }\n            });\n        };\n        this.onNativeScroll = ()=>{\n            if (this._resetVelocityTimeout !== null) {\n                clearTimeout(this._resetVelocityTimeout);\n                this._resetVelocityTimeout = null;\n            }\n            if (this._preventNextNativeScrollEvent) {\n                this._preventNextNativeScrollEvent = false;\n                return;\n            }\n            if (this.isScrolling === false || this.isScrolling === \"native\") {\n                const lastScroll = this.animatedScroll;\n                this.animatedScroll = this.targetScroll = this.actualScroll;\n                this.lastVelocity = this.velocity;\n                this.velocity = this.animatedScroll - lastScroll;\n                this.direction = Math.sign(this.animatedScroll - lastScroll);\n                if (!this.isStopped) {\n                    this.isScrolling = \"native\";\n                }\n                this.emit();\n                if (this.velocity !== 0) {\n                    this._resetVelocityTimeout = setTimeout(()=>{\n                        this.lastVelocity = this.velocity;\n                        this.velocity = 0;\n                        this.isScrolling = false;\n                        this.emit();\n                    }, 400);\n                }\n            }\n        };\n        /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */ this.raf = (time)=>{\n            const deltaTime = time - (this.time || time);\n            this.time = time;\n            this.animate.advance(deltaTime * 1e-3);\n            if (this.options.autoRaf) {\n                this.__rafID = requestAnimationFrame(this.raf);\n            }\n        };\n        window.lenisVersion = version;\n        if (!wrapper || wrapper === document.documentElement) {\n            wrapper = window;\n        }\n        if (typeof duration === \"number\" && typeof easing !== \"function\") {\n            easing = defaultEasing;\n        } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n            duration = 1;\n        }\n        this.options = {\n            wrapper,\n            content,\n            eventsTarget,\n            smoothWheel,\n            syncTouch,\n            syncTouchLerp,\n            touchInertiaExponent,\n            duration,\n            easing,\n            lerp: lerp2,\n            infinite,\n            gestureOrientation,\n            orientation,\n            touchMultiplier,\n            wheelMultiplier,\n            autoResize,\n            prevent,\n            virtualScroll,\n            overscroll,\n            autoRaf,\n            anchors,\n            autoToggle,\n            allowNestedScroll,\n            __experimental__naiveDimensions\n        };\n        this.dimensions = new Dimensions(wrapper, content, {\n            autoResize\n        });\n        this.updateClassName();\n        this.targetScroll = this.animatedScroll = this.actualScroll;\n        this.options.wrapper.addEventListener(\"scroll\", this.onNativeScroll, false);\n        this.options.wrapper.addEventListener(\"scrollend\", this.onScrollEnd, {\n            capture: true\n        });\n        if (this.options.anchors && this.options.wrapper === window) {\n            this.options.wrapper.addEventListener(\"click\", this.onClick, false);\n        }\n        this.options.wrapper.addEventListener(\"pointerdown\", this.onPointerDown, false);\n        this.virtualScroll = new VirtualScroll(eventsTarget, {\n            touchMultiplier,\n            wheelMultiplier\n        });\n        this.virtualScroll.on(\"scroll\", this.onVirtualScroll);\n        if (this.options.autoToggle) {\n            this.rootElement.addEventListener(\"transitionend\", this.onTransitionEnd, {\n                passive: true\n            });\n        }\n        if (this.options.autoRaf) {\n            this.__rafID = requestAnimationFrame(this.raf);\n        }\n    }\n    /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */ destroy() {\n        this.emitter.destroy();\n        this.options.wrapper.removeEventListener(\"scroll\", this.onNativeScroll, false);\n        this.options.wrapper.removeEventListener(\"scrollend\", this.onScrollEnd, {\n            capture: true\n        });\n        this.options.wrapper.removeEventListener(\"pointerdown\", this.onPointerDown, false);\n        if (this.options.anchors && this.options.wrapper === window) {\n            this.options.wrapper.removeEventListener(\"click\", this.onClick, false);\n        }\n        this.virtualScroll.destroy();\n        this.dimensions.destroy();\n        this.cleanUpClassName();\n        if (this.__rafID) {\n            cancelAnimationFrame(this.__rafID);\n        }\n    }\n    on(event, callback) {\n        return this.emitter.on(event, callback);\n    }\n    off(event, callback) {\n        return this.emitter.off(event, callback);\n    }\n    setScroll(scroll) {\n        if (this.isHorizontal) {\n            this.options.wrapper.scrollTo({\n                left: scroll,\n                behavior: \"instant\"\n            });\n        } else {\n            this.options.wrapper.scrollTo({\n                top: scroll,\n                behavior: \"instant\"\n            });\n        }\n    }\n    /**\n   * Force lenis to recalculate the dimensions\n   */ resize() {\n        this.dimensions.resize();\n        this.animatedScroll = this.targetScroll = this.actualScroll;\n        this.emit();\n    }\n    emit() {\n        this.emitter.emit(\"scroll\", this);\n    }\n    reset() {\n        this.isLocked = false;\n        this.isScrolling = false;\n        this.animatedScroll = this.targetScroll = this.actualScroll;\n        this.lastVelocity = this.velocity = 0;\n        this.animate.stop();\n    }\n    /**\n   * Start lenis scroll after it has been stopped\n   */ start() {\n        if (!this.isStopped) return;\n        if (this.options.autoToggle) {\n            this.rootElement.style.removeProperty(\"overflow\");\n            return;\n        }\n        this.internalStart();\n    }\n    internalStart() {\n        if (!this.isStopped) return;\n        this.reset();\n        this.isStopped = false;\n        this.emit();\n    }\n    /**\n   * Stop lenis scroll\n   */ stop() {\n        if (this.isStopped) return;\n        if (this.options.autoToggle) {\n            this.rootElement.style.setProperty(\"overflow\", \"clip\");\n            return;\n        }\n        this.internalStop();\n    }\n    internalStop() {\n        if (this.isStopped) return;\n        this.reset();\n        this.isStopped = true;\n        this.emit();\n    }\n    /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */ scrollTo(target, { offset = 0, immediate = false, lock = false, duration = this.options.duration, easing = this.options.easing, lerp: lerp2 = this.options.lerp, onStart, onComplete, force = false, // scroll even if stopped\n    programmatic = true, // called from outside of the class\n    userData } = {}) {\n        if ((this.isStopped || this.isLocked) && !force) return;\n        if (typeof target === \"string\" && [\n            \"top\",\n            \"left\",\n            \"start\"\n        ].includes(target)) {\n            target = 0;\n        } else if (typeof target === \"string\" && [\n            \"bottom\",\n            \"right\",\n            \"end\"\n        ].includes(target)) {\n            target = this.limit;\n        } else {\n            let node;\n            if (typeof target === \"string\") {\n                node = document.querySelector(target);\n            } else if (target instanceof HTMLElement && target?.nodeType) {\n                node = target;\n            }\n            if (node) {\n                if (this.options.wrapper !== window) {\n                    const wrapperRect = this.rootElement.getBoundingClientRect();\n                    offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top;\n                }\n                const rect = node.getBoundingClientRect();\n                target = (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll;\n            }\n        }\n        if (typeof target !== \"number\") return;\n        target += offset;\n        target = Math.round(target);\n        if (this.options.infinite) {\n            if (programmatic) {\n                this.targetScroll = this.animatedScroll = this.scroll;\n                const distance = target - this.animatedScroll;\n                if (distance > this.limit / 2) {\n                    target = target - this.limit;\n                } else if (distance < -this.limit / 2) {\n                    target = target + this.limit;\n                }\n            }\n        } else {\n            target = clamp(0, target, this.limit);\n        }\n        if (target === this.targetScroll) {\n            onStart?.(this);\n            onComplete?.(this);\n            return;\n        }\n        this.userData = userData ?? {};\n        if (immediate) {\n            this.animatedScroll = this.targetScroll = target;\n            this.setScroll(this.scroll);\n            this.reset();\n            this.preventNextNativeScrollEvent();\n            this.emit();\n            onComplete?.(this);\n            this.userData = {};\n            requestAnimationFrame(()=>{\n                this.dispatchScrollendEvent();\n            });\n            return;\n        }\n        if (!programmatic) {\n            this.targetScroll = target;\n        }\n        if (typeof duration === \"number\" && typeof easing !== \"function\") {\n            easing = defaultEasing;\n        } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n            duration = 1;\n        }\n        this.animate.fromTo(this.animatedScroll, target, {\n            duration,\n            easing,\n            lerp: lerp2,\n            onStart: ()=>{\n                if (lock) this.isLocked = true;\n                this.isScrolling = \"smooth\";\n                onStart?.(this);\n            },\n            onUpdate: (value, completed)=>{\n                this.isScrolling = \"smooth\";\n                this.lastVelocity = this.velocity;\n                this.velocity = value - this.animatedScroll;\n                this.direction = Math.sign(this.velocity);\n                this.animatedScroll = value;\n                this.setScroll(this.scroll);\n                if (programmatic) {\n                    this.targetScroll = value;\n                }\n                if (!completed) this.emit();\n                if (completed) {\n                    this.reset();\n                    this.emit();\n                    onComplete?.(this);\n                    this.userData = {};\n                    requestAnimationFrame(()=>{\n                        this.dispatchScrollendEvent();\n                    });\n                    this.preventNextNativeScrollEvent();\n                }\n            }\n        });\n    }\n    preventNextNativeScrollEvent() {\n        this._preventNextNativeScrollEvent = true;\n        requestAnimationFrame(()=>{\n            this._preventNextNativeScrollEvent = false;\n        });\n    }\n    checkNestedScroll(node, { deltaX, deltaY }) {\n        const time = Date.now();\n        const cache = node._lenis ??= {};\n        let hasOverflowX, hasOverflowY, isScrollableX, isScrollableY, scrollWidth, scrollHeight, clientWidth, clientHeight;\n        const gestureOrientation = this.options.gestureOrientation;\n        if (time - (cache.time ?? 0) > 2e3) {\n            cache.time = Date.now();\n            const computedStyle = window.getComputedStyle(node);\n            cache.computedStyle = computedStyle;\n            const overflowXString = computedStyle.overflowX;\n            const overflowYString = computedStyle.overflowY;\n            hasOverflowX = [\n                \"auto\",\n                \"overlay\",\n                \"scroll\"\n            ].includes(overflowXString);\n            hasOverflowY = [\n                \"auto\",\n                \"overlay\",\n                \"scroll\"\n            ].includes(overflowYString);\n            cache.hasOverflowX = hasOverflowX;\n            cache.hasOverflowY = hasOverflowY;\n            if (!hasOverflowX && !hasOverflowY) return false;\n            if (gestureOrientation === \"vertical\" && !hasOverflowY) return false;\n            if (gestureOrientation === \"horizontal\" && !hasOverflowX) return false;\n            scrollWidth = node.scrollWidth;\n            scrollHeight = node.scrollHeight;\n            clientWidth = node.clientWidth;\n            clientHeight = node.clientHeight;\n            isScrollableX = scrollWidth > clientWidth;\n            isScrollableY = scrollHeight > clientHeight;\n            cache.isScrollableX = isScrollableX;\n            cache.isScrollableY = isScrollableY;\n            cache.scrollWidth = scrollWidth;\n            cache.scrollHeight = scrollHeight;\n            cache.clientWidth = clientWidth;\n            cache.clientHeight = clientHeight;\n        } else {\n            isScrollableX = cache.isScrollableX;\n            isScrollableY = cache.isScrollableY;\n            hasOverflowX = cache.hasOverflowX;\n            hasOverflowY = cache.hasOverflowY;\n            scrollWidth = cache.scrollWidth;\n            scrollHeight = cache.scrollHeight;\n            clientWidth = cache.clientWidth;\n            clientHeight = cache.clientHeight;\n        }\n        if (!hasOverflowX && !hasOverflowY || !isScrollableX && !isScrollableY) {\n            return false;\n        }\n        if (gestureOrientation === \"vertical\" && (!hasOverflowY || !isScrollableY)) return false;\n        if (gestureOrientation === \"horizontal\" && (!hasOverflowX || !isScrollableX)) return false;\n        let orientation;\n        if (gestureOrientation === \"horizontal\") {\n            orientation = \"x\";\n        } else if (gestureOrientation === \"vertical\") {\n            orientation = \"y\";\n        } else {\n            const isScrollingX = deltaX !== 0;\n            const isScrollingY = deltaY !== 0;\n            if (isScrollingX && hasOverflowX && isScrollableX) {\n                orientation = \"x\";\n            }\n            if (isScrollingY && hasOverflowY && isScrollableY) {\n                orientation = \"y\";\n            }\n        }\n        if (!orientation) return false;\n        let scroll, maxScroll, delta, hasOverflow, isScrollable;\n        if (orientation === \"x\") {\n            scroll = node.scrollLeft;\n            maxScroll = scrollWidth - clientWidth;\n            delta = deltaX;\n            hasOverflow = hasOverflowX;\n            isScrollable = isScrollableX;\n        } else if (orientation === \"y\") {\n            scroll = node.scrollTop;\n            maxScroll = scrollHeight - clientHeight;\n            delta = deltaY;\n            hasOverflow = hasOverflowY;\n            isScrollable = isScrollableY;\n        } else {\n            return false;\n        }\n        const willScroll = delta > 0 ? scroll < maxScroll : scroll > 0;\n        return willScroll && hasOverflow && isScrollable;\n    }\n    /**\n   * The root element on which lenis is instanced\n   */ get rootElement() {\n        return this.options.wrapper === window ? document.documentElement : this.options.wrapper;\n    }\n    /**\n   * The limit which is the maximum scroll value\n   */ get limit() {\n        if (this.options.__experimental__naiveDimensions) {\n            if (this.isHorizontal) {\n                return this.rootElement.scrollWidth - this.rootElement.clientWidth;\n            } else {\n                return this.rootElement.scrollHeight - this.rootElement.clientHeight;\n            }\n        } else {\n            return this.dimensions.limit[this.isHorizontal ? \"x\" : \"y\"];\n        }\n    }\n    /**\n   * Whether or not the scroll is horizontal\n   */ get isHorizontal() {\n        return this.options.orientation === \"horizontal\";\n    }\n    /**\n   * The actual scroll value\n   */ get actualScroll() {\n        const wrapper = this.options.wrapper;\n        return this.isHorizontal ? wrapper.scrollX ?? wrapper.scrollLeft : wrapper.scrollY ?? wrapper.scrollTop;\n    }\n    /**\n   * The current scroll value\n   */ get scroll() {\n        return this.options.infinite ? modulo(this.animatedScroll, this.limit) : this.animatedScroll;\n    }\n    /**\n   * The progress of the scroll relative to the limit\n   */ get progress() {\n        return this.limit === 0 ? 1 : this.scroll / this.limit;\n    }\n    /**\n   * Current scroll state\n   */ get isScrolling() {\n        return this._isScrolling;\n    }\n    set isScrolling(value) {\n        if (this._isScrolling !== value) {\n            this._isScrolling = value;\n            this.updateClassName();\n        }\n    }\n    /**\n   * Check if lenis is stopped\n   */ get isStopped() {\n        return this._isStopped;\n    }\n    set isStopped(value) {\n        if (this._isStopped !== value) {\n            this._isStopped = value;\n            this.updateClassName();\n        }\n    }\n    /**\n   * Check if lenis is locked\n   */ get isLocked() {\n        return this._isLocked;\n    }\n    set isLocked(value) {\n        if (this._isLocked !== value) {\n            this._isLocked = value;\n            this.updateClassName();\n        }\n    }\n    /**\n   * Check if lenis is smooth scrolling\n   */ get isSmooth() {\n        return this.isScrolling === \"smooth\";\n    }\n    /**\n   * The class name applied to the wrapper element\n   */ get className() {\n        let className = \"lenis\";\n        if (this.options.autoToggle) className += \" lenis-autoToggle\";\n        if (this.isStopped) className += \" lenis-stopped\";\n        if (this.isLocked) className += \" lenis-locked\";\n        if (this.isScrolling) className += \" lenis-scrolling\";\n        if (this.isScrolling === \"smooth\") className += \" lenis-smooth\";\n        return className;\n    }\n    updateClassName() {\n        this.cleanUpClassName();\n        this.rootElement.className = `${this.rootElement.className} ${this.className}`.trim();\n    }\n    cleanUpClassName() {\n        this.rootElement.className = this.rootElement.className.replace(/lenis(-\\w+)?/g, \"\").trim();\n    }\n};\n //# sourceMappingURL=lenis.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lenis/dist/lenis.mjs\n");

/***/ })

};
;