"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/Projects.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Projects.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Projects; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst projects = [\n    {\n        title: \"E-commerce Platform\",\n        description: \"Modern e-commerce solution with Next.js and Stripe integration\",\n        technologies: [\n            \"Next.js\",\n            \"TypeScript\",\n            \"Stripe\",\n            \"Tailwind CSS\"\n        ],\n        github: \"https://github.com\",\n        demo: \"https://demo.com\"\n    },\n    {\n        title: \"Task Management App\",\n        description: \"Collaborative task management with real-time updates\",\n        technologies: [\n            \"React\",\n            \"Node.js\",\n            \"Socket.io\",\n            \"MongoDB\"\n        ],\n        github: \"https://github.com\",\n        demo: \"https://demo.com\"\n    },\n    {\n        title: \"Weather Dashboard\",\n        description: \"Beautiful weather app with location-based forecasts\",\n        technologies: [\n            \"React\",\n            \"OpenWeather API\",\n            \"Chart.js\"\n        ],\n        github: \"https://github.com\",\n        demo: \"https://demo.com\"\n    },\n    {\n        title: \"Portfolio Website\",\n        description: \"Responsive portfolio with modern animations\",\n        technologies: [\n            \"Next.js\",\n            \"Framer Motion\",\n            \"Tailwind CSS\"\n        ],\n        github: \"https://github.com\",\n        demo: \"https://demo.com\"\n    }\n];\nfunction Projects() {\n    _s();\n    const ref = useRef(null);\n    const isInView = useInView(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const { scrollYProgress } = useScroll({\n        target: ref,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y = useTransform(scrollYProgress, [\n        0,\n        1\n    ], [\n        100,\n        -100\n    ]);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.15,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const projectVariants = {\n        hidden: {\n            opacity: 0,\n            y: 60,\n            scale: 0.9\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            scale: 1,\n            transition: {\n                type: \"spring\",\n                stiffness: 100,\n                damping: 15,\n                duration: 0.8\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        className: \"py-20 px-6 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                style: {\n                    y\n                },\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 right-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-7xl\",\n                ref: ref,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: isInView ? {\n                            opacity: 1,\n                            y: 0\n                        } : {\n                            opacity: 0,\n                            y: 50\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-16 reveal-text\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h2, {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                scale: 1\n                            } : {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            transition: {\n                                duration: 1,\n                                delay: 0.2\n                            },\n                            children: \"SELECTED PROJECTS\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                variants: projectVariants,\n                                className: \"group cursor-pointer project-card stagger-item\",\n                                onClick: ()=>window.open(project.url, \"_blank\"),\n                                whileHover: {\n                                    y: -10,\n                                    transition: {\n                                        duration: 0.3\n                                    }\n                                },\n                                whileTap: {\n                                    scale: 0.98\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    className: \"bg-gray-900/50 border border-gray-800 rounded-xl overflow-hidden hover:border-gray-700 transition-all duration-500 hover:bg-gray-800/50 h-full\",\n                                    whileHover: {\n                                        boxShadow: \"0 20px 40px rgba(0,0,0,0.4)\",\n                                        borderColor: \"rgba(156, 163, 175, 0.3)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden h-48\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center\",\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    transition: {\n                                                        duration: 0.4\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-lg\",\n                                                        children: \"Project Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"absolute inset-0 bg-black/60 flex items-center justify-center\",\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    whileHover: {\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n                                                                className: \"p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors backdrop-blur-sm\",\n                                                                whileHover: {\n                                                                    scale: 1.1,\n                                                                    rotate: 5\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.9\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    size: 20,\n                                                                    className: \"text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n                                                                className: \"p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors backdrop-blur-sm\",\n                                                                whileHover: {\n                                                                    scale: 1.1,\n                                                                    rotate: -5\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.9\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    size: 20,\n                                                                    className: \"text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"text-gray-400 text-sm mb-2\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.5 + index * 0.1\n                                                    },\n                                                    children: project.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h3, {\n                                                    className: \"text-xl font-semibold text-white mb-3\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.6 + index * 0.1\n                                                    },\n                                                    children: project.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                                                    className: \"text-gray-400 mb-4 text-sm leading-relaxed\",\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1\n                                                    } : {\n                                                        opacity: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.7 + index * 0.1\n                                                    },\n                                                    children: project.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: isInView ? {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    } : {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    transition: {\n                                                        delay: 0.8 + index * 0.1\n                                                    },\n                                                    children: project.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.span, {\n                                                            className: \"px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs\",\n                                                            initial: {\n                                                                opacity: 0,\n                                                                scale: 0.8\n                                                            },\n                                                            animate: isInView ? {\n                                                                opacity: 1,\n                                                                scale: 1\n                                                            } : {\n                                                                opacity: 0,\n                                                                scale: 0.8\n                                                            },\n                                                            transition: {\n                                                                delay: 0.9 + index * 0.1 + techIndex * 0.05\n                                                            },\n                                                            whileHover: {\n                                                                scale: 1.05,\n                                                                backgroundColor: \"rgba(75, 85, 99, 0.8)\"\n                                                            },\n                                                            children: tech\n                                                        }, tech, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            }, project.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cookies\\\\portfolio\\\\src\\\\components\\\\sections\\\\Projects.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(Projects, \"XxgFw8CBTTHdAlcYdnmjc4Xe94E=\", true);\n_c = Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL1Byb2plY3RzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXNDO0FBQ2E7QUFFbkQsTUFBTUcsV0FBVztJQUNmO1FBQ0VDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxjQUFjO1lBQUM7WUFBVztZQUFjO1lBQVU7U0FBZTtRQUNqRUMsUUFBUTtRQUNSQyxNQUFNO0lBQ1I7SUFDQTtRQUNFSixPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsY0FBYztZQUFDO1lBQVM7WUFBVztZQUFhO1NBQVU7UUFDMURDLFFBQVE7UUFDUkMsTUFBTTtJQUNSO0lBQ0E7UUFDRUosT0FBTztRQUNQQyxhQUFhO1FBQ2JDLGNBQWM7WUFBQztZQUFTO1lBQW1CO1NBQVc7UUFDdERDLFFBQVE7UUFDUkMsTUFBTTtJQUNSO0lBQ0E7UUFDRUosT0FBTztRQUNQQyxhQUFhO1FBQ2JDLGNBQWM7WUFBQztZQUFXO1lBQWlCO1NBQWU7UUFDMURDLFFBQVE7UUFDUkMsTUFBTTtJQUNSO0NBQ0Q7QUFFYyxTQUFTQzs7SUFDdEIsTUFBTUMsTUFBTUMsT0FBTztJQUNuQixNQUFNQyxXQUFXQyxVQUFVSCxLQUFLO1FBQUVJLE1BQU07UUFBTUMsUUFBUTtJQUFTO0lBQy9ELE1BQU0sRUFBRUMsZUFBZSxFQUFFLEdBQUdDLFVBQVU7UUFDcENDLFFBQVFSO1FBQ1JTLFFBQVE7WUFBQztZQUFhO1NBQVk7SUFDcEM7SUFFQSxNQUFNQyxJQUFJQyxhQUFhTCxpQkFBaUI7UUFBQztRQUFHO0tBQUUsRUFBRTtRQUFDO1FBQUssQ0FBQztLQUFJO0lBRTNELE1BQU1NLG9CQUFvQjtRQUN4QkMsUUFBUTtZQUFFQyxTQUFTO1FBQUU7UUFDckJDLFNBQVM7WUFDUEQsU0FBUztZQUNURSxZQUFZO2dCQUNWQyxpQkFBaUI7Z0JBQ2pCQyxlQUFlO1lBQ2pCO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLGtCQUFrQjtRQUN0Qk4sUUFBUTtZQUNOQyxTQUFTO1lBQ1RKLEdBQUc7WUFDSFUsT0FBTztRQUNUO1FBQ0FMLFNBQVM7WUFDUEQsU0FBUztZQUNUSixHQUFHO1lBQ0hVLE9BQU87WUFDUEosWUFBWTtnQkFDVkssTUFBTTtnQkFDTkMsV0FBVztnQkFDWEMsU0FBUztnQkFDVEMsVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDQztRQUFRQyxJQUFHO1FBQVdDLFdBQVU7OzBCQUUvQiw4REFBQ3JDLGlEQUFNQSxDQUFDc0MsR0FBRztnQkFDVEMsT0FBTztvQkFBRW5CO2dCQUFFO2dCQUNYaUIsV0FBVTs7a0NBRVYsOERBQUNDO3dCQUFJRCxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7MEJBR2pCLDhEQUFDQztnQkFBSUQsV0FBVTtnQkFBOEIzQixLQUFLQTs7a0NBQ2hELDhEQUFDVixpREFBTUEsQ0FBQ3NDLEdBQUc7d0JBQ1RFLFNBQVM7NEJBQUVoQixTQUFTOzRCQUFHSixHQUFHO3dCQUFHO3dCQUM3QnFCLFNBQVM3QixXQUFXOzRCQUFFWSxTQUFTOzRCQUFHSixHQUFHO3dCQUFFLElBQUk7NEJBQUVJLFNBQVM7NEJBQUdKLEdBQUc7d0JBQUc7d0JBQy9ETSxZQUFZOzRCQUFFUSxVQUFVO3dCQUFJO3dCQUM1QkcsV0FBVTtrQ0FFViw0RUFBQ3JDLGlEQUFNQSxDQUFDMEMsRUFBRTs0QkFDUkwsV0FBVTs0QkFDVkcsU0FBUztnQ0FBRWhCLFNBQVM7Z0NBQUdNLE9BQU87NEJBQUk7NEJBQ2xDVyxTQUFTN0IsV0FBVztnQ0FBRVksU0FBUztnQ0FBR00sT0FBTzs0QkFBRSxJQUFJO2dDQUFFTixTQUFTO2dDQUFHTSxPQUFPOzRCQUFJOzRCQUN4RUosWUFBWTtnQ0FBRVEsVUFBVTtnQ0FBR1MsT0FBTzs0QkFBSTtzQ0FDdkM7Ozs7Ozs7Ozs7O2tDQUtILDhEQUFDM0MsaURBQU1BLENBQUNzQyxHQUFHO3dCQUNUTSxVQUFVdEI7d0JBQ1ZrQixTQUFRO3dCQUNSQyxTQUFTN0IsV0FBVyxZQUFZO3dCQUNoQ3lCLFdBQVU7a0NBRVRsQyxTQUFTMEMsR0FBRyxDQUFDLENBQUNDLFNBQVNDLHNCQUN0Qiw4REFBQy9DLGlEQUFNQSxDQUFDc0MsR0FBRztnQ0FFVE0sVUFBVWY7Z0NBQ1ZRLFdBQVU7Z0NBQ1ZXLFNBQVMsSUFBTUMsT0FBT0MsSUFBSSxDQUFDSixRQUFRSyxHQUFHLEVBQUU7Z0NBQ3hDQyxZQUFZO29DQUNWaEMsR0FBRyxDQUFDO29DQUNKTSxZQUFZO3dDQUFFUSxVQUFVO29DQUFJO2dDQUM5QjtnQ0FDQW1CLFVBQVU7b0NBQUV2QixPQUFPO2dDQUFLOzBDQUV4Qiw0RUFBQzlCLGlEQUFNQSxDQUFDc0MsR0FBRztvQ0FDVEQsV0FBVTtvQ0FDVmUsWUFBWTt3Q0FDVkUsV0FBVzt3Q0FDWEMsYUFBYTtvQ0FDZjs7c0RBR0EsOERBQUNqQjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNyQyxpREFBTUEsQ0FBQ3NDLEdBQUc7b0RBQ1RELFdBQVU7b0RBQ1ZlLFlBQVk7d0RBQUV0QixPQUFPO29EQUFLO29EQUMxQkosWUFBWTt3REFBRVEsVUFBVTtvREFBSTs4REFFNUIsNEVBQUNzQjt3REFBS25CLFdBQVU7a0VBQXdCOzs7Ozs7Ozs7Ozs4REFJMUMsOERBQUNyQyxpREFBTUEsQ0FBQ3NDLEdBQUc7b0RBQ1RELFdBQVU7b0RBQ1ZHLFNBQVM7d0RBQUVoQixTQUFTO29EQUFFO29EQUN0QjRCLFlBQVk7d0RBQUU1QixTQUFTO29EQUFFO29EQUN6QkUsWUFBWTt3REFBRVEsVUFBVTtvREFBSTs4REFFNUIsNEVBQUNJO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ3JDLGlEQUFNQSxDQUFDeUQsTUFBTTtnRUFDWnBCLFdBQVU7Z0VBQ1ZlLFlBQVk7b0VBQUV0QixPQUFPO29FQUFLNEIsUUFBUTtnRUFBRTtnRUFDcENMLFVBQVU7b0VBQUV2QixPQUFPO2dFQUFJOzBFQUV2Qiw0RUFBQzdCLCtGQUFZQTtvRUFBQzBELE1BQU07b0VBQUl0QixXQUFVOzs7Ozs7Ozs7OzswRUFFcEMsOERBQUNyQyxpREFBTUEsQ0FBQ3lELE1BQU07Z0VBQ1pwQixXQUFVO2dFQUNWZSxZQUFZO29FQUFFdEIsT0FBTztvRUFBSzRCLFFBQVEsQ0FBQztnRUFBRTtnRUFDckNMLFVBQVU7b0VBQUV2QixPQUFPO2dFQUFJOzBFQUV2Qiw0RUFBQzVCLCtGQUFNQTtvRUFBQ3lELE1BQU07b0VBQUl0QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU9wQyw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDckMsaURBQU1BLENBQUNzQyxHQUFHO29EQUNURCxXQUFVO29EQUNWRyxTQUFTO3dEQUFFaEIsU0FBUzt3REFBR29DLEdBQUcsQ0FBQztvREFBRztvREFDOUJuQixTQUFTN0IsV0FBVzt3REFBRVksU0FBUzt3REFBR29DLEdBQUc7b0RBQUUsSUFBSTt3REFBRXBDLFNBQVM7d0RBQUdvQyxHQUFHLENBQUM7b0RBQUc7b0RBQ2hFbEMsWUFBWTt3REFBRWlCLE9BQU8sTUFBTUksUUFBUTtvREFBSTs4REFFdENELFFBQVFWLEVBQUU7Ozs7Ozs4REFHYiw4REFBQ3BDLGlEQUFNQSxDQUFDNkQsRUFBRTtvREFDUnhCLFdBQVU7b0RBQ1ZHLFNBQVM7d0RBQUVoQixTQUFTO3dEQUFHSixHQUFHO29EQUFHO29EQUM3QnFCLFNBQVM3QixXQUFXO3dEQUFFWSxTQUFTO3dEQUFHSixHQUFHO29EQUFFLElBQUk7d0RBQUVJLFNBQVM7d0RBQUdKLEdBQUc7b0RBQUc7b0RBQy9ETSxZQUFZO3dEQUFFaUIsT0FBTyxNQUFNSSxRQUFRO29EQUFJOzhEQUV0Q0QsUUFBUTFDLEtBQUs7Ozs7Ozs4REFHaEIsOERBQUNKLGlEQUFNQSxDQUFDOEQsQ0FBQztvREFDUHpCLFdBQVU7b0RBQ1ZHLFNBQVM7d0RBQUVoQixTQUFTO29EQUFFO29EQUN0QmlCLFNBQVM3QixXQUFXO3dEQUFFWSxTQUFTO29EQUFFLElBQUk7d0RBQUVBLFNBQVM7b0RBQUU7b0RBQ2xERSxZQUFZO3dEQUFFaUIsT0FBTyxNQUFNSSxRQUFRO29EQUFJOzhEQUV0Q0QsUUFBUXpDLFdBQVc7Ozs7Ozs4REFHdEIsOERBQUNMLGlEQUFNQSxDQUFDc0MsR0FBRztvREFDVEQsV0FBVTtvREFDVkcsU0FBUzt3REFBRWhCLFNBQVM7d0RBQUdKLEdBQUc7b0RBQUc7b0RBQzdCcUIsU0FBUzdCLFdBQVc7d0RBQUVZLFNBQVM7d0RBQUdKLEdBQUc7b0RBQUUsSUFBSTt3REFBRUksU0FBUzt3REFBR0osR0FBRztvREFBRztvREFDL0RNLFlBQVk7d0RBQUVpQixPQUFPLE1BQU1JLFFBQVE7b0RBQUk7OERBRXRDRCxRQUFReEMsWUFBWSxDQUFDdUMsR0FBRyxDQUFDLENBQUNrQixNQUFNQywwQkFDL0IsOERBQUNoRSxpREFBTUEsQ0FBQ3dELElBQUk7NERBRVZuQixXQUFVOzREQUNWRyxTQUFTO2dFQUFFaEIsU0FBUztnRUFBR00sT0FBTzs0REFBSTs0REFDbENXLFNBQVM3QixXQUFXO2dFQUFFWSxTQUFTO2dFQUFHTSxPQUFPOzREQUFFLElBQUk7Z0VBQUVOLFNBQVM7Z0VBQUdNLE9BQU87NERBQUk7NERBQ3hFSixZQUFZO2dFQUFFaUIsT0FBTyxNQUFNSSxRQUFRLE1BQU1pQixZQUFZOzREQUFLOzREQUMxRFosWUFBWTtnRUFBRXRCLE9BQU87Z0VBQU1tQyxpQkFBaUI7NERBQXdCO3NFQUVuRUY7MkRBUElBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQTFGVmpCLFFBQVFWLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUE2RzdCO0dBekx3QjNCO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL1Byb2plY3RzLnRzeD9lZjlmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IHsgRXh0ZXJuYWxMaW5rLCBHaXRodWIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmNvbnN0IHByb2plY3RzID0gW1xuICB7XG4gICAgdGl0bGU6IFwiRS1jb21tZXJjZSBQbGF0Zm9ybVwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIk1vZGVybiBlLWNvbW1lcmNlIHNvbHV0aW9uIHdpdGggTmV4dC5qcyBhbmQgU3RyaXBlIGludGVncmF0aW9uXCIsXG4gICAgdGVjaG5vbG9naWVzOiBbXCJOZXh0LmpzXCIsIFwiVHlwZVNjcmlwdFwiLCBcIlN0cmlwZVwiLCBcIlRhaWx3aW5kIENTU1wiXSxcbiAgICBnaXRodWI6IFwiaHR0cHM6Ly9naXRodWIuY29tXCIsXG4gICAgZGVtbzogXCJodHRwczovL2RlbW8uY29tXCJcbiAgfSxcbiAge1xuICAgIHRpdGxlOiBcIlRhc2sgTWFuYWdlbWVudCBBcHBcIixcbiAgICBkZXNjcmlwdGlvbjogXCJDb2xsYWJvcmF0aXZlIHRhc2sgbWFuYWdlbWVudCB3aXRoIHJlYWwtdGltZSB1cGRhdGVzXCIsXG4gICAgdGVjaG5vbG9naWVzOiBbXCJSZWFjdFwiLCBcIk5vZGUuanNcIiwgXCJTb2NrZXQuaW9cIiwgXCJNb25nb0RCXCJdLFxuICAgIGdpdGh1YjogXCJodHRwczovL2dpdGh1Yi5jb21cIixcbiAgICBkZW1vOiBcImh0dHBzOi8vZGVtby5jb21cIlxuICB9LFxuICB7XG4gICAgdGl0bGU6IFwiV2VhdGhlciBEYXNoYm9hcmRcIixcbiAgICBkZXNjcmlwdGlvbjogXCJCZWF1dGlmdWwgd2VhdGhlciBhcHAgd2l0aCBsb2NhdGlvbi1iYXNlZCBmb3JlY2FzdHNcIixcbiAgICB0ZWNobm9sb2dpZXM6IFtcIlJlYWN0XCIsIFwiT3BlbldlYXRoZXIgQVBJXCIsIFwiQ2hhcnQuanNcIl0sXG4gICAgZ2l0aHViOiBcImh0dHBzOi8vZ2l0aHViLmNvbVwiLFxuICAgIGRlbW86IFwiaHR0cHM6Ly9kZW1vLmNvbVwiXG4gIH0sXG4gIHtcbiAgICB0aXRsZTogXCJQb3J0Zm9saW8gV2Vic2l0ZVwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIlJlc3BvbnNpdmUgcG9ydGZvbGlvIHdpdGggbW9kZXJuIGFuaW1hdGlvbnNcIixcbiAgICB0ZWNobm9sb2dpZXM6IFtcIk5leHQuanNcIiwgXCJGcmFtZXIgTW90aW9uXCIsIFwiVGFpbHdpbmQgQ1NTXCJdLFxuICAgIGdpdGh1YjogXCJodHRwczovL2dpdGh1Yi5jb21cIixcbiAgICBkZW1vOiBcImh0dHBzOi8vZGVtby5jb21cIlxuICB9XG5dXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2plY3RzKCkge1xuICBjb25zdCByZWYgPSB1c2VSZWYobnVsbClcbiAgY29uc3QgaXNJblZpZXcgPSB1c2VJblZpZXcocmVmLCB7IG9uY2U6IHRydWUsIG1hcmdpbjogXCItMTAwcHhcIiB9KVxuICBjb25zdCB7IHNjcm9sbFlQcm9ncmVzcyB9ID0gdXNlU2Nyb2xsKHtcbiAgICB0YXJnZXQ6IHJlZixcbiAgICBvZmZzZXQ6IFtcInN0YXJ0IGVuZFwiLCBcImVuZCBzdGFydFwiXVxuICB9KVxuXG4gIGNvbnN0IHkgPSB1c2VUcmFuc2Zvcm0oc2Nyb2xsWVByb2dyZXNzLCBbMCwgMV0sIFsxMDAsIC0xMDBdKVxuXG4gIGNvbnN0IGNvbnRhaW5lclZhcmlhbnRzID0ge1xuICAgIGhpZGRlbjogeyBvcGFjaXR5OiAwIH0sXG4gICAgdmlzaWJsZToge1xuICAgICAgb3BhY2l0eTogMSxcbiAgICAgIHRyYW5zaXRpb246IHtcbiAgICAgICAgc3RhZ2dlckNoaWxkcmVuOiAwLjE1LFxuICAgICAgICBkZWxheUNoaWxkcmVuOiAwLjNcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBjb25zdCBwcm9qZWN0VmFyaWFudHMgPSB7XG4gICAgaGlkZGVuOiB7XG4gICAgICBvcGFjaXR5OiAwLFxuICAgICAgeTogNjAsXG4gICAgICBzY2FsZTogMC45XG4gICAgfSxcbiAgICB2aXNpYmxlOiB7XG4gICAgICBvcGFjaXR5OiAxLFxuICAgICAgeTogMCxcbiAgICAgIHNjYWxlOiAxLFxuICAgICAgdHJhbnNpdGlvbjoge1xuICAgICAgICB0eXBlOiBcInNwcmluZ1wiLFxuICAgICAgICBzdGlmZm5lc3M6IDEwMCxcbiAgICAgICAgZGFtcGluZzogMTUsXG4gICAgICAgIGR1cmF0aW9uOiAwLjhcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGlkPVwicHJvamVjdHNcIiBjbGFzc05hbWU9XCJweS0yMCBweC02IHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgey8qIEJhY2tncm91bmQgZWxlbWVudHMgKi99XG4gICAgICA8bW90aW9uLmRpdlxuICAgICAgICBzdHlsZT17eyB5IH19XG4gICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcG9pbnRlci1ldmVudHMtbm9uZVwiXG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQwIGxlZnQtMjAgdy02NCBoLTY0IGJnLWJsdWUtNTAwLzUgcm91bmRlZC1mdWxsIGJsdXItM3hsXCIgLz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tNDAgcmlnaHQtMjAgdy04MCBoLTgwIGJnLXB1cnBsZS01MDAvNSByb3VuZGVkLWZ1bGwgYmx1ci0zeGxcIiAvPlxuICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIG1heC13LTd4bFwiIHJlZj17cmVmfT5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDUwIH19XG4gICAgICAgICAgYW5pbWF0ZT17aXNJblZpZXcgPyB7IG9wYWNpdHk6IDEsIHk6IDAgfSA6IHsgb3BhY2l0eTogMCwgeTogNTAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xNiByZXZlYWwtdGV4dFwiXG4gICAgICAgID5cbiAgICAgICAgICA8bW90aW9uLmgyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtZDp0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17aXNJblZpZXcgPyB7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH0gOiB7IG9wYWNpdHk6IDAsIHNjYWxlOiAwLjggfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEsIGRlbGF5OiAwLjIgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBTRUxFQ1RFRCBQUk9KRUNUU1xuICAgICAgICAgIDwvbW90aW9uLmgyPlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICB2YXJpYW50cz17Y29udGFpbmVyVmFyaWFudHN9XG4gICAgICAgICAgaW5pdGlhbD1cImhpZGRlblwiXG4gICAgICAgICAgYW5pbWF0ZT17aXNJblZpZXcgPyBcInZpc2libGVcIiA6IFwiaGlkZGVuXCJ9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOFwiXG4gICAgICAgID5cbiAgICAgICAgICB7cHJvamVjdHMubWFwKChwcm9qZWN0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAga2V5PXtwcm9qZWN0LmlkfVxuICAgICAgICAgICAgICB2YXJpYW50cz17cHJvamVjdFZhcmlhbnRzfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCBjdXJzb3ItcG9pbnRlciBwcm9qZWN0LWNhcmQgc3RhZ2dlci1pdGVtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4ocHJvamVjdC51cmwsICdfYmxhbmsnKX1cbiAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17e1xuICAgICAgICAgICAgICAgIHk6IC0xMCxcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiB7IGR1cmF0aW9uOiAwLjMgfVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45OCB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktOTAwLzUwIGJvcmRlciBib3JkZXItZ3JheS04MDAgcm91bmRlZC14bCBvdmVyZmxvdy1oaWRkZW4gaG92ZXI6Ym9yZGVyLWdyYXktNzAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBob3ZlcjpiZy1ncmF5LTgwMC81MCBoLWZ1bGxcIlxuICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3tcbiAgICAgICAgICAgICAgICAgIGJveFNoYWRvdzogXCIwIDIwcHggNDBweCByZ2JhKDAsMCwwLDAuNClcIixcbiAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBcInJnYmEoMTU2LCAxNjMsIDE3NSwgMC4zKVwiXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHsvKiBQcm9qZWN0IEltYWdlICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuIGgtNDhcIj5cbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTgwMCB0by1ncmF5LTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDUgfX1cbiAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC40IH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1sZ1wiPlByb2plY3QgSW1hZ2U8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBIb3ZlciBvdmVybGF5ICovfVxuICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ibGFjay82MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0zIGJnLXdoaXRlLzIwIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy13aGl0ZS8zMCB0cmFuc2l0aW9uLWNvbG9ycyBiYWNrZHJvcC1ibHVyLXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMSwgcm90YXRlOiA1IH19XG4gICAgICAgICAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45IH19XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEV4dGVybmFsTGluayBzaXplPXsyMH0gY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTMgYmctd2hpdGUvMjAgcm91bmRlZC1mdWxsIGhvdmVyOmJnLXdoaXRlLzMwIHRyYW5zaXRpb24tY29sb3JzIGJhY2tkcm9wLWJsdXItc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4xLCByb3RhdGU6IC01IH19XG4gICAgICAgICAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45IH19XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEdpdGh1YiBzaXplPXsyMH0gY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBQcm9qZWN0IEluZm8gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbSBtYi0yXCJcbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17aXNJblZpZXcgPyB7IG9wYWNpdHk6IDEsIHg6IDAgfSA6IHsgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuNSArIGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtwcm9qZWN0LmlkfVxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8bW90aW9uLmgzXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTNcIlxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e2lzSW5WaWV3ID8geyBvcGFjaXR5OiAxLCB5OiAwIH0gOiB7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuNiArIGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtwcm9qZWN0LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uaDM+XG5cbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24ucFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1iLTQgdGV4dC1zbSBsZWFkaW5nLXJlbGF4ZWRcIlxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17aXNJblZpZXcgPyB7IG9wYWNpdHk6IDEgfSA6IHsgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAwLjcgKyBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7cHJvamVjdC5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgIDwvbW90aW9uLnA+XG5cbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCJcbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXtpc0luVmlldyA/IHsgb3BhY2l0eTogMSwgeTogMCB9IDogeyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAwLjggKyBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7cHJvamVjdC50ZWNobm9sb2dpZXMubWFwKCh0ZWNoLCB0ZWNoSW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8bW90aW9uLnNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17dGVjaH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTgwMCB0ZXh0LWdyYXktMzAwIHJvdW5kZWQgdGV4dC14c1wiXG4gICAgICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHNjYWxlOiAwLjggfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e2lzSW5WaWV3ID8geyBvcGFjaXR5OiAxLCBzY2FsZTogMSB9IDogeyBvcGFjaXR5OiAwLCBzY2FsZTogMC44IH19XG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAwLjkgKyBpbmRleCAqIDAuMSArIHRlY2hJbmRleCAqIDAuMDUgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDUsIGJhY2tncm91bmRDb2xvcjogXCJyZ2JhKDc1LCA4NSwgOTksIDAuOClcIiB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt0ZWNofVxuICAgICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLnNwYW4+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1vdGlvbiIsIkV4dGVybmFsTGluayIsIkdpdGh1YiIsInByb2plY3RzIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInRlY2hub2xvZ2llcyIsImdpdGh1YiIsImRlbW8iLCJQcm9qZWN0cyIsInJlZiIsInVzZVJlZiIsImlzSW5WaWV3IiwidXNlSW5WaWV3Iiwib25jZSIsIm1hcmdpbiIsInNjcm9sbFlQcm9ncmVzcyIsInVzZVNjcm9sbCIsInRhcmdldCIsIm9mZnNldCIsInkiLCJ1c2VUcmFuc2Zvcm0iLCJjb250YWluZXJWYXJpYW50cyIsImhpZGRlbiIsIm9wYWNpdHkiLCJ2aXNpYmxlIiwidHJhbnNpdGlvbiIsInN0YWdnZXJDaGlsZHJlbiIsImRlbGF5Q2hpbGRyZW4iLCJwcm9qZWN0VmFyaWFudHMiLCJzY2FsZSIsInR5cGUiLCJzdGlmZm5lc3MiLCJkYW1waW5nIiwiZHVyYXRpb24iLCJzZWN0aW9uIiwiaWQiLCJjbGFzc05hbWUiLCJkaXYiLCJzdHlsZSIsImluaXRpYWwiLCJhbmltYXRlIiwiaDIiLCJkZWxheSIsInZhcmlhbnRzIiwibWFwIiwicHJvamVjdCIsImluZGV4Iiwib25DbGljayIsIndpbmRvdyIsIm9wZW4iLCJ1cmwiLCJ3aGlsZUhvdmVyIiwid2hpbGVUYXAiLCJib3hTaGFkb3ciLCJib3JkZXJDb2xvciIsInNwYW4iLCJidXR0b24iLCJyb3RhdGUiLCJzaXplIiwieCIsImgzIiwicCIsInRlY2giLCJ0ZWNoSW5kZXgiLCJiYWNrZ3JvdW5kQ29sb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Projects.tsx\n"));

/***/ })

});